# ===================================================================
# Lighthouse (Knowledge Discovery Platform) - Environment Configuration
# ===================================================================
# Lighthouse is a TanStack Start application for research, knowledge
# discovery, and document management with AI-powered insights.

# ===================================================================
# Application Configuration
# ===================================================================
VITE_APP_TITLE=Lighthouse Research Platform
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=development

# ===================================================================
# API Configuration
# ===================================================================
# Main backend API endpoint (Command-Center)
VITE_API_BASE_URL=http://localhost:3000/api
VITE_COMMAND_CENTER_URL=http://localhost:3000

# Python document processing service
VITE_PYTHON_SERVICE_URL=http://localhost:8001

# Knowledge base and search endpoints
VITE_SEARCH_API_URL=http://localhost:3000/api/search
VITE_DOCUMENTS_API_URL=http://localhost:3000/api/documents
VITE_RESEARCH_API_URL=http://localhost:3000/api/research

# API settings
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RATE_LIMIT=100

# ===================================================================
# Authentication Configuration
# ===================================================================
# JWT and session management
VITE_AUTH_TOKEN_KEY=lighthouse_auth_token
VITE_SESSION_TIMEOUT=60
VITE_ENABLE_SSO=true

# Auth endpoints
VITE_AUTH_LOGIN_URL=/auth/login
VITE_AUTH_LOGOUT_URL=/auth/logout
VITE_AUTH_REFRESH_URL=/auth/refresh

# ===================================================================
# Search and Knowledge Discovery
# ===================================================================
# Search configuration
VITE_ENABLE_SEMANTIC_SEARCH=true
VITE_SEARCH_RESULTS_PER_PAGE=20
VITE_MAX_SEARCH_HISTORY=50
VITE_SEARCH_DEBOUNCE_MS=300

# AI-powered features
VITE_ENABLE_AI_INSIGHTS=true
VITE_ENABLE_AUTO_TAGGING=true
VITE_ENABLE_CONTENT_SUMMARIZATION=true
VITE_ENABLE_RELATED_CONTENT=true

# Vector search
VITE_ENABLE_VECTOR_SEARCH=true
VITE_VECTOR_SIMILARITY_THRESHOLD=0.7
VITE_MAX_VECTOR_RESULTS=100

# ===================================================================
# Document Management
# ===================================================================
# File handling
VITE_MAX_FILE_SIZE=104857600
VITE_ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md,html,ppt,pptx,xls,xlsx

# Document processing
VITE_ENABLE_OCR=true
VITE_ENABLE_TEXT_EXTRACTION=true
VITE_ENABLE_METADATA_EXTRACTION=true
VITE_ENABLE_THUMBNAIL_GENERATION=true

# Document organization
VITE_MAX_FOLDERS_DEPTH=10
VITE_MAX_TAGS_PER_DOCUMENT=20
VITE_ENABLE_AUTO_CATEGORIZATION=true

# ===================================================================
# Research Features
# ===================================================================
# Research tools
VITE_ENABLE_RESEARCH_PROJECTS=true
VITE_ENABLE_CITATION_MANAGEMENT=true
VITE_ENABLE_ANNOTATION_TOOLS=true
VITE_ENABLE_COLLABORATION=true

# Data visualization
VITE_ENABLE_CHARTS=true
VITE_ENABLE_KNOWLEDGE_GRAPHS=true
VITE_ENABLE_NETWORK_VISUALIZATION=true

# Export features
VITE_ENABLE_EXPORT=true
VITE_EXPORT_FORMATS=pdf,docx,md,json,csv

# ===================================================================
# AI Integration
# ===================================================================
# AI services
VITE_ENABLE_AI_CHAT=true
VITE_AI_MODEL_PROVIDER=openai
VITE_AI_RESPONSE_STREAMING=true

# Content analysis
VITE_ENABLE_SENTIMENT_ANALYSIS=true
VITE_ENABLE_ENTITY_EXTRACTION=true
VITE_ENABLE_TOPIC_MODELING=true

# Recommendations
VITE_ENABLE_CONTENT_RECOMMENDATIONS=true
VITE_RECOMMENDATION_ALGORITHM=collaborative
VITE_MAX_RECOMMENDATIONS=10

# ===================================================================
# UI/UX Configuration
# ===================================================================
# Theme and appearance
VITE_DEFAULT_THEME=light
VITE_ENABLE_THEME_SWITCHING=true
VITE_ENABLE_DARK_MODE=true

# Layout preferences
VITE_DEFAULT_LAYOUT=grid
VITE_ENABLE_LAYOUT_SWITCHING=true
VITE_SIDEBAR_DEFAULT_COLLAPSED=false

# User interface
VITE_ENABLE_COMMAND_PALETTE=true
VITE_ENABLE_KEYBOARD_SHORTCUTS=true
VITE_ENABLE_QUICK_ACTIONS=true

# ===================================================================
# Performance Configuration
# ===================================================================
# Caching
VITE_CACHE_STRATEGY=memory
VITE_CACHE_DURATION=3600000
VITE_ENABLE_OFFLINE_MODE=true

# Pagination and loading
VITE_ITEMS_PER_PAGE=50
VITE_ENABLE_INFINITE_SCROLL=true
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_VIRTUAL_SCROLLING=true

# Background processing
VITE_ENABLE_BACKGROUND_INDEXING=true
VITE_INDEXING_BATCH_SIZE=100
VITE_INDEXING_INTERVAL=60000

# ===================================================================
# Analytics and Tracking
# ===================================================================
# User analytics
VITE_ENABLE_ANALYTICS=true
VITE_ANALYTICS_PROVIDER=google
VITE_TRACK_USER_BEHAVIOR=true

# Research analytics
VITE_ENABLE_RESEARCH_ANALYTICS=true
VITE_TRACK_SEARCH_QUERIES=true
VITE_TRACK_DOCUMENT_USAGE=true

# Performance monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_TRACKING=true

# ===================================================================
# Security Configuration
# ===================================================================
# Data protection
VITE_ENABLE_DATA_ENCRYPTION=true
VITE_ENABLE_SECURE_STORAGE=true
VITE_ENABLE_CSP=true

# Access control
VITE_ENABLE_ROLE_BASED_ACCESS=true
VITE_ENABLE_DOCUMENT_PERMISSIONS=true
VITE_ENABLE_AUDIT_LOGGING=true

# Privacy
VITE_ENABLE_PRIVACY_MODE=true
VITE_ANONYMIZE_USER_DATA=false

# ===================================================================
# External Services
# ===================================================================
# Error tracking
VITE_SENTRY_DSN=

# Analytics services
VITE_GOOGLE_ANALYTICS_ID=
VITE_MIXPANEL_TOKEN=

# Support and feedback
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_FEEDBACK_URL=
VITE_DOCUMENTATION_URL=

# ===================================================================
# Cross-App Integration
# ===================================================================
# Integration with other Luminar applications
VITE_AMNA_URL=http://localhost:5173
VITE_ECONNECT_URL=http://localhost:5174
VITE_DASHBOARD_URL=http://localhost:5176
VITE_TRAINING_URL=http://localhost:5177
VITE_VENDORS_URL=http://localhost:5178
VITE_WINS_URL=http://localhost:5179

# Shared services
VITE_SHARED_AUTH_ENABLED=true
VITE_SHARED_AUTH_DOMAIN=localhost
VITE_CROSS_APP_NAVIGATION=true
VITE_SHARED_SEARCH_INDEX=true

# ===================================================================
# Development Configuration
# ===================================================================
# Development server
VITE_DEV_PORT=5175
VITE_HMR_PORT=5175

# Development tools
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_ROUTER_DEVTOOLS=true
VITE_ENABLE_QUERY_DEVTOOLS=true

# Debug settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
VITE_ENABLE_VERBOSE_LOGGING=false

# ===================================================================
# Testing Configuration
# ===================================================================
# Test environment
VITE_TEST_MODE=false
VITE_ENABLE_MSW=false
VITE_TEST_API_URL=http://localhost:3001/api

# Test data
VITE_USE_MOCK_DATA=false
VITE_MOCK_DOCUMENTS_COUNT=100
VITE_MOCK_USERS_COUNT=10

# ===================================================================
# Build Configuration
# ===================================================================
# Build settings
VITE_BUILD_OUTPUT_DIR=dist
VITE_PUBLIC_PATH=/

# Server-side rendering
VITE_ENABLE_SSR=true
VITE_SSR_PORT=3000

# Optimization
VITE_ENABLE_MINIFICATION=true
VITE_ENABLE_COMPRESSION=true
VITE_GENERATE_SOURCE_MAPS=true

# Progressive Web App
VITE_ENABLE_PWA=true
VITE_PWA_CACHE_STRATEGY=stale-while-revalidate

# Bundle analysis
VITE_BUNDLE_ANALYZER=false
VITE_CHUNK_SIZE_WARNING_LIMIT=500