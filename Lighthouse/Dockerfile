# Multi-stage Dockerfile for Lighthouse Frontend
# Based on TanStack Start framework

# Stage 1: Dependencies
FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# Install dependencies (support both npm and yarn)
RUN if [ -f yarn.lock ]; then \
      yarn install --frozen-lockfile; \
    else \
      npm ci; \
    fi

# Stage 2: Builder
FROM node:20-alpine AS builder
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy dependencies from previous stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
ENV NODE_ENV=production
RUN if [ -f yarn.lock ]; then \
      yarn build; \
    else \
      npm run build; \
    fi

# Stage 3: Runner (Production)
FROM node:20-alpine AS runner
RUN apk add --no-cache libc6-compat wget

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tanstack -u 1001

WORKDIR /app

# Copy necessary files
COPY --from=builder --chown=tanstack:nodejs /app/.vinxi ./dist
COPY --from=builder --chown=tanstack:nodejs /app/package*.json ./
COPY --from=builder --chown=tanstack:nodejs /app/public ./public

# Install only production dependencies
RUN if [ -f yarn.lock ]; then \
      yarn install --production --frozen-lockfile; \
    else \
      npm ci --only=production; \
    fi

# Switch to non-root user
USER tanstack

# Expose port
EXPOSE 3003

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3003/health || exit 1

# Set environment
ENV NODE_ENV=production
ENV PORT=3003

# Start the application
CMD ["npm", "start"]