import { json } from '@tanstack/react-start'
import { createServerFileRoute } from '@tanstack/react-start/server'
import { documentProcessor } from '~/lib/document-processing/client'
import type { ProcessedDocument } from '~/types'

export const ServerRoute = createServerFileRoute('/api/documents/upload').methods({
  POST: async ({ request }) => {
    try {
      const formData = await request.formData();
      const file = formData.get('file') as File;
      const url = formData.get('url') as string;
      const folderId = formData.get('folderId') as string;
      const tags = formData.get('tags') as string;
      const title = formData.get('title') as string;
      const description = formData.get('description') as string;
      const extractMetadata = formData.get('extractMetadata') === 'true';
      const chunkSize = parseInt(formData.get('chunkSize') as string) || 1000;
      const chunkOverlap = parseInt(formData.get('chunkOverlap') as string) || 200;

      const options = {
        extractMetadata,
        chunkSize,
        chunkOverlap,
        folderId: folderId || undefined,
        tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
        title: title || undefined,
        description: description || undefined,
      };

      let result;

      if (file) {
        // Validate file before processing
        const validation = documentProcessor.validateFile(file);
        if (!validation.valid) {
          return json({ error: validation.error }, { status: 400 });
        }

        result = await documentProcessor.processFile(file, options);
      } else if (url) {
        result = await documentProcessor.processUrl(url, options);
      } else {
        return json({ error: 'Either file or URL is required' }, { status: 400 });
      }

      if (!result.success) {
        return json({ error: result.error || 'Processing failed' }, { status: 500 });
      }

      // Return the job ID and document info for status tracking
      return json({
        success: true,
        jobId: result.jobId,
        document: result.document,
        message: 'Document uploaded and processing started'
      });

    } catch (error) {
      console.error('Document upload error:', error);
      return json(
        { error: 'Failed to process document' },
        { status: 500 }
      );
    }
  }
})