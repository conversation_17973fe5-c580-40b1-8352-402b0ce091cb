import { v4 as uuidv4 } from 'uuid'

interface ProcessingResult {
  success: boolean;
  content: string;
  metadata: Record<string, any>;
  chunks?: DocumentChunk[];
  word_count?: number;
  char_count?: number;
  processing_time?: number;
  extracted_at?: string;
}

interface DocumentChunk {
  id: string;
  content: string;
  start_char: number;
  end_char: number;
  metadata: Record<string, any>;
}

interface ProcessingStatus {
  job_id: string;
  status: string;
  progress: number;
  result?: ProcessingResult;
  error?: string;
}

class RealDocumentProcessor {
  private pythonServiceUrl: string;
  private commandCenterUrl: string;

  constructor() {
    // In production, these would come from environment variables
    this.pythonServiceUrl = import.meta.env.VITE_PYTHON_SERVICE_URL || 'http://localhost:8001';
    this.commandCenterUrl = import.meta.env.VITE_COMMAND_CENTER_URL || 'http://localhost:3000';
  }

  async processFile(
    file: File,
    options: {
      extractMetadata?: boolean;
      chunkSize?: number;
      chunkOverlap?: number;
      folderId?: string;
      tags?: string[];
      title?: string;
      description?: string;
    } = {}
  ): Promise<{
    success: boolean;
    jobId?: string;
    document?: any;
    error?: string;
  }> {
    try {
      // Use Command-Center API for file upload and processing
      const formData = new FormData();
      formData.append('file', file);
      
      if (options.extractMetadata !== undefined) {
        formData.append('extractMetadata', options.extractMetadata.toString());
      }
      if (options.chunkSize) {
        formData.append('chunkSize', options.chunkSize.toString());
      }
      if (options.chunkOverlap) {
        formData.append('chunkOverlap', options.chunkOverlap.toString());
      }
      if (options.folderId) {
        formData.append('folderId', options.folderId);
      }
      if (options.tags) {
        formData.append('tags', options.tags.join(','));
      }
      if (options.title) {
        formData.append('title', options.title);
      }
      if (options.description) {
        formData.append('description', options.description);
      }

      const response = await fetch(`${this.commandCenterUrl}/api/documents/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      
      return {
        success: true,
        jobId: result.jobId,
        document: result.document,
      };
    } catch (error) {
      console.error('File processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async processUrl(
    url: string,
    options: {
      extractMetadata?: boolean;
      chunkSize?: number;
      chunkOverlap?: number;
      folderId?: string;
      tags?: string[];
      title?: string;
      description?: string;
    } = {}
  ): Promise<{
    success: boolean;
    jobId?: string;
    document?: any;
    error?: string;
  }> {
    try {
      const requestBody = {
        url,
        extractMetadata: options.extractMetadata ?? true,
        chunkSize: options.chunkSize ?? 1000,
        chunkOverlap: options.chunkOverlap ?? 200,
        folderId: options.folderId,
        tags: options.tags || [],
        title: options.title,
        description: options.description,
      };

      const response = await fetch(`${this.commandCenterUrl}/api/documents/process/url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`URL processing failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      
      return {
        success: true,
        jobId: result.jobId,
        document: result.document,
      };
    } catch (error) {
      console.error('URL processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async getProcessingStatus(jobId: string): Promise<ProcessingStatus | null> {
    try {
      const response = await fetch(`${this.commandCenterUrl}/api/documents/processing/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get processing status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting processing status:', error);
      return null;
    }
  }

  async pollProcessingStatus(
    jobId: string,
    onUpdate?: (status: ProcessingStatus) => void,
    maxAttempts: number = 60,
    intervalMs: number = 2000
  ): Promise<ProcessingResult | null> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const status = await this.getProcessingStatus(jobId);
        
        if (!status) {
          throw new Error('Failed to get processing status');
        }

        if (onUpdate) {
          onUpdate(status);
        }

        if (status.status === 'completed' && status.result) {
          return status.result;
        }

        if (status.status === 'failed') {
          throw new Error(status.error || 'Processing failed');
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, intervalMs));
        attempts++;
      } catch (error) {
        console.error('Error polling status:', error);
        attempts++;
        
        if (attempts >= maxAttempts) {
          throw error;
        }
        
        await new Promise(resolve => setTimeout(resolve, intervalMs));
      }
    }

    throw new Error('Processing timeout - maximum attempts reached');
  }

  async processBatch(
    files: File[],
    urls: string[] = [],
    options: {
      extractMetadata?: boolean;
      chunkSize?: number;
      chunkOverlap?: number;
      folderId?: string;
      tags?: string[];
    } = {}
  ): Promise<{
    success: boolean;
    jobIds?: string[];
    documents?: any[];
    errors?: string[];
  }> {
    try {
      const formData = new FormData();
      
      // Add files
      files.forEach(file => {
        formData.append('files', file);
      });

      // Add URLs (if any)
      if (urls.length > 0) {
        urls.forEach(url => {
          formData.append('urls', url);
        });
      }

      // Add options
      if (options.extractMetadata !== undefined) {
        formData.append('extractMetadata', options.extractMetadata.toString());
      }
      if (options.chunkSize) {
        formData.append('chunkSize', options.chunkSize.toString());
      }
      if (options.chunkOverlap) {
        formData.append('chunkOverlap', options.chunkOverlap.toString());
      }
      if (options.folderId) {
        formData.append('folderId', options.folderId);
      }
      if (options.tags) {
        formData.append('tags', options.tags.join(','));
      }

      const response = await fetch(`${this.commandCenterUrl}/api/documents/upload/batch`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Batch processing failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      
      return {
        success: true,
        jobIds: result.jobIds,
        documents: result.documents,
      };
    } catch (error) {
      console.error('Batch processing error:', error);
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
      };
    }
  }

  private getAuthToken(): string {
    // In a real application, this would get the token from your auth system
    // For now, we'll assume it's stored in localStorage
    return localStorage.getItem('authToken') || '';
  }

  // Utility method to validate file types
  isFileTypeSupported(file: File): boolean {
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'text/markdown',
      'text/html',
    ];

    return supportedTypes.includes(file.type);
  }

  // Get file size limit (100MB)
  getMaxFileSize(): number {
    return 100 * 1024 * 1024; // 100MB
  }

  // Validate file before processing
  validateFile(file: File): { valid: boolean; error?: string } {
    if (!this.isFileTypeSupported(file)) {
      return {
        valid: false,
        error: `File type ${file.type} is not supported. Supported types: PDF, DOCX, DOC, TXT, MD, HTML`,
      };
    }

    if (file.size > this.getMaxFileSize()) {
      return {
        valid: false,
        error: `File size ${(file.size / 1024 / 1024).toFixed(2)}MB exceeds the maximum limit of 100MB`,
      };
    }

    return { valid: true };
  }
}

export const documentProcessor = new RealDocumentProcessor();