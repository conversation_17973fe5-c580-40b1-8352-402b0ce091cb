import type { Document, ProcessedDocument, Folder, Tag } from '~/types';
import { mockDocuments, mockFolders, mockTags } from '~/utils/mockData';

interface DocumentStore {
  addDocument(document: ProcessedDocument): Promise<void>;
  getDocument(id: string): Promise<ProcessedDocument | null>;
  getAllDocuments(): Promise<ProcessedDocument[]>;
  getDocuments(filters?: {
    folderId?: string;
    search?: string;
    tags?: string[];
    documentType?: Array<'pdf' | 'docx' | 'txt' | 'url'>;
    startDate?: string;
    endDate?: string;
  }): Promise<ProcessedDocument[]>;
  searchDocuments(query: string): Promise<ProcessedDocument[]>;
  deleteDocument(id: string): Promise<void>;
  updateDocument(id: string, updates: Partial<ProcessedDocument>): Promise<void>;
  
  addFolder(folder: Folder): Promise<void>;
  getFolders(): Promise<Folder[]>;
  getFolder(id: string): Promise<Folder | null>;
  deleteFolder(id: string): Promise<void>;
  updateFolder(id: string, updates: Partial<Folder>): Promise<void>;
  
  addTag(tag: Tag): Promise<void>;
  getTags(): Promise<Tag[]>;
  getTag(id: string): Promise<Tag | null>;
  deleteTag(id: string): Promise<void>;
  
  generateTags(content: string): Promise<string[]>;
}

export class InMemoryDocumentStore implements DocumentStore {
  private documents = new Map<string, ProcessedDocument>();
  private folders = new Map<string, Folder>();
  private tags = new Map<string, Tag>();

  constructor() {
    this.initializeWithMockData();
  }

  private initializeWithMockData() {
    // Initialize folders
    mockFolders.forEach(folder => this.folders.set(folder.id, folder));
    
    // Initialize tags
    mockTags.forEach(tag => this.tags.set(tag.id, tag));
    
    // Initialize documents as ProcessedDocuments
    mockDocuments.forEach(doc => {
      const processedDoc: ProcessedDocument = {
        ...doc,
        chunks: [],
        content: doc.content || `Sample content for ${doc.name}`,
        metadata: {
          ...doc.metadata,
          processed: true
        }
      };
      this.documents.set(doc.id, processedDoc);
    });
  }

  async addDocument(document: ProcessedDocument): Promise<void> {
    this.documents.set(document.id, document);
    
    // Update tag counts
    for (const tagName of document.tags) {
      const existingTag = Array.from(this.tags.values()).find(t => t.name === tagName);
      if (existingTag) {
        existingTag.documentCount++;
      } else {
        const newTag: Tag = {
          id: `tag-${Date.now()}-${Math.random()}`,
          name: tagName,
          color: this.generateColor(),
          documentCount: 1
        };
        this.tags.set(newTag.id, newTag);
      }
    }

    // Update folder document count
    if (document.folderId) {
      const folder = this.folders.get(document.folderId);
      if (folder) {
        folder.documentCount++;
      }
    }
  }

  async getDocument(id: string): Promise<ProcessedDocument | null> {
    return this.documents.get(id) || null;
  }

  async getAllDocuments(): Promise<ProcessedDocument[]> {
    return Array.from(this.documents.values());
  }

  async getDocuments(filters?: {
    folderId?: string;
    search?: string;
    tags?: string[];
    documentType?: Array<"pdf" | "docx" | "txt" | "url">;
    startDate?: string;
    endDate?: string;
  }): Promise<ProcessedDocument[]> {
    let documents = Array.from(this.documents.values());

    if (filters?.folderId) {
      documents = documents.filter(doc => doc.folderId === filters.folderId);
    }

    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      documents = documents.filter(doc =>
        doc.name.toLowerCase().includes(searchLower) ||
        doc.content?.toLowerCase().includes(searchLower)
      );
    }

    if (filters?.tags && filters.tags.length > 0) {
      documents = documents.filter(doc =>
        filters.tags!.some(tag => doc.tags.includes(tag))
      );
    }

    if (filters?.documentType && filters.documentType.length > 0) {
      documents = documents.filter(doc => filters.documentType!.includes(doc.type));
    }

    if (filters?.startDate) {
      documents = documents.filter(doc => new Date(doc.createdAt) >= new Date(filters.startDate!));
    }

    if (filters?.endDate) {
      documents = documents.filter(doc => new Date(doc.createdAt) <= new Date(filters.endDate!));
    }

    return documents;
  }

  async searchDocuments(query: string): Promise<ProcessedDocument[]> {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.documents.values()).filter(doc =>
      doc.name.toLowerCase().includes(lowerQuery) ||
      doc.content?.toLowerCase().includes(lowerQuery) ||
      doc.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  async deleteDocument(id: string): Promise<void> {
    const doc = this.documents.get(id);
    if (doc) {
      // Update tag counts
      for (const tagName of doc.tags) {
        const tag = Array.from(this.tags.values()).find(t => t.name === tagName);
        if (tag) {
          tag.documentCount--;
          if (tag.documentCount === 0) {
            this.tags.delete(tag.id);
          }
        }
      }

      // Update folder count
      if (doc.folderId) {
        const folder = this.folders.get(doc.folderId);
        if (folder) {
          folder.documentCount--;
        }
      }

      this.documents.delete(id);
    }
  }

  async updateDocument(id: string, updates: Partial<ProcessedDocument>): Promise<void> {
    const doc = this.documents.get(id);
    if (doc) {
      this.documents.set(id, { ...doc, ...updates, updatedAt: new Date().toISOString() });
    }
  }

  async addFolder(folder: Folder): Promise<void> {
    this.folders.set(folder.id, folder);
  }

  async getFolders(): Promise<Folder[]> {
    return Array.from(this.folders.values());
  }

  async getFolder(id: string): Promise<Folder | null> {
    return this.folders.get(id) || null;
  }

  async updateFolder(id: string, updates: Partial<Folder>): Promise<void> {
    const folder = this.folders.get(id);
    if (folder) {
      this.folders.set(id, { ...folder, ...updates });
    }
  }

  async deleteFolder(id: string): Promise<void> {
    // Move documents from deleted folder to root
    for (const doc of this.documents.values()) {
      if (doc.folderId === id) {
        doc.folderId = null;
      }
    }
    this.folders.delete(id);
  }

  async addTag(tag: Tag): Promise<void> {
    this.tags.set(tag.id, tag);
  }

  async getTags(): Promise<Tag[]> {
    return Array.from(this.tags.values());
  }

  async getTag(id: string): Promise<Tag | null> {
    return this.tags.get(id) || null;
  }

  async deleteTag(id: string): Promise<void> {
    const tag = this.tags.get(id);
    if (tag) {
      // Remove tag from all documents
      for (const doc of this.documents.values()) {
        doc.tags = doc.tags.filter(t => t !== tag.name);
      }
      this.tags.delete(id);
    }
  }

  private generateColor(): string {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  async generateTags(content: string): Promise<string[]> {
    const tags: string[] = [];
    
    // Simple keyword-based auto-tagging (in production, this would use AI)
    const keywords = {
      'ai': ['artificial intelligence', 'machine learning', 'neural', 'deep learning', 'AI', 'ML'],
      'healthcare': ['health', 'medical', 'patient', 'clinical', 'hospital', 'medicine'],
      'training': ['learning', 'education', 'course', 'training', 'onboarding', 'tutorial'],
      'research': ['study', 'research', 'analysis', 'findings', 'results', 'investigation'],
      'technology': ['software', 'hardware', 'computer', 'digital', 'tech', 'programming'],
      'business': ['company', 'business', 'corporate', 'enterprise', 'management', 'organization']
    };

    const contentLower = content.toLowerCase();
    
    for (const [tag, terms] of Object.entries(keywords)) {
      if (terms.some(term => contentLower.includes(term.toLowerCase()))) {
        tags.push(tag);
      }
    }

    return [...new Set(tags)]; // Remove duplicates
  }
}

// Export the API-based document store for production use
export { documentStore } from './api-document-store';

// Keep the in-memory store available for testing
export { InMemoryDocumentStore };