import type { Document, ProcessedDocument, Folder, Tag } from '~/types';

interface DocumentStore {
  addDocument(document: ProcessedDocument): Promise<void>;
  getDocument(id: string): Promise<ProcessedDocument | null>;
  getAllDocuments(): Promise<ProcessedDocument[]>;
  getDocuments(filters?: {
    folderId?: string;
    search?: string;
    tags?: string[];
    documentType?: Array<'pdf' | 'docx' | 'txt' | 'url'>;
    startDate?: string;
    endDate?: string;
  }): Promise<ProcessedDocument[]>;
  searchDocuments(query: string): Promise<ProcessedDocument[]>;
  deleteDocument(id: string): Promise<void>;
  updateDocument(id: string, updates: Partial<ProcessedDocument>): Promise<void>;
  
  addFolder(folder: Folder): Promise<void>;
  getFolders(): Promise<Folder[]>;
  getFolder(id: string): Promise<Folder | null>;
  deleteFolder(id: string): Promise<void>;
  updateFolder(id: string, updates: Partial<Folder>): Promise<void>;
  
  addTag(tag: Tag): Promise<void>;
  getTags(): Promise<Tag[]>;
  getTag(id: string): Promise<Tag | null>;
  deleteTag(id: string): Promise<void>;
  
  generateTags(content: string): Promise<string[]>;
}

export class ApiDocumentStore implements DocumentStore {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_COMMAND_CENTER_URL || 'http://localhost:3000';
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api${endpoint}`;
    const token = this.getAuthToken();

    const defaultHeaders = {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${errorText}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    }

    return {} as T;
  }

  private getAuthToken(): string {
    return localStorage.getItem('authToken') || '';
  }

  // Documents
  async addDocument(document: ProcessedDocument): Promise<void> {
    // This method is not used in the new flow since documents are created via upload/process endpoints
    // Keep for interface compatibility
    throw new Error('Use uploadDocument or processUrl instead');
  }

  async getDocument(id: string): Promise<ProcessedDocument | null> {
    try {
      const response = await this.makeRequest<any>(`/documents/${id}`);
      return this.transformDocumentFromAPI(response);
    } catch (error) {
      console.error('Error getting document:', error);
      return null;
    }
  }

  async getAllDocuments(): Promise<ProcessedDocument[]> {
    return this.getDocuments();
  }

  async getDocuments(filters?: {
    folderId?: string;
    search?: string;
    tags?: string[];
    documentType?: Array<'pdf' | 'docx' | 'txt' | 'url'>;
    startDate?: string;
    endDate?: string;
  }): Promise<ProcessedDocument[]> {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.folderId) {
        queryParams.append('folderId', filters.folderId);
      }
      if (filters?.tags && filters.tags.length > 0) {
        queryParams.append('tags', filters.tags.join(','));
      }
      if (filters?.documentType && filters.documentType.length > 0) {
        queryParams.append('documentType', filters.documentType.join(','));
      }
      if (filters?.startDate) {
        queryParams.append('startDate', filters.startDate);
      }
      if (filters?.endDate) {
        queryParams.append('endDate', filters.endDate);
      }

      const queryString = queryParams.toString();
      const endpoint = `/documents${queryString ? `?${queryString}` : ''}`;
      
      const response = await this.makeRequest<{
        documents: any[];
        total: number;
        page: number;
        totalPages: number;
      }>(endpoint);

      return response.documents.map(doc => this.transformDocumentFromAPI(doc));
    } catch (error) {
      console.error('Error getting documents:', error);
      return [];
    }
  }

  async searchDocuments(query: string): Promise<ProcessedDocument[]> {
    try {
      if (!query.trim()) {
        return [];
      }

      const response = await this.makeRequest<any[]>(`/documents/search?q=${encodeURIComponent(query)}`);
      return response.map(doc => this.transformDocumentFromAPI(doc));
    } catch (error) {
      console.error('Error searching documents:', error);
      return [];
    }
  }

  async semanticSearch(query: string, options?: {
    limit?: number;
    threshold?: number;
  }): Promise<ProcessedDocument[]> {
    try {
      if (!query.trim()) {
        return [];
      }

      const queryParams = new URLSearchParams();
      queryParams.append('q', query);
      if (options?.limit) {
        queryParams.append('limit', options.limit.toString());
      }
      if (options?.threshold) {
        queryParams.append('threshold', options.threshold.toString());
      }

      const response = await this.makeRequest<any[]>(`/documents/search/semantic?${queryParams.toString()}`);
      return response.map(doc => this.transformDocumentFromAPI(doc));
    } catch (error) {
      console.error('Error in semantic search:', error);
      return [];
    }
  }

  async deleteDocument(id: string): Promise<void> {
    try {
      await this.makeRequest(`/documents/${id}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  async updateDocument(id: string, updates: Partial<ProcessedDocument>): Promise<void> {
    try {
      await this.makeRequest(`/documents/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(this.transformDocumentForAPI(updates)),
      });
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  async getDocumentChunks(id: string, page = 1, limit = 10): Promise<{
    chunks: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const response = await this.makeRequest<{
        chunks: any[];
        total: number;
        page: number;
        totalPages: number;
      }>(`/documents/${id}/chunks?page=${page}&limit=${limit}`);
      
      return response;
    } catch (error) {
      console.error('Error getting document chunks:', error);
      return { chunks: [], total: 0, page: 1, totalPages: 0 };
    }
  }

  // Folders
  async getFolders(): Promise<Folder[]> {
    try {
      const response = await this.makeRequest<any[]>('/documents/folders');
      return response.map(folder => this.transformFolderFromAPI(folder));
    } catch (error) {
      console.error('Error getting folders:', error);
      return [];
    }
  }

  async getFolder(id: string): Promise<Folder | null> {
    try {
      const folders = await this.getFolders();
      return folders.find(folder => folder.id === id) || null;
    } catch (error) {
      console.error('Error getting folder:', error);
      return null;
    }
  }

  async addFolder(folder: Folder): Promise<void> {
    try {
      await this.makeRequest('/documents/folders', {
        method: 'POST',
        body: JSON.stringify({
          name: folder.name,
          parentId: folder.parentId,
        }),
      });
    } catch (error) {
      console.error('Error creating folder:', error);
      throw error;
    }
  }

  async updateFolder(id: string, updates: Partial<Folder>): Promise<void> {
    // Implementation would depend on API endpoint availability
    throw new Error('Folder updates not implemented in API yet');
  }

  async deleteFolder(id: string): Promise<void> {
    // Implementation would depend on API endpoint availability
    throw new Error('Folder deletion not implemented in API yet');
  }

  // Tags
  async getTags(): Promise<Tag[]> {
    try {
      const response = await this.makeRequest<any[]>('/documents/tags');
      return response.map(tag => this.transformTagFromAPI(tag));
    } catch (error) {
      console.error('Error getting tags:', error);
      return [];
    }
  }

  async getTag(id: string): Promise<Tag | null> {
    try {
      const tags = await this.getTags();
      return tags.find(tag => tag.id === id) || null;
    } catch (error) {
      console.error('Error getting tag:', error);
      return null;
    }
  }

  async addTag(tag: Tag): Promise<void> {
    // Tags are typically created automatically when documents are tagged
    // This would require a specific API endpoint
    throw new Error('Manual tag creation not implemented in API yet');
  }

  async deleteTag(id: string): Promise<void> {
    // Implementation would depend on API endpoint availability
    throw new Error('Tag deletion not implemented in API yet');
  }

  async generateTags(content: string): Promise<string[]> {
    // This would typically be handled by the AI service
    // For now, return empty array or implement a simple keyword extraction
    return [];
  }

  // Transform methods to convert between API and frontend formats
  private transformDocumentFromAPI(apiDoc: any): ProcessedDocument {
    return {
      id: apiDoc.id,
      name: apiDoc.title,
      type: this.mapDocumentType(apiDoc.type),
      content: apiDoc.content || '',
      chunks: [], // Chunks are loaded separately
      size: apiDoc.size || 0,
      createdAt: apiDoc.createdAt,
      updatedAt: apiDoc.updatedAt,
      folderId: apiDoc.folderId,
      tags: apiDoc.tags || [],
      metadata: {
        ...apiDoc.metadata,
        processed: true,
        word_count: apiDoc.metadata?.wordCount,
        char_count: apiDoc.metadata?.charCount,
      },
    };
  }

  private transformDocumentForAPI(doc: Partial<ProcessedDocument>): any {
    return {
      title: doc.name,
      content: doc.content,
      folderId: doc.folderId,
      tags: doc.tags,
      metadata: doc.metadata,
    };
  }

  private transformFolderFromAPI(apiFolder: any): Folder {
    return {
      id: apiFolder.id,
      name: apiFolder.name,
      parentId: apiFolder.parentId,
      documentCount: apiFolder.documentCount || 0,
      color: apiFolder.color || '#3B82F6',
    };
  }

  private transformTagFromAPI(apiTag: any): Tag {
    return {
      id: apiTag.id,
      name: apiTag.name,
      color: apiTag.color || '#3B82F6',
      documentCount: apiTag.documentCount || 0,
    };
  }

  private mapDocumentType(apiType: string): 'pdf' | 'docx' | 'txt' | 'url' {
    const typeMapping: Record<string, 'pdf' | 'docx' | 'txt' | 'url'> = {
      'pdf': 'pdf',
      'text': 'txt',
      'other': 'url', // URLs are typically 'other' type
    };

    return typeMapping[apiType] || 'txt';
  }
}

export const documentStore = new ApiDocumentStore();