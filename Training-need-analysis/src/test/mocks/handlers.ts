import { http, HttpResponse } from 'msw'

// Define request handlers for training API endpoints
export const handlers = [
  // Training courses endpoints
  http.get('/api/training/courses', () => {
    return HttpResponse.json({
      courses: [
        {
          id: '1',
          title: 'React Fundamentals',
          description: 'Learn the basics of React',
          duration: '3 hours',
          level: 'beginner',
          category: 'frontend',
          completionRate: 75,
        },
        {
          id: '2',
          title: 'Advanced TypeScript',
          description: 'Master TypeScript patterns',
          duration: '5 hours',
          level: 'advanced',
          category: 'frontend',
          completionRate: 30,
        },
      ],
    })
  }),

  // Assessments endpoints
  http.get('/api/assessments', () => {
    return HttpResponse.json({
      assessments: [
        {
          id: '1',
          name: 'JavaScript Skills Assessment',
          questions: 20,
          duration: 30,
          status: 'completed',
          score: 85,
        },
      ],
    })
  }),

  // Skills gap analysis endpoint
  http.get('/api/skills/gap-analysis', () => {
    return HttpResponse.json({
      gaps: [
        {
          skill: 'React',
          currentLevel: 3,
          requiredLevel: 5,
          gap: 2,
        },
        {
          skill: 'TypeScript',
          currentLevel: 2,
          requiredLevel: 4,
          gap: 2,
        },
      ],
    })
  }),

  // Analytics endpoint
  http.get('/api/training/analytics', () => {
    return HttpResponse.json({
      totalCourses: 15,
      completedCourses: 8,
      inProgressCourses: 4,
      totalHours: 120,
      averageScore: 82,
    })
  }),

  // Create assessment endpoint
  http.post('/api/assessments', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      id: '2',
      ...body,
      status: 'pending',
    })
  }),
]