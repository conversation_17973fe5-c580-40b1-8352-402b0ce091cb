import { setupServer } from 'msw/node'
import { handlers } from './handlers'

// Setup requests interception using the given handlers
export const server = setupServer(...handlers)

// Enable more verbose logging in test environment
server.events.on('request:start', () => {
  // MSW intercepted request
})

server.events.on('request:match', () => {
  // MSW matched request
})

server.events.on('request:unhandled', ({ request }) => {
  console.warn('MSW unhandled:', request.method, request.url)
})

// Export server instance
export { handlers }