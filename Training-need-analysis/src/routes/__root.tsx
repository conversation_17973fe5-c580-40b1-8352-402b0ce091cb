import {
  Head<PERSON>onte<PERSON>,
  <PERSON>,
  Outlet,
  <PERSON><PERSON><PERSON>,
  createRootRoute,
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import * as React from 'react'
import { DefaultCatchBoundary } from '~/components/DefaultCatchBoundary'
import { NotFound } from '~/components/NotFound'
import appCss from '~/styles/app.css?url'
import { seo } from '~/utils/seo'
import { useAuthStore, AMNAWidget, ThemeProvider, IntegrationProvider } from '@luminar/shared-ui'

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      ...seo({
        title: 'TNA Platform | Training Need Analysis for L&D Teams',
        description: 'Comprehensive Training Need Analysis platform for Learning & Development professionals. Assess skills, identify gaps, and manage training programs effectively.',
      }),
    ],
    links: [
      { rel: 'stylesheet', href: appCss },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: '/apple-touch-icon.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: '/favicon-16x16.png',
      },
      { rel: 'manifest', href: '/site.webmanifest', color: '#fffff' },
      { rel: 'icon', href: '/favicon.ico' },
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    )
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
})

function RootComponent() {
  return (
    <ThemeProvider>
      <IntegrationProvider>
        <RootDocument>
          <Outlet />
        </RootDocument>
      </IntegrationProvider>
    </ThemeProvider>
  )
}

function RootDocument({ children }: { children: React.ReactNode }) {
  const user = useAuthStore((state) => state.user)
  const logout = useAuthStore((state) => state.logout)
  
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        <div className="bg-white border-b shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-8">
                <Link
                  to="/"
                  activeOptions={{ exact: true }}
                  className="text-xl font-bold text-blue-600"
                >
                  TNA Platform
                </Link>
                <nav className="flex space-x-6">
                  <Link
                    to="/dashboard"
                    activeProps={{
                      className: 'text-blue-600 font-medium border-b-2 border-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-transparent"
                  >
                    Dashboard
                  </Link>
                  <Link
                    to="/assessments"
                    activeProps={{
                      className: 'text-blue-600 font-medium border-b-2 border-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-transparent"
                  >
                    Assessments
                  </Link>
                  <Link
                    to="/training"
                    activeProps={{
                      className: 'text-blue-600 font-medium border-b-2 border-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-transparent"
                  >
                    Training
                  </Link>
                  <Link
                    to="/reports"
                    activeProps={{
                      className: 'text-blue-600 font-medium border-b-2 border-blue-600',
                    }}
                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium border-b-2 border-transparent"
                  >
                    Reports
                  </Link>
                </nav>
              </div>
              <div className="flex items-center space-x-4">
                <button className="text-gray-400 hover:text-gray-600">
                  <span className="sr-only">Notifications</span>
                  🔔
                </button>
                {user ? (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-700">{user.name}</span>
                    <div className="relative group">
                      <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer">
                        <span className="text-white text-sm font-medium">
                          {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </span>
                      </div>
                      <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 invisible group-hover:visible">
                        <div className="px-4 py-2 text-sm text-gray-700 border-b">
                          <div className="font-medium">{user.name}</div>
                          <div className="text-xs text-gray-500">{user.email}</div>
                        </div>
                        <button 
                          onClick={() => logout()}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          Sign out
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <Link
                    to="/"
                    className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Sign in
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>
        <hr />
        {children}
        
        {/* AMNAWidget for cross-app AI assistance */}
        <AMNAWidget
          position="bottom-left"
          context={{ 
            app: 'training-need-analysis',
            page: typeof window !== 'undefined' ? window.location.pathname : '/',
            environment: 'production'
          }}
          showBadge={true}
        />
        
        <TanStackRouterDevtools position="bottom-right" />
        <Scripts />
      </body>
    </html>
  )
}
