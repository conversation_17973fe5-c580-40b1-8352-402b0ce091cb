import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor, within } from '@testing-library/react'
import { render } from '../test/test-utils'
import { useTrainingStore, useAuthStore } from '@luminar/shared-ui'
import TrainingCatalog from './training.index'

// Mock the stores
vi.mock('@luminar/shared-ui', () => ({
  useTrainingStore: vi.fn(),
  useAuthStore: vi.fn(),
}))

// Mock TanStack Router components
vi.mock('@tanstack/react-router', () => ({
  Link: ({ children, to, params }: any) => (
    <a href={params ? `${to}/${params.courseId}` : to}>{children}</a>
  ),
  createFileRoute: (path: string) => ({
    component: TrainingCatalog,
  }),
}))

describe('TrainingCatalog', () => {
  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  }

  const mockCourses = [
    {
      id: '1',
      title: 'Advanced React Development',
      description: 'Master advanced React patterns and performance optimization',
      category: 'technical',
      level: 'advanced',
      rating: 4.8,
      enrollmentCount: 150,
      duration: 20,
      price: 99,
      provider: 'Tech Academy',
      format: 'online',
      skills: [{ name: 'React' }, { name: 'JavaScript' }],
    },
    {
      id: '2',
      title: 'Leadership Excellence',
      description: 'Develop essential leadership skills for modern teams',
      category: 'soft_skills',
      level: 'intermediate',
      rating: 4.6,
      enrollmentCount: 200,
      duration: 15,
      price: 0,
      provider: 'Internal',
      format: 'workshop',
      skills: [{ name: 'Leadership' }, { name: 'Communication' }],
    },
    {
      id: '3',
      title: 'Python for Data Science',
      description: 'Learn Python programming for data analysis and machine learning',
      category: 'technical',
      level: 'beginner',
      rating: 4.9,
      enrollmentCount: 300,
      duration: 30,
      price: 149,
      provider: 'Data Institute',
      format: 'certification',
      skills: [{ name: 'Python' }, { name: 'Data Science' }],
    },
    {
      id: '4',
      title: 'Effective Communication',
      description: 'Master the art of professional communication',
      category: 'soft_skills',
      level: 'beginner',
      rating: 4.5,
      enrollmentCount: 100,
      duration: 10,
      price: 0,
      provider: 'HR Department',
      format: 'internal',
      skills: [{ name: 'Communication' }, { name: 'Soft Skills' }],
    },
  ]

  const mockRecommendations = [
    mockCourses[0],
    mockCourses[2],
  ]

  const defaultMockStore = {
    courses: mockCourses,
    coursesLoading: false,
    coursesPagination: {
      total: 50,
      page: 1,
      limit: 20,
      totalPages: 3,
    },
    recommendations: mockRecommendations,
    fetchCourses: vi.fn(),
    fetchRecommendations: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAuthStore as any).mockReturnValue({ user: mockUser })
    ;(useTrainingStore as any).mockReturnValue(defaultMockStore)
  })

  it('renders training catalog header', () => {
    render(<TrainingCatalog />)

    expect(screen.getByText('Discover Your Next Learning Adventure')).toBeInTheDocument()
    expect(screen.getByText(/Explore our comprehensive training catalog/)).toBeInTheDocument()
  })

  it('fetches courses and recommendations on mount', async () => {
    render(<TrainingCatalog />)

    await waitFor(() => {
      expect(defaultMockStore.fetchCourses).toHaveBeenCalledWith({ page: 1, limit: 20 })
      expect(defaultMockStore.fetchRecommendations).toHaveBeenCalled()
    })
  })

  it('displays search input and button', () => {
    render(<TrainingCatalog />)

    expect(screen.getByPlaceholderText('Search courses, skills, or providers...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument()
  })

  it('handles search functionality', async () => {
    const { user } = render(<TrainingCatalog />)

    const searchInput = screen.getByPlaceholderText('Search courses, skills, or providers...')
    await user.type(searchInput, 'React')

    const searchButton = screen.getByRole('button', { name: /search/i })
    await user.click(searchButton)

    expect(defaultMockStore.fetchCourses).toHaveBeenCalledWith({
      search: 'React',
      page: 1,
      limit: 20,
    })
  })

  it('handles search on Enter key', async () => {
    const { user } = render(<TrainingCatalog />)

    const searchInput = screen.getByPlaceholderText('Search courses, skills, or providers...')
    await user.type(searchInput, 'Python')
    await user.keyboard('{Enter}')

    expect(defaultMockStore.fetchCourses).toHaveBeenCalledWith({
      search: 'Python',
      page: 1,
      limit: 20,
    })
  })

  it('displays loading state', () => {
    ;(useTrainingStore as any).mockReturnValue({
      ...defaultMockStore,
      coursesLoading: true,
      courses: [],
    })

    render(<TrainingCatalog />)

    expect(screen.getByText('Loading courses...')).toBeInTheDocument()
  })

  it('displays training statistics', () => {
    render(<TrainingCatalog />)

    // Total Courses
    expect(screen.getByText('Total Courses')).toBeInTheDocument()
    expect(screen.getByText('50')).toBeInTheDocument()

    // Total Enrollments
    expect(screen.getByText('Total Enrollments')).toBeInTheDocument()
    expect(screen.getByText('750')).toBeInTheDocument() // Sum of all enrollments

    // Average Rating
    expect(screen.getByText('Avg Course Rating')).toBeInTheDocument()
    expect(screen.getByText('4.7')).toBeInTheDocument() // Average of ratings

    // Total Hours
    expect(screen.getByText('Hours of Content')).toBeInTheDocument()
    expect(screen.getByText('75')).toBeInTheDocument() // Sum of durations
  })

  it('displays featured courses', () => {
    render(<TrainingCatalog />)

    const featuredSection = screen.getByText('Featured Courses').closest('section')
    expect(featuredSection).toBeInTheDocument()

    // Should show high-rated courses (>= 4.7)
    within(featuredSection!).getByText('Advanced React Development')
    within(featuredSection!).getByText('Python for Data Science')
  })

  it('displays popular courses sorted by enrollment', () => {
    render(<TrainingCatalog />)

    const popularSection = screen.getByText('Most Popular').closest('section')
    expect(popularSection).toBeInTheDocument()

    // Should be sorted by enrollment count
    const courseCards = within(popularSection!).getAllByRole('heading', { level: 3 })
    expect(courseCards[0]).toHaveTextContent('Python for Data Science') // 300 enrollments
    expect(courseCards[1]).toHaveTextContent('Leadership Excellence') // 200 enrollments
  })

  it('categorizes courses correctly', () => {
    render(<TrainingCatalog />)

    // Technical Skills section
    const techSection = screen.getByText('Technical Skills').closest('div')
    expect(within(techSection!).getByText('Advanced React Development')).toBeInTheDocument()
    expect(within(techSection!).getByText('Python for Data Science')).toBeInTheDocument()

    // Soft Skills section
    const softSection = screen.getByText('Soft Skills').closest('div')
    expect(within(softSection!).getByText('Leadership Excellence')).toBeInTheDocument()
    expect(within(softSection!).getByText('Effective Communication')).toBeInTheDocument()
  })

  it('displays course details correctly', () => {
    render(<TrainingCatalog />)

    const reactCourse = screen.getByText('Advanced React Development').closest('div')
    expect(within(reactCourse!).getByText('20h')).toBeInTheDocument()
    expect(within(reactCourse!).getByText('$99')).toBeInTheDocument()
    expect(within(reactCourse!).getByText('Tech Academy')).toBeInTheDocument()
    expect(within(reactCourse!).getByText('⭐ 4.8')).toBeInTheDocument()
  })

  it('displays free courses correctly', () => {
    render(<TrainingCatalog />)

    const freeCourse = screen.getByText('Leadership Excellence').closest('div')
    expect(within(freeCourse!).getByText('Free')).toBeInTheDocument()
  })

  it('shows difficulty badges', () => {
    render(<TrainingCatalog />)

    expect(screen.getByText('Advanced')).toBeInTheDocument()
    expect(screen.getByText('Intermediate')).toBeInTheDocument()
    expect(screen.getAllByText('Beginner')).toHaveLength(2)
  })

  it('shows format badges', () => {
    render(<TrainingCatalog />)

    expect(screen.getByText('Online')).toBeInTheDocument()
    expect(screen.getByText('Workshop')).toBeInTheDocument()
    expect(screen.getByText('Certification')).toBeInTheDocument()
    expect(screen.getByText('Internal')).toBeInTheDocument()
  })

  it('displays quick actions section', () => {
    render(<TrainingCatalog />)

    expect(screen.getByText('Quick Actions')).toBeInTheDocument()
    expect(screen.getByText('Request New Course')).toBeInTheDocument()
    expect(screen.getByText('Create Learning Path')).toBeInTheDocument()
    expect(screen.getByText('Training Budget')).toBeInTheDocument()
  })

  it('shows recommendation count in button', () => {
    render(<TrainingCatalog />)

    const recommendationsButton = screen.getByRole('button', { name: /my recommendations/i })
    expect(recommendationsButton).toHaveTextContent('My Recommendations (2)')
  })

  it('does not fetch recommendations without user', async () => {
    ;(useAuthStore as any).mockReturnValue({ user: null })

    render(<TrainingCatalog />)

    await waitFor(() => {
      expect(defaultMockStore.fetchRecommendations).not.toHaveBeenCalled()
    })
  })

  it('navigates to course detail page', () => {
    render(<TrainingCatalog />)

    const courseLink = screen.getAllByRole('link', { name: /view course/i })[0]
    expect(courseLink).toHaveAttribute('href', '/training/1')
  })

  it('navigates to learning paths page', () => {
    render(<TrainingCatalog />)

    const learningPathsLink = screen.getByRole('link', { name: /learning paths/i })
    expect(learningPathsLink).toHaveAttribute('href', '/training/learning-paths')
  })

  it('navigates to recommendations page', () => {
    render(<TrainingCatalog />)

    const recommendationsLink = screen.getByRole('link', { name: /my recommendations/i })
    expect(recommendationsLink).toHaveAttribute('href', '/training/recommendations')
  })

  it('handles view all courses button', async () => {
    const { user } = render(<TrainingCatalog />)

    const viewAllButton = screen.getByRole('button', { name: /view all courses/i })
    await user.click(viewAllButton)

    expect(defaultMockStore.fetchCourses).toHaveBeenCalledWith({ page: 1, limit: 50 })
  })

  it('disables search button while loading', () => {
    ;(useTrainingStore as any).mockReturnValue({
      ...defaultMockStore,
      coursesLoading: true,
    })

    render(<TrainingCatalog />)

    const searchButton = screen.getByRole('button', { name: /search/i })
    expect(searchButton).toBeDisabled()
  })

  it('handles empty search results', () => {
    ;(useTrainingStore as any).mockReturnValue({
      ...defaultMockStore,
      courses: [],
      coursesPagination: { total: 0 },
    })

    render(<TrainingCatalog />)

    // Statistics should show zeros
    expect(screen.getByText('0')).toBeInTheDocument()
    expect(screen.getByText('0.0')).toBeInTheDocument()
  })

  it('calculates statistics correctly with no courses', () => {
    ;(useTrainingStore as any).mockReturnValue({
      ...defaultMockStore,
      courses: [],
    })

    render(<TrainingCatalog />)

    // Should handle division by zero for average rating
    const avgRating = screen.getByText('Avg Course Rating').parentElement
    expect(within(avgRating!).getByText('0.0')).toBeInTheDocument()
  })

  it('shows enrollment count with proper formatting', () => {
    const highEnrollmentCourse = {
      ...mockCourses[0],
      enrollmentCount: 1500,
    }

    ;(useTrainingStore as any).mockReturnValue({
      ...defaultMockStore,
      courses: [highEnrollmentCourse],
    })

    render(<TrainingCatalog />)

    expect(screen.getByText('1,500')).toBeInTheDocument()
  })
})</function_results>

Now let me create one more test file for the Vendors analytics component:

<function_calls>
<invoke name="mcp__filesystem__read_file">
<parameter name="path">/Users/<USER>/Luminar/Vendors/src/components/analytics/VendorAnalytics.tsx