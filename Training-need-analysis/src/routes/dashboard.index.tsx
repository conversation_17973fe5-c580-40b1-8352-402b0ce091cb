import { createFileRoute } from '@tanstack/react-router'
import { api, useTrainingStore, useAuthStore } from '@luminar/shared-ui'
import { useEffect } from 'react'

export const Route = createFileRoute('/dashboard/')({
  component: DashboardOverview,
})

function DashboardOverview() {
  const user = useAuthStore((state) => state.user)
  const {
    metrics,
    userProgress,
    courses,
    enrollments,
    skillGaps,
    fetchTrainingMetrics,
    fetchUserProgress,
    fetchCourses,
    fetchEnrollments,
    fetchSkillGaps,
  } = useTrainingStore()

  useEffect(() => {
    // Fetch dashboard data
    const loadDashboardData = async () => {
      await Promise.all([
        fetchTrainingMetrics(user?.departmentId),
        fetchUserProgress(user?.id),
        fetchCourses({ limit: 5 }), // Recent courses
        fetchEnrollments(user?.id),
        fetchSkillGaps(user?.id),
      ])
    }

    loadDashboardData()
  }, [user, fetchTrainingMetrics, fetchUserProgress, fetchCourses, fetchEnrollments, fetchSkillGaps])

  // Loading state
  if (!metrics || !userProgress) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading dashboard...</div>
      </div>
    )
  }

  // Transform data for backwards compatibility with existing UI
  const analytics = {
    totalEmployees: metrics.totalUsers || 0,
    totalSkillGaps: skillGaps?.length || 0,
    criticalGaps: skillGaps?.filter(gap => gap.priority === 'critical').length || 0,
    trainingCompleted: metrics.completedCourses || 0,
    budgetUtilized: metrics.totalBudgetUsed || 0,
    trainingROI: metrics.roi || 1.0,
    departmentMetrics: metrics.departmentBreakdown || [],
    skillTrends: metrics.skillTrends || [],
  }

  const recentActivities = [
    ...enrollments.slice(0, 5).map(e => ({
      id: e.id,
      type: e.status === 'completed' ? 'training-completed' : 'training-started',
      description: `${e.status === 'completed' ? 'Completed' : 'Started'} ${e.course?.title || 'training'}`,
      date: e.enrolledAt,
    })),
    ...skillGaps.slice(0, 5).map(g => ({
      id: g.id,
      type: 'gap-identified',
      description: `Skill gap identified: ${g.skill?.name || 'Unknown skill'}`,
      date: g.identifiedAt,
    })),
  ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 10)

  return (
    <div className="space-y-6">
      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Employees"
          value={analytics.totalEmployees}
          icon="👥"
          color="blue"
        />
        <MetricCard
          title="Skill Gaps Identified"
          value={analytics.totalSkillGaps}
          icon="⚠️"
          color="red"
          subtitle={`${analytics.criticalGaps} critical`}
        />
        <MetricCard
          title="Training Completed"
          value={analytics.trainingCompleted}
          icon="✅"
          color="green"
          subtitle="This quarter"
        />
        <MetricCard
          title="Budget Utilized"
          value={`$${analytics.budgetUtilized.toLocaleString()}`}
          icon="💰"
          color="purple"
          subtitle={`ROI: ${analytics.trainingROI}x`}
        />
      </div>

      {/* Department Performance */}
      <div className="bg-white rounded-lg border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Department Performance</h2>
        <div className="space-y-4">
          {analytics.departmentMetrics.map((dept) => (
            <div key={dept.departmentId} className="border-l-4 border-blue-400 pl-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium text-gray-900">{dept.departmentName}</h3>
                  <p className="text-sm text-gray-600">{dept.employeeCount} employees</p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-500">Avg Skill Level</div>
                  <div className="text-lg font-semibold">{dept.averageSkillLevel.toFixed(1)}/3</div>
                </div>
              </div>
              <div className="mt-2 grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Skill Gaps:</span>
                  <span className="ml-1 font-medium text-red-600">{dept.skillGaps}</span>
                </div>
                <div>
                  <span className="text-gray-500">Training:</span>
                  <span className="ml-1 font-medium text-blue-600">{dept.trainingEnrollments}</span>
                </div>
                <div>
                  <span className="text-gray-500">Budget:</span>
                  <span className="ml-1 font-medium">
                    ${dept.budgetUsed.toLocaleString()} / ${dept.budgetAllocated.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Skills Trends and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Skills Trends */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Skills Trends</h2>
          <div className="space-y-3">
            {analytics.skillTrends.map((skill) => (
              <div key={skill.skillId} className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{skill.skillName}</div>
                  <div className="text-sm text-gray-500">{skill.category}</div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{skill.averageLevel.toFixed(1)}/3</span>
                    <TrendIndicator trend={skill.trend} percentage={skill.trendPercentage} />
                  </div>
                  <div className="text-xs text-gray-500">{skill.gapCount} gaps</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
          <div className="space-y-3">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex space-x-3">
                <ActivityIcon type={activity.type} />
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">{formatDate(activity.date)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function MetricCard({ 
  title, 
  value, 
  icon, 
  color, 
  subtitle 
}: { 
  title: string
  value: string | number
  icon: string
  color: 'blue' | 'red' | 'green' | 'purple'
  subtitle?: string 
}) {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200',
    red: 'bg-red-50 border-red-200',
    green: 'bg-green-50 border-green-200',
    purple: 'bg-purple-50 border-purple-200',
  }

  return (
    <div className={`p-6 rounded-lg border ${colorClasses[color]}`}>
      <div className="flex items-center">
        <span className="text-2xl mr-3">{icon}</span>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
        </div>
      </div>
    </div>
  )
}

function TrendIndicator({ trend, percentage }: { trend: string; percentage: number }) {
  const isPositive = trend === 'improving'
  const isNeutral = trend === 'stable'
  
  if (isNeutral) {
    return <span className="text-gray-400">→</span>
  }
  
  return (
    <span className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
      {isPositive ? '↗' : '↘'} {percentage}%
    </span>
  )
}

function ActivityIcon({ type }: { type: string }) {
  const icons = {
    'assessment-completed': '📋',
    'training-started': '📚',
    'training-completed': '🎓',
    'skill-updated': '⬆️',
    'gap-identified': '🔍',
  }
  
  return (
    <span className="text-lg">
      {icons[type as keyof typeof icons] || '📄'}
    </span>
  )
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMs = now.getTime() - date.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))
  
  if (diffInDays === 0) return 'Today'
  if (diffInDays === 1) return 'Yesterday'
  if (diffInDays < 7) return `${diffInDays} days ago`
  
  return date.toLocaleDateString()
}