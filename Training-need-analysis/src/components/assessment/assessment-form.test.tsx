import { describe, it, expect, vi } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { AssessmentForm } from './assessment-form'

describe('AssessmentForm', () => {
  const mockSkills = [
    {
      id: 'skill-1',
      name: 'React',
      description: 'React framework knowledge',
      category: 'Frontend',
    },
    {
      id: 'skill-2',
      name: 'TypeScript',
      description: 'TypeScript programming',
      category: 'Frontend',
    },
    {
      id: 'skill-3',
      name: 'Testing',
      description: 'Unit and integration testing',
      category: 'Quality',
    },
  ]

  const defaultProps = {
    assessmentId: 'assessment-1',
    title: 'Frontend Skills Assessment',
    description: 'Assess your frontend development skills',
    skills: mockSkills,
    onSubmit: vi.fn(),
    onSaveDraft: vi.fn(),
  }

  it('renders assessment form with title and description', () => {
    render(<AssessmentForm {...defaultProps} />)

    expect(screen.getByText('Frontend Skills Assessment')).toBeInTheDocument()
    expect(screen.getByText('Assess your frontend development skills')).toBeInTheDocument()
  })

  it('shows progress indicator', () => {
    render(<AssessmentForm {...defaultProps} />)

    expect(screen.getByText('1 of 3')).toBeInTheDocument()
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
  })

  it('displays first skill by default', () => {
    render(<AssessmentForm {...defaultProps} />)

    expect(screen.getByText('React')).toBeInTheDocument()
    expect(screen.getByText('React framework knowledge')).toBeInTheDocument()
  })

  it('navigates to next skill when Next button is clicked', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)

    expect(screen.getByText('TypeScript')).toBeInTheDocument()
    expect(screen.getByText('TypeScript programming')).toBeInTheDocument()
    expect(screen.getByText('2 of 3')).toBeInTheDocument()
  })

  it('navigates to previous skill when Previous button is clicked', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    // First go to second skill
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)

    // Then go back
    const previousButton = screen.getByRole('button', { name: /previous/i })
    await user.click(previousButton)

    expect(screen.getByText('React')).toBeInTheDocument()
    expect(screen.getByText('1 of 3')).toBeInTheDocument()
  })

  it('disables Previous button on first skill', () => {
    render(<AssessmentForm {...defaultProps} />)

    const previousButton = screen.getByRole('button', { name: /previous/i })
    expect(previousButton).toBeDisabled()
  })

  it('shows Submit button on last skill', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    // Navigate to last skill
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton) // Go to skill 2
    await user.click(nextButton) // Go to skill 3

    expect(screen.getByRole('button', { name: /submit assessment/i })).toBeInTheDocument()
    expect(screen.queryByRole('button', { name: /next/i })).not.toBeInTheDocument()
  })

  it('updates self rating when slider is moved', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    const selfRatingSlider = screen.getByLabelText(/self assessment/i)
    await user.click(selfRatingSlider)

    // Verify the rating updates (exact value depends on slider implementation)
    expect(selfRatingSlider).toHaveAttribute('aria-valuenow')
  })

  it('updates confidence level when slider is moved', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    const confidenceSlider = screen.getByLabelText(/confidence level/i)
    await user.click(confidenceSlider)

    expect(confidenceSlider).toHaveAttribute('aria-valuenow')
  })

  it('calls onSubmit with all responses when submitted', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    // Navigate through all skills and set ratings
    for (let i = 0; i < mockSkills.length; i++) {
      const selfRatingSlider = screen.getByLabelText(/self assessment/i)
      const confidenceSlider = screen.getByLabelText(/confidence level/i)
      
      // Simulate setting values (implementation specific)
      await user.click(selfRatingSlider)
      await user.click(confidenceSlider)

      if (i < mockSkills.length - 1) {
        const nextButton = screen.getByRole('button', { name: /next/i })
        await user.click(nextButton)
      }
    }

    // Submit the assessment
    const submitButton = screen.getByRole('button', { name: /submit assessment/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            skillId: 'skill-1',
            selfRating: expect.any(Number),
            confidence: expect.any(Number),
          }),
          expect.objectContaining({
            skillId: 'skill-2',
            selfRating: expect.any(Number),
            confidence: expect.any(Number),
          }),
          expect.objectContaining({
            skillId: 'skill-3',
            selfRating: expect.any(Number),
            confidence: expect.any(Number),
          }),
        ])
      )
    })
  })

  it('calls onSaveDraft when Save Draft button is clicked', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    const saveDraftButton = screen.getByRole('button', { name: /save draft/i })
    await user.click(saveDraftButton)

    await waitFor(() => {
      expect(defaultProps.onSaveDraft).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            skillId: 'skill-1',
          }),
        ])
      )
    })
  })

  it('preserves responses when navigating between skills', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    // Set rating on first skill
    const selfRatingSlider = screen.getByLabelText(/self assessment/i)
    await user.click(selfRatingSlider)

    // Navigate to next skill
    const nextButton = screen.getByRole('button', { name: /next/i })
    await user.click(nextButton)

    // Navigate back
    const previousButton = screen.getByRole('button', { name: /previous/i })
    await user.click(previousButton)

    // Rating should be preserved
    expect(selfRatingSlider).toHaveAttribute('aria-valuenow')
  })

  it('shows completion indicator for completed skills', async () => {
    const { user } = render(<AssessmentForm {...defaultProps} />)

    // Complete first skill
    const selfRatingSlider = screen.getByLabelText(/self assessment/i)
    const confidenceSlider = screen.getByLabelText(/confidence level/i)
    await user.click(selfRatingSlider)
    await user.click(confidenceSlider)

    // The Next button should be enabled
    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).not.toBeDisabled()
  })

  it('handles empty skills array gracefully', () => {
    render(<AssessmentForm {...defaultProps} skills={[]} />)

    expect(screen.getByText('Frontend Skills Assessment')).toBeInTheDocument()
    // Should not crash
  })

  it('handles missing onSaveDraft callback', async () => {
    const propsWithoutSaveDraft = {
      ...defaultProps,
      onSaveDraft: undefined,
    }

    const { user } = render(<AssessmentForm {...propsWithoutSaveDraft} />)

    const saveDraftButton = screen.getByRole('button', { name: /save draft/i })
    await user.click(saveDraftButton)

    // Should not throw error
    expect(true).toBe(true)
  })
})