import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { ReportBuilder } from './report-builder'

// Mock the child components
vi.mock('./chart-widgets', () => ({
  ChartWidget: ({ title, type }: any) => (
    <div data-testid={`chart-${type}`}>{title}</div>
  ),
}))

vi.mock('./export-panel', () => ({
  ExportPanel: ({ onExport }: any) => (
    <div data-testid="export-panel">
      <button onClick={() => onExport('pdf')}>Export PDF</button>
      <button onClick={() => onExport('excel')}>Export Excel</button>
    </div>
  ),
}))

describe('ReportBuilder', () => {
  const mockReportConfig = {
    title: 'Monthly Training Report',
    dateRange: {
      start: '2024-01-01',
      end: '2024-01-31',
    },
    metrics: ['completion_rate', 'average_score', 'total_hours'],
    filters: {
      department: 'Engineering',
      courseType: 'technical',
    },
  }

  const defaultProps = {
    config: mockReportConfig,
    onConfigChange: vi.fn(),
    onGenerate: vi.fn(),
    onExport: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders report builder with title', () => {
    render(<ReportBuilder {...defaultProps} />)

    expect(screen.getByText('Report Builder')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Monthly Training Report')).toBeInTheDocument()
  })

  it('displays date range inputs', () => {
    render(<ReportBuilder {...defaultProps} />)

    const startDateInput = screen.getByLabelText(/start date/i)
    const endDateInput = screen.getByLabelText(/end date/i)

    expect(startDateInput).toHaveValue('2024-01-01')
    expect(endDateInput).toHaveValue('2024-01-31')
  })

  it('shows metric selection checkboxes', () => {
    render(<ReportBuilder {...defaultProps} />)

    expect(screen.getByRole('checkbox', { name: /completion rate/i })).toBeChecked()
    expect(screen.getByRole('checkbox', { name: /average score/i })).toBeChecked()
    expect(screen.getByRole('checkbox', { name: /total hours/i })).toBeChecked()
    expect(screen.getByRole('checkbox', { name: /enrollment count/i })).not.toBeChecked()
  })

  it('updates title when changed', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const titleInput = screen.getByDisplayValue('Monthly Training Report')
    await user.clear(titleInput)
    await user.type(titleInput, 'Quarterly Training Report')

    expect(defaultProps.onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Quarterly Training Report',
      })
    )
  })

  it('updates date range when changed', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const startDateInput = screen.getByLabelText(/start date/i)
    await user.clear(startDateInput)
    await user.type(startDateInput, '2024-02-01')

    expect(defaultProps.onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        dateRange: expect.objectContaining({
          start: '2024-02-01',
        }),
      })
    )
  })

  it('toggles metrics when checkboxes are clicked', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const enrollmentCheckbox = screen.getByRole('checkbox', { name: /enrollment count/i })
    await user.click(enrollmentCheckbox)

    expect(defaultProps.onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        metrics: expect.arrayContaining(['enrollment_count']),
      })
    )
  })

  it('removes metric when unchecked', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const completionCheckbox = screen.getByRole('checkbox', { name: /completion rate/i })
    await user.click(completionCheckbox)

    expect(defaultProps.onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        metrics: expect.not.arrayContaining(['completion_rate']),
      })
    )
  })

  it('displays filter dropdowns', () => {
    render(<ReportBuilder {...defaultProps} />)

    expect(screen.getByLabelText(/department/i)).toHaveValue('Engineering')
    expect(screen.getByLabelText(/course type/i)).toHaveValue('technical')
  })

  it('updates filters when changed', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const departmentSelect = screen.getByLabelText(/department/i)
    await user.selectOptions(departmentSelect, 'Marketing')

    expect(defaultProps.onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        filters: expect.objectContaining({
          department: 'Marketing',
        }),
      })
    )
  })

  it('calls onGenerate when Generate Report button is clicked', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(defaultProps.onGenerate).toHaveBeenCalledWith(mockReportConfig)
  })

  it('shows loading state when generating report', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} isGenerating={true} />)

    expect(screen.getByText(/generating report/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /generating/i })).toBeDisabled()
  })

  it('displays preview charts when data is available', () => {
    const reportData = {
      completionRate: 85,
      averageScore: 92,
      totalHours: 320,
    }

    render(<ReportBuilder {...defaultProps} reportData={reportData} />)

    expect(screen.getByTestId('chart-line')).toBeInTheDocument()
    expect(screen.getByTestId('chart-bar')).toBeInTheDocument()
    expect(screen.getByTestId('chart-pie')).toBeInTheDocument()
  })

  it('shows export panel when report is generated', () => {
    const reportData = {
      completionRate: 85,
      averageScore: 92,
      totalHours: 320,
    }

    render(<ReportBuilder {...defaultProps} reportData={reportData} />)

    expect(screen.getByTestId('export-panel')).toBeInTheDocument()
  })

  it('calls onExport with correct format', async () => {
    const reportData = {
      completionRate: 85,
      averageScore: 92,
      totalHours: 320,
    }

    const { user } = render(<ReportBuilder {...defaultProps} reportData={reportData} />)

    const exportPdfButton = screen.getByRole('button', { name: /export pdf/i })
    await user.click(exportPdfButton)

    expect(defaultProps.onExport).toHaveBeenCalledWith('pdf')
  })

  it('validates date range', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const startDateInput = screen.getByLabelText(/start date/i)
    const endDateInput = screen.getByLabelText(/end date/i)

    // Set end date before start date
    await user.clear(startDateInput)
    await user.type(startDateInput, '2024-02-01')
    await user.clear(endDateInput)
    await user.type(endDateInput, '2024-01-01')

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(screen.getByText(/end date must be after start date/i)).toBeInTheDocument()
    expect(defaultProps.onGenerate).not.toHaveBeenCalled()
  })

  it('requires at least one metric to be selected', async () => {
    const configWithNoMetrics = {
      ...mockReportConfig,
      metrics: [],
    }

    const { user } = render(<ReportBuilder {...defaultProps} config={configWithNoMetrics} />)

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(screen.getByText(/please select at least one metric/i)).toBeInTheDocument()
    expect(defaultProps.onGenerate).not.toHaveBeenCalled()
  })

  it('shows saved report templates', () => {
    const templates = [
      { id: '1', name: 'Weekly Summary', config: mockReportConfig },
      { id: '2', name: 'Department Analysis', config: mockReportConfig },
    ]

    render(<ReportBuilder {...defaultProps} savedTemplates={templates} />)

    expect(screen.getByText('Weekly Summary')).toBeInTheDocument()
    expect(screen.getByText('Department Analysis')).toBeInTheDocument()
  })

  it('loads template when selected', async () => {
    const templates = [
      { 
        id: '1', 
        name: 'Weekly Summary', 
        config: {
          ...mockReportConfig,
          title: 'Weekly Training Summary',
        },
      },
    ]

    const { user } = render(<ReportBuilder {...defaultProps} savedTemplates={templates} />)

    const templateButton = screen.getByText('Weekly Summary')
    await user.click(templateButton)

    expect(defaultProps.onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Weekly Training Summary',
      })
    )
  })

  it('allows saving current configuration as template', async () => {
    const onSaveTemplate = vi.fn()
    const { user } = render(
      <ReportBuilder {...defaultProps} onSaveTemplate={onSaveTemplate} />
    )

    const saveButton = screen.getByRole('button', { name: /save as template/i })
    await user.click(saveButton)

    const templateNameInput = screen.getByLabelText(/template name/i)
    await user.type(templateNameInput, 'My Custom Report')

    const confirmButton = screen.getByRole('button', { name: /save/i })
    await user.click(confirmButton)

    expect(onSaveTemplate).toHaveBeenCalledWith({
      name: 'My Custom Report',
      config: mockReportConfig,
    })
  })

  it('clears all filters when Clear Filters button is clicked', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const clearButton = screen.getByRole('button', { name: /clear filters/i })
    await user.click(clearButton)

    expect(defaultProps.onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        filters: {},
      })
    )
  })
})