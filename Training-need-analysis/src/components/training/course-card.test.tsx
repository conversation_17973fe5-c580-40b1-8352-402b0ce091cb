import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { CourseCard } from './course-card'

describe('CourseCard', () => {
  const mockCourse = {
    id: '1',
    title: 'React Fundamentals',
    description: 'Learn the basics of React development',
    type: 'online',
    difficulty: 'beginner',
    duration: '3 hours',
    instructor: '<PERSON>',
    category: 'Frontend',
    skills: ['React', 'JavaScript', 'JSX'],
    imageUrl: 'https://example.com/react.jpg',
    price: 49.99,
    currency: 'USD',
  }

  const defaultProps = {
    course: mockCourse,
    onSelect: vi.fn(),
    onEnroll: vi.fn(),
  }

  it('renders course information correctly', () => {
    render(<CourseCard {...defaultProps} />)

    expect(screen.getByText('React Fundamentals')).toBeInTheDocument()
    expect(screen.getByText('Learn the basics of React development')).toBeInTheDocument()
    expect(screen.getByText('<PERSON>')).toBeInTheDocument()
    expect(screen.getByText('3 hours')).toBeInTheDocument()
    expect(screen.getByText('$49.99')).toBeInTheDocument()
  })

  it('displays difficulty badge with correct variant', () => {
    render(<CourseCard {...defaultProps} />)

    const difficultyBadge = screen.getByText('beginner')
    expect(difficultyBadge).toBeInTheDocument()
    expect(difficultyBadge).toHaveClass('badge-success')
  })

  it('displays intermediate difficulty with warning variant', () => {
    const intermediateCourse = { ...mockCourse, difficulty: 'intermediate' }
    render(<CourseCard {...defaultProps} course={intermediateCourse} />)

    const difficultyBadge = screen.getByText('intermediate')
    expect(difficultyBadge).toHaveClass('badge-warning')
  })

  it('displays advanced difficulty with danger variant', () => {
    const advancedCourse = { ...mockCourse, difficulty: 'advanced' }
    render(<CourseCard {...defaultProps} course={advancedCourse} />)

    const difficultyBadge = screen.getByText('advanced')
    expect(difficultyBadge).toHaveClass('badge-danger')
  })

  it('shows course type icon for online courses', () => {
    render(<CourseCard {...defaultProps} />)

    // Check for online course icon (computer/monitor icon)
    const icon = screen.getByRole('img', { hidden: true })
    expect(icon).toBeInTheDocument()
  })

  it('shows workshop icon for workshop courses', () => {
    const workshopCourse = { ...mockCourse, type: 'workshop' }
    render(<CourseCard {...defaultProps} course={workshopCourse} />)

    // Icon should be present
    const icon = screen.getByRole('img', { hidden: true })
    expect(icon).toBeInTheDocument()
  })

  it('displays all skills', () => {
    render(<CourseCard {...defaultProps} />)

    expect(screen.getByText('React')).toBeInTheDocument()
    expect(screen.getByText('JavaScript')).toBeInTheDocument()
    expect(screen.getByText('JSX')).toBeInTheDocument()
  })

  it('calls onSelect when card is clicked', async () => {
    const { user } = render(<CourseCard {...defaultProps} />)

    const card = screen.getByRole('article')
    await user.click(card)

    expect(defaultProps.onSelect).toHaveBeenCalledWith(mockCourse)
  })

  it('shows Enroll button when not enrolled', () => {
    render(<CourseCard {...defaultProps} isEnrolled={false} />)

    const enrollButton = screen.getByRole('button', { name: /enroll now/i })
    expect(enrollButton).toBeInTheDocument()
  })

  it('shows Continue button when enrolled', () => {
    render(<CourseCard {...defaultProps} isEnrolled={true} progress={50} />)

    const continueButton = screen.getByRole('button', { name: /continue learning/i })
    expect(continueButton).toBeInTheDocument()
  })

  it('shows progress bar when enrolled', () => {
    render(<CourseCard {...defaultProps} isEnrolled={true} progress={75} />)

    const progressBar = screen.getByRole('progressbar')
    expect(progressBar).toBeInTheDocument()
    expect(progressBar).toHaveAttribute('aria-valuenow', '75')
  })

  it('calls onEnroll when Enroll button is clicked', async () => {
    const { user } = render(<CourseCard {...defaultProps} isEnrolled={false} />)

    const enrollButton = screen.getByRole('button', { name: /enroll now/i })
    await user.click(enrollButton)

    expect(defaultProps.onEnroll).toHaveBeenCalledWith(mockCourse.id)
  })

  it('renders in compact mode', () => {
    render(<CourseCard {...defaultProps} compact={true} />)

    // In compact mode, certain elements might be hidden
    expect(screen.getByText('React Fundamentals')).toBeInTheDocument()
    // Description might be truncated or hidden in compact mode
  })

  it('handles missing optional fields gracefully', () => {
    const minimalCourse = {
      id: '2',
      title: 'Basic Course',
      description: 'A simple course',
      type: 'online',
      difficulty: 'beginner',
      duration: '1 hour',
    }

    render(<CourseCard {...defaultProps} course={minimalCourse} />)

    expect(screen.getByText('Basic Course')).toBeInTheDocument()
    expect(screen.getByText('A simple course')).toBeInTheDocument()
  })

  it('displays course image when provided', () => {
    render(<CourseCard {...defaultProps} />)

    const image = screen.getByAltText('React Fundamentals')
    expect(image).toBeInTheDocument()
    expect(image).toHaveAttribute('src', 'https://example.com/react.jpg')
  })

  it('shows placeholder when image is not provided', () => {
    const courseWithoutImage = { ...mockCourse, imageUrl: undefined }
    render(<CourseCard {...defaultProps} course={courseWithoutImage} />)

    // Should show a placeholder or default image
    const placeholder = screen.getByRole('img', { hidden: true })
    expect(placeholder).toBeInTheDocument()
  })

  it('formats price correctly with currency', () => {
    render(<CourseCard {...defaultProps} />)

    expect(screen.getByText('$49.99')).toBeInTheDocument()
  })

  it('shows free badge when price is 0', () => {
    const freeCourse = { ...mockCourse, price: 0 }
    render(<CourseCard {...defaultProps} course={freeCourse} />)

    expect(screen.getByText('Free')).toBeInTheDocument()
  })

  it('handles unknown course type gracefully', () => {
    const unknownTypeCourse = { ...mockCourse, type: 'unknown' }
    render(<CourseCard {...defaultProps} course={unknownTypeCourse} />)

    // Should render without crashing and show a default icon
    expect(screen.getByText('React Fundamentals')).toBeInTheDocument()
  })

  it('disables enroll button when already enrolled', () => {
    render(<CourseCard {...defaultProps} isEnrolled={true} progress={100} />)

    // When course is completed, button might be disabled or show different text
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
  })
})