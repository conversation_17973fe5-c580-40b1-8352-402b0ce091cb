import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { TrainingDashboard } from './TrainingDashboard'
import { useAuthStore } from '@luminar/shared-auth'
import { useTrainingStore } from '../../stores/trainingStore'

// Mock the stores
vi.mock('@luminar/shared-auth', () => ({
  useAuthStore: vi.fn(),
}))

vi.mock('../../stores/trainingStore', () => ({
  useTrainingStore: vi.fn(),
}))

// Mock the child components
vi.mock('./analytics-overview', () => ({
  AnalyticsOverview: () => <div data-testid="analytics-overview">Analytics Overview</div>,
}))

vi.mock('./recent-activity', () => ({
  RecentActivity: () => <div data-testid="recent-activity">Recent Activity</div>,
}))

vi.mock('./training-heatmap', () => ({
  TrainingHeatmap: () => <div data-testid="training-heatmap">Training Heatmap</div>,
}))

describe('TrainingDashboard', () => {
  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  }

  const mockCourses = [
    {
      id: '1',
      title: 'React Fundamentals',
      description: 'Learn React basics',
      skills: ['React', 'JavaScript'],
    },
    {
      id: '2',
      title: 'TypeScript Advanced',
      description: 'Master TypeScript',
      skills: ['TypeScript', 'JavaScript'],
    },
  ]

  const mockEnrollments = [
    {
      id: 'e1',
      userId: 'user-1',
      courseId: '1',
      status: 'completed',
      progress: 100,
      enrolledAt: new Date().toISOString(),
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'e2',
      userId: 'user-1',
      courseId: '2',
      status: 'in_progress',
      progress: 50,
      enrolledAt: new Date().toISOString(),
      deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ]

  const mockAssessments = [
    {
      id: 'a1',
      userId: 'user-1',
      courseId: '1',
      score: 85,
    },
    {
      id: 'a2',
      userId: 'user-1',
      courseId: '2',
      score: 90,
    },
  ]

  const mockTrainingStore = {
    courses: mockCourses,
    enrollments: mockEnrollments,
    assessments: mockAssessments,
    analytics: {},
    fetchCourses: vi.fn().mockResolvedValue(undefined),
    fetchEnrollments: vi.fn().mockResolvedValue(undefined),
    fetchAssessments: vi.fn().mockResolvedValue(undefined),
    fetchAnalytics: vi.fn().mockResolvedValue(undefined),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAuthStore as any).mockReturnValue({ user: mockUser })
    ;(useTrainingStore as any).mockReturnValue(mockTrainingStore)
  })

  it('renders loading state initially', () => {
    render(<TrainingDashboard />)
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('fetches all data on mount', async () => {
    render(<TrainingDashboard />)

    await waitFor(() => {
      expect(mockTrainingStore.fetchCourses).toHaveBeenCalled()
      expect(mockTrainingStore.fetchEnrollments).toHaveBeenCalled()
      expect(mockTrainingStore.fetchAssessments).toHaveBeenCalled()
      expect(mockTrainingStore.fetchAnalytics).toHaveBeenCalled()
    })
  })

  it('displays dashboard statistics correctly', async () => {
    render(<TrainingDashboard />)

    await waitFor(() => {
      // Total courses
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('Total Courses')).toBeInTheDocument()

      // Completed courses
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('Completed Courses')).toBeInTheDocument()

      // In progress courses
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('In Progress')).toBeInTheDocument()

      // Average score
      expect(screen.getByText('88%')).toBeInTheDocument()
      expect(screen.getByText('Average Score')).toBeInTheDocument()
    })
  })

  it('calculates completion rate correctly', async () => {
    render(<TrainingDashboard />)

    await waitFor(() => {
      // 1 completed out of 2 enrollments = 50%
      expect(screen.getByText('50%')).toBeInTheDocument()
      expect(screen.getByText('Completion Rate')).toBeInTheDocument()
    })
  })

  it('shows upcoming deadlines', async () => {
    render(<TrainingDashboard />)

    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('Upcoming Deadlines')).toBeInTheDocument()
    })
  })

  it('calculates skills gained correctly', async () => {
    render(<TrainingDashboard />)

    await waitFor(() => {
      // Only completed course has 2 skills
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('Skills Gained')).toBeInTheDocument()
    })
  })

  it('renders all dashboard sections', async () => {
    render(<TrainingDashboard />)

    await waitFor(() => {
      expect(screen.getByTestId('analytics-overview')).toBeInTheDocument()
      expect(screen.getByTestId('recent-activity')).toBeInTheDocument()
      expect(screen.getByTestId('training-heatmap')).toBeInTheDocument()
    })
  })

  it('handles error state gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    mockTrainingStore.fetchCourses.mockRejectedValueOnce(new Error('API Error'))

    render(<TrainingDashboard />)

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error loading dashboard data:',
        expect.any(Error)
      )
    })

    consoleErrorSpy.mockRestore()
  })

  it('filters enrollments by current user', async () => {
    const otherUserEnrollment = {
      id: 'e3',
      userId: 'user-2',
      courseId: '1',
      status: 'completed',
      progress: 100,
      enrolledAt: new Date().toISOString(),
      deadline: new Date().toISOString(),
    }

    ;(useTrainingStore as any).mockReturnValue({
      ...mockTrainingStore,
      enrollments: [...mockEnrollments, otherUserEnrollment],
    })

    render(<TrainingDashboard />)

    await waitFor(() => {
      // Should still show only 1 completed course for the current user
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('Completed Courses')).toBeInTheDocument()
    })
  })

  it('handles empty data state', async () => {
    ;(useTrainingStore as any).mockReturnValue({
      ...mockTrainingStore,
      courses: [],
      enrollments: [],
      assessments: [],
    })

    render(<TrainingDashboard />)

    await waitFor(() => {
      expect(screen.getByText('0')).toBeInTheDocument()
      expect(screen.getByText('Total Courses')).toBeInTheDocument()
    })
  })
})