/// <reference types="vinxi/types/client" />
import { hydrateRoot } from 'react-dom/client'
import { StartClient } from '@tanstack/react-start'
import { createRouter } from './router'
import { ThemeProvider, apiClient } from '@luminar/shared-ui'

async function enableMocking() {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  const { worker } = await import('./mocks/browser')
  
  return worker.start({
    onUnhandledRequest: 'bypass',
  })
}

enableMocking().then(() => {
  // Configure API client
  apiClient.setBaseURL(import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api')
  
  const router = createRouter()
  hydrateRoot(
    document, 
    <ThemeProvider>
      <StartClient router={router} />
    </ThemeProvider>
  )
})
