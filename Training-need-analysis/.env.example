# ===================================================================
# Training Need Analysis (TNA) - Environment Configuration
# ===================================================================
# Training Need Analysis is a React-based platform for skills assessment,
# gap analysis, and training recommendations.

# ===================================================================
# Application Configuration
# ===================================================================
VITE_APP_TITLE=Training Need Analysis Platform
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=development

# ===================================================================
# API Configuration
# ===================================================================
# Main backend API endpoint (Command-Center)
VITE_API_BASE_URL=http://localhost:3000/api

# TNA-specific endpoints
VITE_ASSESSMENTS_API_URL=http://localhost:3000/api/assessments
VITE_SKILLS_API_URL=http://localhost:3000/api/skills
VITE_TRAINING_API_URL=http://localhost:3000/api/training
VITE_REPORTS_API_URL=http://localhost:3000/api/reports

# API settings
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RATE_LIMIT=200

# ===================================================================
# Authentication Configuration
# ===================================================================
# User authentication
VITE_AUTH_TOKEN_KEY=tna_auth_token
VITE_SESSION_TIMEOUT=60
VITE_ENABLE_SSO=true

# Role-based access
VITE_ENABLE_ROLE_BASED_ACCESS=true
VITE_DEFAULT_USER_ROLE=employee
VITE_ADMIN_ROLES=admin,hr_manager,training_manager

# ===================================================================
# Assessment Configuration
# ===================================================================
# Assessment types
VITE_ENABLE_SELF_ASSESSMENT=true
VITE_ENABLE_PEER_ASSESSMENT=true
VITE_ENABLE_MANAGER_ASSESSMENT=true
VITE_ENABLE_360_ASSESSMENT=true

# Assessment settings
VITE_MAX_ASSESSMENT_DURATION=3600
VITE_ENABLE_ASSESSMENT_TIMER=true
VITE_ALLOW_ASSESSMENT_PAUSE=true
VITE_AUTO_SAVE_INTERVAL=30000

# Question types
VITE_ENABLE_MULTIPLE_CHOICE=true
VITE_ENABLE_RATING_SCALE=true
VITE_ENABLE_TEXT_RESPONSES=true
VITE_ENABLE_FILE_UPLOADS=true

# ===================================================================
# Skills Management
# ===================================================================
# Skills framework
VITE_ENABLE_COMPETENCY_FRAMEWORK=true
VITE_MAX_SKILL_LEVELS=5
VITE_SKILL_LEVEL_NAMES=Novice,Basic,Intermediate,Advanced,Expert

# Skills assessment
VITE_ENABLE_SKILL_MATRIX=true
VITE_ENABLE_SKILL_GAPS_ANALYSIS=true
VITE_ENABLE_SKILL_RECOMMENDATIONS=true

# Skills categories
VITE_ENABLE_TECHNICAL_SKILLS=true
VITE_ENABLE_SOFT_SKILLS=true
VITE_ENABLE_LEADERSHIP_SKILLS=true
VITE_ENABLE_DOMAIN_SKILLS=true

# ===================================================================
# Training Recommendations
# ===================================================================
# Recommendation engine
VITE_ENABLE_AI_RECOMMENDATIONS=true
VITE_RECOMMENDATION_ALGORITHM=collaborative_filtering
VITE_MAX_RECOMMENDATIONS=10

# Training types
VITE_ENABLE_ONLINE_TRAINING=true
VITE_ENABLE_CLASSROOM_TRAINING=true
VITE_ENABLE_WORKSHOP_TRAINING=true
VITE_ENABLE_MENTORING=true

# Learning paths
VITE_ENABLE_LEARNING_PATHS=true
VITE_MAX_PATH_DURATION_MONTHS=12
VITE_ENABLE_ADAPTIVE_LEARNING=true

# ===================================================================
# Reporting and Analytics
# ===================================================================
# Report types
VITE_ENABLE_INDIVIDUAL_REPORTS=true
VITE_ENABLE_TEAM_REPORTS=true
VITE_ENABLE_DEPARTMENT_REPORTS=true
VITE_ENABLE_ORGANIZATION_REPORTS=true

# Analytics features
VITE_ENABLE_DASHBOARD_ANALYTICS=true
VITE_ENABLE_TREND_ANALYSIS=true
VITE_ENABLE_BENCHMARKING=true

# Export options
VITE_ENABLE_REPORT_EXPORT=true
VITE_EXPORT_FORMATS=pdf,excel,csv,ppt

# ===================================================================
# Department and Team Management
# ===================================================================
# Organizational structure
VITE_ENABLE_DEPARTMENT_ANALYSIS=true
VITE_ENABLE_TEAM_COMPARISON=true
VITE_ENABLE_ROLE_BASED_ANALYSIS=true

# Hierarchy management
VITE_MAX_ORGANIZATION_LEVELS=10
VITE_ENABLE_MANAGER_DASHBOARD=true
VITE_ENABLE_TEAM_OVERVIEW=true

# ===================================================================
# Training Metrics
# ===================================================================
# Effectiveness tracking
VITE_ENABLE_TRAINING_EFFECTIVENESS=true
VITE_ENABLE_ROI_CALCULATION=true
VITE_ENABLE_COMPLETION_TRACKING=true

# Performance metrics
VITE_ENABLE_BEFORE_AFTER_COMPARISON=true
VITE_ENABLE_SKILL_IMPROVEMENT_TRACKING=true
VITE_ENABLE_PERFORMANCE_CORRELATION=true

# ===================================================================
# Learning Pathways
# ===================================================================
# Pathway management
VITE_ENABLE_CUSTOM_PATHWAYS=true
VITE_ENABLE_PREREQUISITE_TRACKING=true
VITE_ENABLE_PATHWAY_RECOMMENDATIONS=true

# Progress tracking
VITE_ENABLE_MILESTONE_TRACKING=true
VITE_ENABLE_COMPLETION_CERTIFICATES=true
VITE_ENABLE_BADGE_SYSTEM=true

# ===================================================================
# UI/UX Configuration
# ===================================================================
# Theme and appearance
VITE_DEFAULT_THEME=light
VITE_ENABLE_THEME_SWITCHING=true
VITE_ENABLE_DARK_MODE=true

# Dashboard configuration
VITE_DEFAULT_DASHBOARD=overview
VITE_ENABLE_DASHBOARD_CUSTOMIZATION=true
VITE_WIDGET_REFRESH_INTERVAL=300000

# Data visualization
VITE_ENABLE_CHARTS=true
VITE_CHART_LIBRARY=recharts
VITE_ENABLE_INTERACTIVE_CHARTS=true

# ===================================================================
# Notification Configuration
# ===================================================================
# Assessment notifications
VITE_ENABLE_ASSESSMENT_REMINDERS=true
VITE_REMINDER_FREQUENCY_DAYS=7
VITE_ENABLE_DEADLINE_NOTIFICATIONS=true

# Training notifications
VITE_ENABLE_TRAINING_NOTIFICATIONS=true
VITE_ENABLE_COMPLETION_NOTIFICATIONS=true
VITE_ENABLE_RECOMMENDATION_NOTIFICATIONS=true

# ===================================================================
# Integration Configuration
# ===================================================================
# LMS integration
VITE_ENABLE_LMS_INTEGRATION=false
VITE_LMS_PROVIDER=moodle
VITE_LMS_API_URL=

# HR system integration
VITE_ENABLE_HR_INTEGRATION=false
VITE_HR_SYSTEM_API_URL=
VITE_ENABLE_EMPLOYEE_SYNC=false

# Calendar integration
VITE_ENABLE_CALENDAR_INTEGRATION=false
VITE_CALENDAR_PROVIDER=google

# ===================================================================
# External Services
# ===================================================================
# Error tracking
VITE_SENTRY_DSN=

# Analytics
VITE_GOOGLE_ANALYTICS_ID=
VITE_ENABLE_USER_ANALYTICS=true

# Support
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_HELP_DESK_URL=

# ===================================================================
# Cross-App Integration
# ===================================================================
# Integration with other Luminar applications
VITE_AMNA_URL=http://localhost:5173
VITE_ECONNECT_URL=http://localhost:5174
VITE_LIGHTHOUSE_URL=http://localhost:5175
VITE_DASHBOARD_URL=http://localhost:5176
VITE_VENDORS_URL=http://localhost:5178
VITE_WINS_URL=http://localhost:5179

# Shared services
VITE_SHARED_AUTH_ENABLED=true
VITE_SHARED_AUTH_DOMAIN=localhost
VITE_CROSS_APP_NAVIGATION=true

# ===================================================================
# Development Configuration
# ===================================================================
# Development server
VITE_DEV_PORT=5177
VITE_HMR_PORT=5177

# Development tools
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_ROUTER_DEVTOOLS=true

# Debug settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
VITE_ENABLE_MOCK_DATA=false

# ===================================================================
# Testing Configuration
# ===================================================================
# Test environment
VITE_TEST_MODE=false
VITE_ENABLE_MSW=false
VITE_TEST_API_URL=http://localhost:3001/api

# Test data
VITE_USE_MOCK_ASSESSMENTS=false
VITE_MOCK_EMPLOYEES_COUNT=50
VITE_MOCK_ASSESSMENTS_COUNT=25

# ===================================================================
# Performance Configuration
# ===================================================================
# Caching
VITE_CACHE_STRATEGY=memory
VITE_CACHE_DURATION=1800000
VITE_ENABLE_OFFLINE_MODE=false

# Loading optimization
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_PAGINATION_SIZE=20

# ===================================================================
# Security Configuration
# ===================================================================
# Data protection
VITE_ENABLE_DATA_ENCRYPTION=true
VITE_ENABLE_SECURE_STORAGE=true
VITE_ENABLE_CSP=true

# Privacy
VITE_ENABLE_ANONYMIZATION=true
VITE_DATA_RETENTION_MONTHS=36

# ===================================================================
# Build Configuration
# ===================================================================
# Build settings
VITE_BUILD_OUTPUT_DIR=dist
VITE_PUBLIC_PATH=/

# Optimization
VITE_ENABLE_MINIFICATION=true
VITE_ENABLE_COMPRESSION=true
VITE_GENERATE_SOURCE_MAPS=true

# Bundle analysis
VITE_BUNDLE_ANALYZER=false
VITE_CHUNK_SIZE_WARNING_LIMIT=500