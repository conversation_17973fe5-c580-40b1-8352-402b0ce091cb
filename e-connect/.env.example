# ===================================================================
# E-Connect (Email Management Platform) - Environment Configuration
# ===================================================================
# E-Connect is a React-based email management application with Gmail
# integration, analytics, and multi-account support.

# ===================================================================
# Application Configuration
# ===================================================================
VITE_APP_TITLE=E-Connect Email Platform
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development

# ===================================================================
# API Configuration
# ===================================================================
# Main backend API endpoint (Command-Center)
VITE_API_URL=http://localhost:3000/api

# Email service endpoints
VITE_EMAIL_API_URL=http://localhost:3000/api/email
VITE_ANALYTICS_API_URL=http://localhost:3000/api/analytics

# API timeout settings (in milliseconds)
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RATE_LIMIT=100

# ===================================================================
# Authentication Configuration
# ===================================================================
# OAuth configuration for Gmail integration
VITE_GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
VITE_GOOGLE_CLIENT_SECRET=your-google-client-secret
VITE_OAUTH_REDIRECT_URI=http://localhost:5174/oauth-callback

# OAuth scopes for Gmail API
VITE_GMAIL_SCOPES=https://www.googleapis.com/auth/gmail.readonly,https://www.googleapis.com/auth/gmail.send,https://www.googleapis.com/auth/gmail.modify

# JWT token storage
VITE_AUTH_TOKEN_KEY=econnect_auth_token
VITE_SESSION_TIMEOUT=60

# Multi-account support
VITE_MAX_ACCOUNTS=5
VITE_ENABLE_ACCOUNT_SWITCHING=true

# ===================================================================
# Gmail Integration Configuration
# ===================================================================
# Gmail API settings
VITE_GMAIL_API_VERSION=v1
VITE_GMAIL_BATCH_SIZE=100
VITE_GMAIL_SYNC_INTERVAL=300000

# Email processing
VITE_MAX_EMAILS_PER_REQUEST=50
VITE_EMAIL_CACHE_DURATION=3600000
VITE_ENABLE_EMAIL_THREADING=true

# Attachment handling
VITE_MAX_ATTACHMENT_SIZE=********
VITE_ALLOWED_ATTACHMENT_TYPES=image/*,text/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# ===================================================================
# Email Features Configuration
# ===================================================================
# AI Assistant integration
VITE_ENABLE_AI_ASSISTANT=true
VITE_AI_SUGGESTIONS_ENABLED=true
VITE_AI_RESPONSE_TEMPLATES=true

# Bulk operations
VITE_ENABLE_BULK_OPERATIONS=true
VITE_BULK_OPERATION_LIMIT=1000

# Cold email features
VITE_ENABLE_COLD_EMAIL=true
VITE_COLD_EMAIL_TRACKING=true
VITE_COLD_EMAIL_TEMPLATES=true

# Email rules and automation
VITE_ENABLE_EMAIL_RULES=true
VITE_MAX_RULES_PER_ACCOUNT=50
VITE_ENABLE_AUTO_LABELING=true

# ===================================================================
# Analytics Configuration
# ===================================================================
# Email analytics
VITE_ENABLE_ANALYTICS=true
VITE_ANALYTICS_RETENTION_DAYS=90
VITE_ENABLE_PERFORMANCE_TRACKING=true

# Dashboard features
VITE_ENABLE_DASHBOARD=true
VITE_DASHBOARD_REFRESH_INTERVAL=60000
VITE_MAX_CHART_DATA_POINTS=100

# Reporting
VITE_ENABLE_REPORTS=true
VITE_REPORT_EXPORT_FORMATS=pdf,csv,xlsx

# ===================================================================
# UI/UX Configuration
# ===================================================================
# Theme and appearance
VITE_DEFAULT_THEME=light
VITE_ENABLE_THEME_SWITCHING=true
VITE_ENABLE_DARK_MODE=true

# Email list configuration
VITE_EMAILS_PER_PAGE=25
VITE_ENABLE_INFINITE_SCROLL=true
VITE_ENABLE_EMAIL_PREVIEW=true

# Search and filtering
VITE_ENABLE_ADVANCED_SEARCH=true
VITE_SEARCH_HISTORY_LIMIT=10
VITE_SEARCH_DEBOUNCE_MS=300

# Keyboard shortcuts
VITE_ENABLE_KEYBOARD_SHORTCUTS=true
VITE_KEYBOARD_SHORTCUTS_HELP=true

# ===================================================================
# Performance Configuration
# ===================================================================
# Caching strategies
VITE_CACHE_STRATEGY=memory
VITE_CACHE_SIZE_LIMIT=50485760
VITE_ENABLE_OFFLINE_MODE=true

# Lazy loading
VITE_ENABLE_LAZY_LOADING=true
VITE_IMAGE_LAZY_LOADING=true
VITE_VIRTUAL_SCROLLING=true

# Background sync
VITE_ENABLE_BACKGROUND_SYNC=true
VITE_SYNC_INTERVAL=60000

# ===================================================================
# Security Configuration
# ===================================================================
# Email security
VITE_ENABLE_EMAIL_ENCRYPTION=false
VITE_ENABLE_SPAM_DETECTION=true
VITE_ENABLE_PHISHING_PROTECTION=true

# Data protection
VITE_ENABLE_DATA_ENCRYPTION=true
VITE_SECURE_STORAGE=true
VITE_ENABLE_CSP=true

# Privacy settings
VITE_ENABLE_TRACKING_PROTECTION=true
VITE_ANONYMOUS_ANALYTICS=false

# ===================================================================
# External Services
# ===================================================================
# Sentry for error tracking
VITE_SENTRY_DSN=

# Analytics services
VITE_GOOGLE_ANALYTICS_ID=
VITE_MIXPANEL_TOKEN=

# Support and feedback
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_FEEDBACK_URL=
VITE_DOCUMENTATION_URL=

# ===================================================================
# Cross-App Integration
# ===================================================================
# Integration with other Luminar applications
VITE_AMNA_URL=http://localhost:5173
VITE_LIGHTHOUSE_URL=http://localhost:5175
VITE_DASHBOARD_URL=http://localhost:5176
VITE_TRAINING_URL=http://localhost:5177
VITE_VENDORS_URL=http://localhost:5178
VITE_WINS_URL=http://localhost:5179

# Shared authentication
VITE_SHARED_AUTH_ENABLED=true
VITE_SHARED_AUTH_DOMAIN=localhost
VITE_CROSS_APP_NAVIGATION=true

# ===================================================================
# Development Configuration
# ===================================================================
# Development server
VITE_DEV_PORT=5174
VITE_HMR_PORT=5174

# Development tools
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_QUERY_DEVTOOLS=true

# Debug settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
VITE_ENABLE_CONSOLE_LOGS=true

# ===================================================================
# Testing Configuration
# ===================================================================
# Test environment
VITE_TEST_MODE=false
VITE_ENABLE_MSW=false
VITE_TEST_API_URL=http://localhost:3001/api

# Mock data
VITE_USE_MOCK_EMAILS=false
VITE_MOCK_USER_COUNT=5
VITE_MOCK_EMAIL_COUNT=100

# ===================================================================
# Build Configuration
# ===================================================================
# Build settings
VITE_BUILD_OUTPUT_DIR=dist
VITE_PUBLIC_PATH=/

# Optimization
VITE_ENABLE_MINIFICATION=true
VITE_ENABLE_COMPRESSION=true
VITE_GENERATE_SOURCE_MAPS=true

# Bundle analysis
VITE_BUNDLE_ANALYZER=false
VITE_CHUNK_SIZE_WARNING_LIMIT=500