{"name": "e-connect-2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@luminar/shared-ui": "file:../shared-ui", "@luminar/shared-auth": "file:../shared-auth", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-router": "^1.125.6", "@tanstack/react-router-devtools": "^1.127.3", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.9", "@tanstack/router-vite-plugin": "^1.125.6", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "immer": "^10.1.1", "jotai": "^2.12.5", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-hotkeys-hook": "^5.1.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "react-virtual": "^2.10.4", "recharts": "^2.12.7", "socket.io-client": "^4.7.5", "sonner": "^2.0.4", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "zod": "^3.25.46", "zustand": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "msw": "^2.10.3", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "msw": {"workerDirectory": ["public"]}}