import { createRootRoute, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { Suspense, useEffect } from 'react'
import { Navigation } from '../components/layout/Navigation'
import { KeyboardShortcutsWrapper } from '../components/ui/KeyboardShortcutsWrapper'
import { useSettingsStore } from '../stores/settingsStore'
import { useMultiAccountStore } from '../stores/multiAccountStore'
import { useAnalyticsStore } from '../stores/analyticsStore'
import { useUIStore } from '../stores/uiStore'
import { cn } from '../lib/utils'
import { AMNAWidget, ThemeProvider, IntegrationProvider } from '@luminar/shared-ui'


export const Route = createRootRoute({
  component: RootComponent,
})

function RootComponent() {
  const { theme, keyboardShortcutsEnabled, initializeSettings } = useSettingsStore()
  const { initializeMultiAccount } = useMultiAccountStore()
  const { initializeAnalytics } = useAnalyticsStore()
  const { toasts, dismissToast, globalLoading, loadingMessage } = useUIStore()

  // Initialize stores on mount
  useEffect(() => {
    initializeSettings()
    initializeMultiAccount()
    initializeAnalytics()
  }, [])

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }, [theme])

  return (
    <ThemeProvider>
      <IntegrationProvider>
        <div className={cn(
          "min-h-screen transition-colors duration-200",
          "bg-gray-50 dark:bg-gray-900"
        )}>
      <div className="flex h-screen">
        {/* Navigation */}
        <Navigation />

        {/* Main content */}
        <div className="flex-1 flex flex-col overflow-hidden md:ml-64">
          {/* Mobile top padding */}
          <div className="md:hidden h-16" />
          
          <main className="flex-1 relative overflow-y-auto focus:outline-none">
            <Suspense fallback={
              <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
                    Loading...
                  </p>
                </div>
              </div>
            }>
              <Outlet />
            </Suspense>
          </main>
          
          {/* Mobile bottom padding */}
          <div className="md:hidden h-16" />
        </div>
      </div>

      {/* Global Loading Overlay */}
      {globalLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl flex flex-col items-center space-y-4 max-w-sm mx-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
            <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
              {loadingMessage || 'Loading...'}
            </p>
          </div>
        </div>
      )}

      {/* Custom Toast Notifications */}
      <div className="fixed top-4 right-4 z-40 space-y-2">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={cn(
              "max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5",
              "transform transition-all duration-300 ease-in-out",
              toast.type === 'success' && "border-l-4 border-green-500",
              toast.type === 'error' && "border-l-4 border-red-500",
              toast.type === 'warning' && "border-l-4 border-yellow-500",
              toast.type === 'info' && "border-l-4 border-blue-500"
            )}
          >
            <div className="p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {toast.type === 'success' && (
                    <div className="h-5 w-5 text-green-400">✓</div>
                  )}
                  {toast.type === 'error' && (
                    <div className="h-5 w-5 text-red-400">✕</div>
                  )}
                  {toast.type === 'warning' && (
                    <div className="h-5 w-5 text-yellow-400">⚠</div>
                  )}
                  {toast.type === 'info' && (
                    <div className="h-5 w-5 text-blue-400">ℹ</div>
                  )}
                </div>
                <div className="ml-3 w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {toast.message}
                  </p>
                  {toast.action && (
                    <div className="mt-3 flex space-x-7">
                      <button
                        type="button"
                        onClick={toast.action.onClick}
                        className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500"
                      >
                        {toast.action.label}
                      </button>
                    </div>
                  )}
                </div>
                <div className="ml-4 flex-shrink-0 flex">
                  <button
                    className="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    onClick={() => dismissToast(toast.id)}
                  >
                    <span className="sr-only">Close</span>
                    <div className="h-5 w-5">✕</div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

          {/* Global Components */}
          <KeyboardShortcutsWrapper />
          
          {/* AMNAWidget for cross-app AI assistance */}
          <AMNAWidget
            position="bottom-left"
            context={{ 
              app: 'e-connect',
              page: typeof window !== 'undefined' ? window.location.pathname : '/',
              environment: 'production'
            }}
            showBadge={true}
          />
          
          {/* Development Tools */}
          {import.meta.env.DEV && <TanStackRouterDevtools position="bottom-right" />}
        </div>
      </IntegrationProvider>
    </ThemeProvider>
  )
}
