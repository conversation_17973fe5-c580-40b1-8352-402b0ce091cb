import React, { useEffect, useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu'
import { 
  Mail, 
  MoreHorizontal, 
  Settings, 
  Trash2, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Clock,
  Wifi,
  WifiOff,
  Plus
} from 'lucide-react'
import { Alert, AlertDescription } from '../ui/alert'
import { Separator } from '../ui/separator'
import { useEmailStore } from '../../stores/emailStore'
import { ConnectAccountDialog } from './ConnectAccountDialog'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'

interface EmailAccount {
  id: string
  email: string
  displayName?: string
  provider: string
  status: 'active' | 'inactive' | 'error' | 'reauth_required'
  isActive: boolean
  syncEnabled: boolean
  lastSyncAt?: string
  lastSuccessfulSyncAt?: string
  lastSyncError?: string
  syncStats?: {
    totalEmailsSynced: number
    successfulSyncs: number
    failedSyncs: number
  }
  createdAt: string
}

export const AccountList: React.FC = () => {
  const [showConnectDialog, setShowConnectDialog] = useState(false)
  const [syncing, setSyncing] = useState<Set<string>>(new Set())
  const {
    emailAccounts,
    currentAccountId,
    isLoading,
    error,
    getEmailAccounts,
    deleteEmailAccount,
    syncEmailAccount,
    switchToAccount,
  } = useEmailStore()

  useEffect(() => {
    getEmailAccounts().catch(console.error)
  }, [getEmailAccounts])

  const handleSync = async (accountId: string, fullSync = false) => {
    setSyncing(prev => new Set(prev).add(accountId))
    
    try {
      await syncEmailAccount(accountId, fullSync)
      toast.success('Email sync started successfully')
      
      // Refresh account list to get updated sync status
      setTimeout(() => {
        getEmailAccounts().catch(console.error)
      }, 1000)
    } catch (err) {
      console.error('Failed to sync account:', err)
      toast.error('Failed to start email sync')
    } finally {
      setSyncing(prev => {
        const newSet = new Set(prev)
        newSet.delete(accountId)
        return newSet
      })
    }
  }

  const handleDelete = async (accountId: string, email: string) => {
    if (!confirm(`Are you sure you want to disconnect ${email}? This will remove all synced data.`)) {
      return
    }

    try {
      await deleteEmailAccount(accountId)
      toast.success('Email account disconnected successfully')
    } catch (err) {
      console.error('Failed to delete account:', err)
      toast.error('Failed to disconnect email account')
    }
  }

  const getStatusBadge = (account: EmailAccount) => {
    switch (account.status) {
      case 'active':
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        )
      case 'error':
      case 'reauth_required':
        return (
          <Badge variant="destructive">
            <AlertCircle className="w-3 h-3 mr-1" />
            {account.status === 'reauth_required' ? 'Reauth Required' : 'Error'}
          </Badge>
        )
      case 'inactive':
        return (
          <Badge variant="outline">
            <Clock className="w-3 h-3 mr-1" />
            Inactive
          </Badge>
        )
      default:
        return null
    }
  }

  const getSyncIcon = (account: EmailAccount, accountId: string) => {
    if (syncing.has(accountId)) {
      return <RefreshCw className="w-4 h-4 animate-spin" />
    }
    
    if (account.syncEnabled) {
      return <Wifi className="w-4 h-4 text-green-600" />
    }
    
    return <WifiOff className="w-4 h-4 text-gray-400" />
  }

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'gmail':
        return '📧'
      case 'outlook':
        return '📨'
      case 'yahoo':
        return '📮'
      default:
        return '📬'
    }
  }

  if (isLoading && emailAccounts.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>Loading email accounts...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Email Accounts</CardTitle>
            <Button onClick={() => setShowConnectDialog(true)} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Account
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {emailAccounts.length === 0 ? (
            <div className="text-center py-8">
              <Mail className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No email accounts connected</h3>
              <p className="text-gray-500 mb-4">
                Connect your email accounts to start managing your messages
              </p>
              <Button onClick={() => setShowConnectDialog(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Connect Your First Account
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {emailAccounts.map((account) => (
                <div
                  key={account.id}
                  className={`p-4 rounded-lg border transition-all cursor-pointer hover:shadow-sm ${
                    currentAccountId === account.id
                      ? 'border-primary bg-primary/5'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => switchToAccount(account.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center text-lg">
                        {getProviderIcon(account.provider)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {account.displayName || account.email}
                          </p>
                          {getStatusBadge(account)}
                        </div>
                        <p className="text-xs text-gray-500">{account.email}</p>
                        {account.lastSyncAt && (
                          <p className="text-xs text-gray-400">
                            Last sync: {formatDistanceToNow(new Date(account.lastSyncAt), { addSuffix: true })}
                          </p>
                        )}
                        {account.lastSyncError && (
                          <p className="text-xs text-red-500 truncate">
                            Error: {account.lastSyncError}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1 text-xs text-gray-500">
                        {getSyncIcon(account, account.id)}
                        {account.syncStats && (
                          <span>{account.syncStats.totalEmailsSynced} emails</span>
                        )}
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              handleSync(account.id)
                            }}
                            disabled={syncing.has(account.id)}
                          >
                            <RefreshCw className="w-4 h-4 mr-2" />
                            {syncing.has(account.id) ? 'Syncing...' : 'Sync Now'}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              handleSync(account.id, true)
                            }}
                            disabled={syncing.has(account.id)}
                          >
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Full Sync
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                            <Settings className="w-4 h-4 mr-2" />
                            Settings
                          </DropdownMenuItem>
                          <Separator />
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDelete(account.id, account.email)
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Disconnect
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <ConnectAccountDialog
        open={showConnectDialog}
        onOpenChange={setShowConnectDialog}
      />
    </div>
  )
}