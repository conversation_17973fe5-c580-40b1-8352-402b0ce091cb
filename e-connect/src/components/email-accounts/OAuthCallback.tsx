import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from '@tanstack/react-router'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Alert, AlertDescription } from '../ui/alert'
import { Button } from '../ui/button'
import { Loader2, CheckCircle, AlertCircle, Mail } from 'lucide-react'
import { useEmailStore } from '../../stores/emailStore'
import { toast } from 'sonner'

export const OAuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing')
  const [error, setError] = useState<string | null>(null)
  const [accountInfo, setAccountInfo] = useState<any>(null)
  const { handleEmailAuthCallback } = useEmailStore()

  useEffect(() => {
    const processCallback = async () => {
      try {
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        const error = searchParams.get('error')
        const provider = searchParams.get('provider') || 'gmail' // Default to gmail

        if (error) {
          throw new Error(`OAuth error: ${error}`)
        }

        if (!code) {
          throw new Error('Authorization code not found')
        }

        // Handle the OAuth callback
        const account = await handleEmailAuthCallback(code, provider, state || undefined)
        
        setAccountInfo(account)
        setStatus('success')
        toast.success(`Successfully connected ${account.email}`)

        // Redirect to main app after a short delay
        setTimeout(() => {
          navigate({ to: '/' })
        }, 3000)

      } catch (err) {
        console.error('OAuth callback error:', err)
        setError(err instanceof Error ? err.message : 'Failed to connect email account')
        setStatus('error')
        toast.error('Failed to connect email account')
      }
    }

    processCallback()
  }, [searchParams, handleEmailAuthCallback, navigate])

  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="text-center py-8">
            <Loader2 className="w-12 h-12 mx-auto text-primary animate-spin mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Connecting your email account...
            </h3>
            <p className="text-gray-500">
              Please wait while we set up your email integration
            </p>
          </div>
        )

      case 'success':
        return (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 mx-auto text-green-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Email account connected successfully!
            </h3>
            {accountInfo && (
              <p className="text-gray-600 mb-4">
                Connected {accountInfo.email} ({accountInfo.provider})
              </p>
            )}
            <p className="text-gray-500 mb-6">
              You'll be redirected to the main app in a few seconds...
            </p>
            <Button onClick={() => navigate({ to: '/' })}>
              <Mail className="w-4 h-4 mr-2" />
              Go to Inbox
            </Button>
          </div>
        )

      case 'error':
        return (
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Connection failed
            </h3>
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <div className="space-x-3">
              <Button
                variant="outline"
                onClick={() => window.history.back()}
              >
                Go Back
              </Button>
              <Button onClick={() => navigate({ to: '/' })}>
                <Mail className="w-4 h-4 mr-2" />
                Go to Main App
              </Button>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Email Account Setup</CardTitle>
        </CardHeader>
        <CardContent>
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  )
}