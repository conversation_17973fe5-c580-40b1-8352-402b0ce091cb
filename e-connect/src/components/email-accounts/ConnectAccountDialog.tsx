import React, { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Mail, AlertCircle, Loader2 } from 'lucide-react'
import { Alert, AlertDescription } from '../ui/alert'
import { useEmailStore } from '../../stores/emailStore'
import { toast } from 'sonner'

// Provider configurations
const EMAIL_PROVIDERS = [
  {
    id: 'gmail',
    name: 'Gmail',
    description: 'Connect your Google/Gmail account',
    icon: '📧',
    color: 'bg-red-500',
    popular: true,
  },
  {
    id: 'outlook',
    name: 'Outlook',
    description: 'Connect your Microsoft Outlook account',
    icon: '📨',
    color: 'bg-blue-500',
    popular: true,
    disabled: true, // Not implemented yet
  },
  {
    id: 'yahoo',
    name: 'Yahoo Mail',
    description: 'Connect your Yahoo Mail account',
    icon: '📮',
    color: 'bg-purple-500',
    disabled: true, // Not implemented yet
  },
  {
    id: 'imap',
    name: 'IMA<PERSON>',
    description: 'Connect via IMAP (advanced)',
    icon: '⚙️',
    color: 'bg-gray-500',
    disabled: true, // Not implemented yet
  },
]

interface ConnectAccountDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export const ConnectAccountDialog: React.FC<ConnectAccountDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const [connecting, setConnecting] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const { connectEmailAccount } = useEmailStore()

  const handleConnect = async (providerId: string) => {
    if (connecting) return

    setConnecting(providerId)
    setError(null)

    try {
      await connectEmailAccount(providerId)
      toast.success('Redirecting to authentication...')
      onOpenChange(false)
    } catch (err) {
      console.error('Failed to connect account:', err)
      setError(err instanceof Error ? err.message : 'Failed to connect account')
      toast.error('Failed to start connection process')
    } finally {
      setConnecting(null)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Connect Email Account
          </DialogTitle>
          <DialogDescription>
            Choose an email provider to connect your account and start managing your emails.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-3">
            {EMAIL_PROVIDERS.map((provider) => (
              <Card
                key={provider.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  provider.disabled
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:scale-[1.02]'
                } ${
                  connecting === provider.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => !provider.disabled && handleConnect(provider.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-10 h-10 rounded-lg ${provider.color} flex items-center justify-center text-white text-lg`}
                      >
                        {provider.icon}
                      </div>
                      <div>
                        <CardTitle className="text-base flex items-center gap-2">
                          {provider.name}
                          {provider.popular && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                              Popular
                            </span>
                          )}
                          {provider.disabled && (
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                              Coming Soon
                            </span>
                          )}
                        </CardTitle>
                        <CardDescription className="text-sm">
                          {provider.description}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {connecting === provider.id && (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      )}
                      <Button
                        variant={provider.disabled ? 'ghost' : 'outline'}
                        size="sm"
                        disabled={provider.disabled || connecting === provider.id}
                        onClick={(e) => {
                          e.stopPropagation()
                          if (!provider.disabled) {
                            handleConnect(provider.id)
                          }
                        }}
                      >
                        {connecting === provider.id
                          ? 'Connecting...'
                          : provider.disabled
                          ? 'Coming Soon'
                          : 'Connect'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>

          <div className="text-sm text-muted-foreground space-y-2 pt-4 border-t">
            <p className="font-medium">What happens when you connect?</p>
            <ul className="space-y-1 text-xs">
              <li>• You'll be redirected to your email provider's secure login page</li>
              <li>• Grant permission for E-Connect to access your emails</li>
              <li>• Your emails will be synced automatically and securely</li>
              <li>• You can disconnect your account at any time in settings</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}