import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { Thread, ParsedMessage, EmailFilter, EmailSort, EmailCategory } from '../types/email'
import type { PaginationParams } from '../types/api'
import type { UnifiedThread, UnifiedInboxFilter } from '../types/multi-account'
import { emailWebSocketService } from '../services/websocket'

interface EmailState {
  // Multi-account support
  currentAccountId: string | null
  isUnifiedView: boolean
  emailAccounts: any[] // Email account objects from API
  
  // State (account-specific or unified)
  threads: Thread[] | UnifiedThread[]
  selectedThread: Thread | UnifiedThread | null
  selectedMessage: ParsedMessage | null
  isLoading: boolean
  error: string | null
  
  // Pagination
  currentPage: number
  pageSize: number
  totalThreads: number
  hasMore: boolean
  
  // Filters and sorting
  filter: EmailFilter | UnifiedInboxFilter
  sort: EmailSort
  
  // Selection
  selectedThreadIds: Set<string>
  isSelectMode: boolean
  
  // Stats (account-specific or aggregated)
  stats: {
    totalUnread: number
    totalImportant: number
    categories: Record<string, number>
    accountBreakdown?: Array<{
      accountId: string
      accountName: string
      unread: number
      total: number
    }>
  }
  
  // Actions
  fetchThreads: (params?: PaginationParams & (EmailFilter | UnifiedInboxFilter)) => Promise<void>
  fetchThread: (threadId: string, accountId?: string) => Promise<void>
  
  // Email account management
  getEmailAccounts: () => Promise<any[]>
  connectEmailAccount: (provider: string) => Promise<void>
  handleEmailAuthCallback: (code: string, provider: string, state?: string) => Promise<any>
  updateEmailAccount: (accountId: string, updateData: any) => Promise<any>
  deleteEmailAccount: (accountId: string) => Promise<void>
  syncEmailAccount: (accountId: string, fullSync?: boolean) => Promise<any>
  getSyncStatus: (accountId: string) => Promise<any>
  
  // Multi-account actions
  switchToAccount: (accountId: string | null) => void
  switchToUnifiedView: () => void
  fetchUnifiedThreads: (params?: PaginationParams & UnifiedInboxFilter) => Promise<void>
  selectThread: (threadId: string) => void
  selectMessage: (messageId: string) => void
  toggleThreadSelection: (threadId: string) => void
  selectAllThreads: () => void
  clearSelection: () => void
  setSelectMode: (enabled: boolean) => void
  
  // Thread actions
  markAsRead: (threadIds: string[], isRead: boolean) => Promise<void>
  star: (threadIds: string[], isStarred: boolean) => Promise<void>
  archive: (threadIds: string[]) => Promise<void>
  deleteThreads: (threadIds: string[]) => Promise<void>
  categorize: (threadIds: string[], category: EmailCategory) => Promise<void>
  
  // Message actions
  sendReply: (threadId: string, message: string) => Promise<void>
  forward: (messageId: string, to: string, message: string) => Promise<void>
  
  // Filter and sort
  setFilter: (filter: Partial<EmailFilter>) => void
  clearFilter: () => void
  setSort: (sort: EmailSort) => void
  
  // Pagination
  loadMore: () => Promise<void>
  goToPage: (page: number) => Promise<void>
  setPageSize: (size: number) => Promise<void>
  
  // Search
  search: (query: string) => Promise<void>
  
  // Refresh
  refresh: () => Promise<void>
  
  // Initialize
  initialize: () => Promise<void>
  
  clearError: () => void
}

export const useEmailStore = create<EmailState>()(
  devtools(
    (set, get) => ({
      // Multi-account state
      currentAccountId: null,
      isUnifiedView: false,
      
      // Initial state
      threads: [],
      selectedThread: null,
      selectedMessage: null,
      isLoading: false,
      error: null,
      
      // Pagination
      currentPage: 1,
      pageSize: 20,
      totalThreads: 0,
      hasMore: false,
      
      // Filters and sorting
      filter: {},
      sort: { field: 'date', order: 'desc' },
      
      // Selection
      selectedThreadIds: new Set(),
      isSelectMode: false,
      
      // Stats
      stats: {
        totalUnread: 0,
        totalImportant: 0,
        categories: {},
      },
      
      // Fetch threads (account-specific or unified)
      fetchThreads: async (params) => {
        const { isUnifiedView } = get()
        
        if (isUnifiedView) {
          return get().fetchUnifiedThreads(params)
        }
        
        set({ isLoading: true, error: null })
        
        try {
          const { filter, sort, currentPage, pageSize, currentAccountId } = get()
          
          if (!currentAccountId) {
            throw new Error('No account selected')
          }
          
          const searchParams = {
            ...filter,
            ...params,
            maxResults: params?.pageSize || pageSize,
            pageToken: params?.pageToken,
          }
          
          // Use the new emailApi with account-specific endpoints
          const { emailApi } = await import('../utils/api')
          const response = await emailApi.getThreads(currentAccountId, searchParams)
          
          set({
            threads: response.threads || [],
            totalThreads: response.threads?.length || 0,
            currentPage: params?.page || currentPage,
            hasMore: !!response.nextPageToken,
            stats: get().stats, // Keep existing stats for now
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch threads',
          })
        }
      },,
      
      // Fetch single thread
      fetchThread: async (threadId: string, accountId?: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const { currentAccountId } = get()
          const targetAccountId = accountId || currentAccountId
          
          if (!targetAccountId) {
            throw new Error('No account specified')
          }
          
          // Use the new emailApi with account-specific endpoints
          const { emailApi } = await import('../utils/api')
          const thread = await emailApi.getThread(targetAccountId, threadId)
          
          set({
            selectedThread: thread,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch thread',
          })
        }
      },,
      
      // Select thread
      selectThread: (threadId: string) => {
        const thread = get().threads.find(t => t.id === threadId)
        set({ selectedThread: thread || null })
      },
      
      // Select message
      selectMessage: (messageId: string) => {
        const { threads } = get()
        let foundMessage: ParsedMessage | null = null
        
        for (const thread of threads) {
          const message = thread.messages.find(m => m.id === messageId)
          if (message) {
            foundMessage = message
            break
          }
        }
        
        set({ selectedMessage: foundMessage })
      },
      
      // Toggle thread selection
      toggleThreadSelection: (threadId: string) => {
        const { selectedThreadIds } = get()
        const newSelection = new Set(selectedThreadIds)
        
        if (newSelection.has(threadId)) {
          newSelection.delete(threadId)
        } else {
          newSelection.add(threadId)
        }
        
        set({ selectedThreadIds: newSelection })
      },
      
      // Select all threads
      selectAllThreads: () => {
        const { threads } = get()
        set({ selectedThreadIds: new Set(threads.map(t => t.id)) })
      },
      
      // Clear selection
      clearSelection: () => {
        set({ selectedThreadIds: new Set(), isSelectMode: false })
      },
      
      // Set select mode
      setSelectMode: (enabled: boolean) => {
        set({ isSelectMode: enabled })
        if (!enabled) {
          set({ selectedThreadIds: new Set() })
        }
      },
      
      // Mark as read/unread
      markAsRead: async (threadIds: string[], isRead: boolean) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/read`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isRead }),
            })
          )
          
          await Promise.all(promises)
          
          // Update local state
          const { threads } = get()
          const updatedThreads = threads.map(thread => {
            if (threadIds.includes(thread.id)) {
              return {
                ...thread,
                status: { ...thread.status, isUnread: !isRead },
                unreadCount: isRead ? 0 : thread.messageCount,
              }
            }
            return thread
          })
          
          set({
            threads: updatedThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update threads',
          })
        }
      },
      
      // Star/unstar threads
      star: async (threadIds: string[], isStarred: boolean) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/star`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isStarred }),
            })
          )
          
          await Promise.all(promises)
          
          // Update local state
          const { threads } = get()
          const updatedThreads = threads.map(thread => {
            if (threadIds.includes(thread.id)) {
              return {
                ...thread,
                status: { ...thread.status, isStarred },
              }
            }
            return thread
          })
          
          set({
            threads: updatedThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update threads',
          })
        }
      },
      
      // Archive threads
      archive: async (threadIds: string[]) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/archive`, {
              method: 'POST',
            })
          )
          
          await Promise.all(promises)
          
          // Remove from current view
          const { threads } = get()
          const remainingThreads = threads.filter(t => !threadIds.includes(t.id))
          
          set({
            threads: remainingThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to archive threads',
          })
        }
      },
      
      // Delete threads
      deleteThreads: async (threadIds: string[]) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}`, {
              method: 'DELETE',
            })
          )
          
          await Promise.all(promises)
          
          // Remove from local state
          const { threads } = get()
          const remainingThreads = threads.filter(t => !threadIds.includes(t.id))
          
          set({
            threads: remainingThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to delete threads',
          })
        }
      },
      
      // Categorize threads
      categorize: async (threadIds: string[], category: EmailCategory) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/categorize`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ category }),
            })
          )
          
          await Promise.all(promises)
          
          // Update local state
          const { threads } = get()
          const updatedThreads = threads.map(thread => {
            if (threadIds.includes(thread.id)) {
              return { ...thread, category }
            }
            return thread
          })
          
          set({
            threads: updatedThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to categorize threads',
          })
        }
      },
      
      // Send reply
      sendReply: async (threadId: string, message: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await fetch('/api/messages/send', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              threadId,
              body: message,
              type: 'reply',
            }),
          })
          
          if (!response.ok) {
            throw new Error('Failed to send reply')
          }
          
          // Refresh thread to get updated messages
          await get().fetchThread(threadId)
          
          set({
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to send reply',
          })
          throw error
        }
      },
      
      // Forward message
      forward: async (messageId: string, to: string, message: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await fetch('/api/messages/forward', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              messageId,
              to,
              message,
            }),
          })
          
          if (!response.ok) {
            throw new Error('Failed to forward message')
          }
          
          set({
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to forward message',
          })
          throw error
        }
      },
      
      // Set filter
      setFilter: (filter: Partial<EmailFilter>) => {
        set((state) => ({
          filter: { ...state.filter, ...filter },
          currentPage: 1, // Reset to first page when filter changes
        }))
        get().fetchThreads()
      },
      
      // Clear filter
      clearFilter: () => {
        set({ filter: {}, currentPage: 1 })
        get().fetchThreads()
      },
      
      // Set sort
      setSort: (sort: EmailSort) => {
        set({ sort, currentPage: 1 })
        get().fetchThreads()
      },
      
      // Load more
      loadMore: async () => {
        const { currentPage, hasMore } = get()
        if (!hasMore) return
        
        await get().fetchThreads({ page: currentPage + 1 })
      },
      
      // Go to page
      goToPage: async (page: number) => {
        await get().fetchThreads({ page })
      },
      
      // Set page size
      setPageSize: async (size: number) => {
        set({ pageSize: size, currentPage: 1 })
        await get().fetchThreads()
      },
      
      // Search
      search: async (query: string) => {
        set({ filter: { query }, currentPage: 1 })
        await get().fetchThreads()
      },
      
      // Refresh
      refresh: async () => {
        await get().fetchThreads()
      },
      
      // Multi-account actions
      switchToAccount: (accountId: string | null) => {
        const { currentAccountId } = get()
        
        // Leave current account WebSocket room
        if (currentAccountId) {
          emailWebSocketService.leaveAccount(currentAccountId)
        }
        
        set({
          currentAccountId: accountId,
          isUnifiedView: false,
          threads: [],
          selectedThread: null,
          selectedMessage: null,
          selectedThreadIds: new Set(),
          currentPage: 1,
          filter: {},
        })
        
        // Join new account WebSocket room and fetch threads
        if (accountId) {
          emailWebSocketService.joinAccount(accountId)
          get().fetchThreads()
        }
      },,

      switchToUnifiedView: () => {
        set({
          isUnifiedView: true,
          currentAccountId: null,
          threads: [],
          selectedThread: null,
          selectedMessage: null,
          selectedThreadIds: new Set(),
          currentPage: 1,
          filter: {},
        })
        
        // Fetch unified threads
        get().fetchUnifiedThreads()
      },

      fetchUnifiedThreads: async (params) => {
        set({ isLoading: true, error: null })
        
        try {
          const { filter, sort, currentPage, pageSize } = get()
          
          const requestBody = {
            page: params?.page || currentPage,
            pageSize: params?.pageSize || pageSize,
            sort: sort.field,
            order: sort.order,
            filter: { ...filter, ...params },
          }
          
          const response = await fetch('/api/unified/threads', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody),
          })
          
          if (!response.ok) {
            throw new Error('Failed to fetch unified threads')
          }
          
          const data = await response.json()
          
          set({
            threads: data.data as UnifiedThread[],
            totalThreads: data.pagination.totalItems,
            currentPage: data.pagination.page,
            hasMore: data.pagination.hasNext,
            stats: {
              ...data.summary,
              accountBreakdown: data.accountBreakdown,
            },
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch unified threads',
          })
        }
      },

      // Initialize store
      initialize: async () => {
        try {
          // Load initial threads for default account if available
          const { currentAccountId, isUnifiedView } = get()
          if (currentAccountId || isUnifiedView) {
            await get().fetchThreads()
          }

          // Setup WebSocket event listeners
          emailWebSocketService.on('sync-completed', (event) => {
            const { currentAccountId } = get()
            if (event.accountId === currentAccountId) {
              // Refresh threads after sync completes
              get().fetchThreads()
            }
            
            // Update account sync status
            get().getEmailAccounts()
          })

          emailWebSocketService.on('new-messages', (event) => {
            const { currentAccountId } = get()
            if (event.accountId === currentAccountId) {
              // Refresh threads when new messages arrive
              get().fetchThreads()
            }
          })

          emailWebSocketService.on('account-connected', () => {
            // Refresh account list when new account is connected
            get().getEmailAccounts()
          })

          emailWebSocketService.on('account-disconnected', () => {
            // Refresh account list when account is disconnected
            get().getEmailAccounts()
          })

          // Join current account for real-time updates
          if (currentAccountId) {
            emailWebSocketService.joinAccount(currentAccountId)
          }

        } catch (error) {
          console.warn('Failed to initialize email store:', error)
        }
      },,

      // Email account management
      emailAccounts: [],
      
      // Get email accounts
      getEmailAccounts: async () => {
        set({ isLoading: true, error: null })
        
        try {
          const { emailApi } = await import('../utils/api')
          const accounts = await emailApi.getAccounts()
          
          set({
            emailAccounts: accounts,
            isLoading: false,
            error: null,
          })
          
          return accounts
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch email accounts',
          })
          throw error
        }
      },
      
      // Connect new email account
      connectEmailAccount: async (provider: string) => {
        try {
          const { emailApi } = await import('../utils/api')
          const { authUrl } = await emailApi.getAuthUrl(provider)
          
          // Open OAuth popup or redirect
          window.location.href = authUrl
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to start email connection',
          })
          throw error
        }
      },
      
      // Handle OAuth callback
      handleEmailAuthCallback: async (code: string, provider: string, state?: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const { emailApi } = await import('../utils/api')
          const account = await emailApi.handleAuthCallback({
            code,
            provider,
            ...(state && { state }),
          })
          
          // Refresh account list
          await get().getEmailAccounts()
          
          // Set as current account if it's the first one
          const { emailAccounts, currentAccountId } = get()
          if (!currentAccountId && emailAccounts.length > 0) {
            get().switchToAccount(account.id)
          }
          
          set({
            isLoading: false,
            error: null,
          })
          
          return account
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to connect email account',
          })
          throw error
        }
      },
      
      // Update email account
      updateEmailAccount: async (accountId: string, updateData: any) => {
        set({ isLoading: true, error: null })
        
        try {
          const { emailApi } = await import('../utils/api')
          const updatedAccount = await emailApi.updateAccount(accountId, updateData)
          
          // Update in local state
          const { emailAccounts } = get()
          const updatedAccounts = emailAccounts.map(account =>
            account.id === accountId ? updatedAccount : account
          )
          
          set({
            emailAccounts: updatedAccounts,
            isLoading: false,
            error: null,
          })
          
          return updatedAccount
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update email account',
          })
          throw error
        }
      },
      
      // Delete email account
      deleteEmailAccount: async (accountId: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const { emailApi } = await import('../utils/api')
          await emailApi.deleteAccount(accountId)
          
          // Remove from local state
          const { emailAccounts, currentAccountId } = get()
          const remainingAccounts = emailAccounts.filter(account => account.id !== accountId)
          
          let newCurrentAccountId = currentAccountId
          if (currentAccountId === accountId) {
            newCurrentAccountId = remainingAccounts.length > 0 ? remainingAccounts[0].id : null
          }
          
          set({
            emailAccounts: remainingAccounts,
            currentAccountId: newCurrentAccountId,
            isLoading: false,
            error: null,
          })
          
          // Switch to new account or clear data
          if (newCurrentAccountId) {
            get().switchToAccount(newCurrentAccountId)
          } else {
            set({
              threads: [],
              selectedThread: null,
              selectedMessage: null,
            })
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to delete email account',
          })
          throw error
        }
      },
      
      // Sync email account
      syncEmailAccount: async (accountId: string, fullSync: boolean = false) => {
        try {
          const { emailApi } = await import('../utils/api')
          const syncResponse = await emailApi.syncAccount(accountId, { fullSync })
          
          // Optionally update UI to show sync in progress
          set({
            error: null,
          })
          
          return syncResponse
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to start email sync',
          })
          throw error
        }
      },
      
      // Get sync status
      getSyncStatus: async (accountId: string) => {
        try {
          const { emailApi } = await import('../utils/api')
          return await emailApi.getSyncStatus(accountId)
        } catch (error) {
          console.error('Failed to get sync status:', error)
          return { isActive: false }
        }
      },

      // Clear error
      clearError: () => set({ error: null }),
    })
  )
)