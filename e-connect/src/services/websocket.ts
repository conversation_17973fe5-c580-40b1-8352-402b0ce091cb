import { io, Socket } from 'socket.io-client'
import { toast } from 'sonner'

export interface EmailSyncEvent {
  accountId: string
  syncLogId: string
  syncType: string
  timestamp: Date
}

export interface SyncCompletedEvent extends EmailSyncEvent {
  emailsProcessed: number
  duration: number
}

export interface SyncFailedEvent extends EmailSyncEvent {
  error: string
}

export interface SyncProgressEvent {
  accountId: string
  syncLogId: string
  processed: number
  total: number
  percentage: number
  currentOperation: string
  timestamp: Date
}

export interface NewMessagesEvent {
  accountId: string
  threadIds: string[]
  messageCount: number
  timestamp: Date
}

export interface AccountEvent {
  accountId: string
  provider: string
  email: string
  timestamp: Date
}

type EmailSyncEventHandlers = {
  'connected': (data: any) => void
  'sync-started': (event: EmailSyncEvent) => void
  'sync-completed': (event: SyncCompletedEvent) => void
  'sync-failed': (event: SyncFailedEvent) => void
  'sync-progress': (event: SyncProgressEvent) => void
  'new-messages': (event: NewMessagesEvent) => void
  'account-connected': (event: AccountEvent) => void
  'account-disconnected': (event: AccountEvent) => void
  'account-joined': (data: { accountId: string }) => void
  'account-left': (data: { accountId: string }) => void
  'sync-status': (data: { accountId: string; status: any }) => void
  'error': (data: { message: string }) => void
}

class EmailWebSocketService {
  private socket: Socket | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private eventHandlers = new Map<keyof EmailSyncEventHandlers, Set<Function>>()

  constructor() {
    this.connect()
  }

  private connect() {
    const wsUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:3000'
    
    // Get auth token (you may need to adjust this based on your auth implementation)
    const token = localStorage.getItem('auth_token') || 'mock-token'

    this.socket = io(`${wsUrl}/email-sync`, {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      autoConnect: true,
    })

    this.setupEventListeners()
  }

  private setupEventListeners() {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('Connected to email sync WebSocket')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.emit('connected', { connected: true })
    })

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from email sync WebSocket:', reason)
      this.isConnected = false
      this.handleReconnect()
    })

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      this.handleReconnect()
    })

    // Email sync events
    this.socket.on('sync-started', (event: EmailSyncEvent) => {
      console.log('Email sync started:', event)
      toast.info(`Email sync started for account ${event.accountId}`)
      this.emit('sync-started', event)
    })

    this.socket.on('sync-completed', (event: SyncCompletedEvent) => {
      console.log('Email sync completed:', event)
      toast.success(`Email sync completed: ${event.emailsProcessed} emails processed`)
      this.emit('sync-completed', event)
    })

    this.socket.on('sync-failed', (event: SyncFailedEvent) => {
      console.log('Email sync failed:', event)
      toast.error(`Email sync failed: ${event.error}`)
      this.emit('sync-failed', event)
    })

    this.socket.on('sync-progress', (event: SyncProgressEvent) => {
      console.log('Email sync progress:', event)
      this.emit('sync-progress', event)
    })

    this.socket.on('new-messages', (event: NewMessagesEvent) => {
      console.log('New messages received:', event)
      toast.info(`${event.messageCount} new messages received`)
      this.emit('new-messages', event)
    })

    this.socket.on('account-connected', (event: AccountEvent) => {
      console.log('Email account connected:', event)
      toast.success(`Email account connected: ${event.email}`)
      this.emit('account-connected', event)
    })

    this.socket.on('account-disconnected', (event: AccountEvent) => {
      console.log('Email account disconnected:', event)
      toast.info(`Email account disconnected: ${event.email}`)
      this.emit('account-disconnected', event)
    })

    this.socket.on('account-joined', (data) => {
      console.log('Joined account room:', data)
      this.emit('account-joined', data)
    })

    this.socket.on('account-left', (data) => {
      console.log('Left account room:', data)
      this.emit('account-left', data)
    })

    this.socket.on('sync-status', (data) => {
      console.log('Sync status update:', data)
      this.emit('sync-status', data)
    })

    this.socket.on('error', (data: { message: string }) => {
      console.error('WebSocket error:', data)
      toast.error(`WebSocket error: ${data.message}`)
      this.emit('error', data)
    })
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        if (this.socket) {
          this.socket.connect()
        }
      }, delay)
    } else {
      console.error('Max reconnection attempts reached')
      toast.error('Lost connection to email sync service')
    }
  }

  // Public API

  /**
   * Join a specific email account for real-time updates
   */
  joinAccount(accountId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join-account', { accountId })
    }
  }

  /**
   * Leave a specific email account
   */
  leaveAccount(accountId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave-account', { accountId })
    }
  }

  /**
   * Get current sync status for an account
   */
  getSyncStatus(accountId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('get-sync-status', { accountId })
    }
  }

  /**
   * Add event listener
   */
  on<K extends keyof EmailSyncEventHandlers>(
    event: K,
    handler: EmailSyncEventHandlers[K]
  ) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event)!.add(handler)

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event)
      if (handlers) {
        handlers.delete(handler)
      }
    }
  }

  /**
   * Remove event listener
   */
  off<K extends keyof EmailSyncEventHandlers>(
    event: K,
    handler: EmailSyncEventHandlers[K]
  ) {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
    }
  }

  /**
   * Emit event to registered handlers
   */
  private emit<K extends keyof EmailSyncEventHandlers>(
    event: K,
    data: Parameters<EmailSyncEventHandlers[K]>[0]
  ) {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in ${event} handler:`, error)
        }
      })
    }
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
      this.isConnected = false
    }
  }

  /**
   * Get connection status
   */
  get connected() {
    return this.isConnected
  }

  /**
   * Manually reconnect
   */
  reconnect() {
    if (this.socket) {
      this.socket.connect()
    } else {
      this.connect()
    }
  }
}

// Create singleton instance
export const emailWebSocketService = new EmailWebSocketService()

// React hook for using WebSocket in components
export const useEmailWebSocket = () => {
  return emailWebSocketService
}