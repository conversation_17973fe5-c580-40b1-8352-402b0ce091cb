# Luminar Document Processing Service Configuration

# Environment
LUMINAR_ENV=development
LUMINAR_DOC_DEBUG=true
LUMINAR_DOC_LOG_LEVEL=INFO

# Server Configuration
LUMINAR_DOC_HOST=0.0.0.0
LUMINAR_DOC_PORT=8001
LUMINAR_DOC_WORKERS=1

# Redis Configuration
LUMINAR_DOC_REDIS_URL=redis://localhost:6379/0
LUMINAR_DOC_REDIS_PASSWORD=

# File Processing
LUMINAR_DOC_TEMP_DIR=/tmp/document-processing
LUMINAR_DOC_MAX_FILE_SIZE=104857600  # 100MB
LUMINAR_DOC_DEFAULT_CHUNK_SIZE=1000
LUMINAR_DOC_DEFAULT_CHUNK_OVERLAP=200
LUMINAR_DOC_PROCESSING_TIMEOUT=300

# Crawl4AI Configuration
LUMINAR_DOC_CRAWL_TIMEOUT=30
LUMINAR_DOC_CRAWL_USER_AGENT=Luminar-DocumentProcessor/1.0
LUMINAR_DOC_CRAWL_MAX_DEPTH=1
LUMINAR_DOC_CRAWL_RESPECT_ROBOTS=true

# Unstructured.io Configuration (Optional)
LUMINAR_DOC_UNSTRUCTURED_API_KEY=
LUMINAR_DOC_UNSTRUCTURED_API_URL=

# Security
LUMINAR_DOC_API_KEY=
LUMINAR_DOC_CORS_ORIGINS=["*"]

# Monitoring
LUMINAR_DOC_ENABLE_METRICS=true
LUMINAR_DOC_METRICS_PORT=8002