"""
Document processing core implementation using Unstructured.io and Crawl4AI
"""

import os
import time
import logging
import asyncio
import tempfile
from typing import List, Dict, Any, Optional
from pathlib import Path
import hashlib

import aiofiles
import httpx
from crawl4ai import AsyncWebCrawler
from unstructured.partition.auto import partition
from unstructured.partition.pdf import partition_pdf
from unstructured.partition.docx import partition_docx
from unstructured.partition.text import partition_text
from unstructured.chunking.title import chunk_by_title
from unstructured.staging.base import elements_to_json

from models import ProcessingResult, DocumentChunk, DocumentMetadata, UrlMetadata
from config import get_settings

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Main document processing class"""
    
    def __init__(self):
        self.settings = get_settings()
        self.crawler = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize the document processor"""
        try:
            # Initialize web crawler
            self.crawler = AsyncWebCrawler(
                verbose=False,
                config={
                    "headless": True,
                    "browser_type": "chromium",
                    "user_agent": self.settings.crawl_user_agent,
                    "timeout": self.settings.crawl_timeout * 1000,  # Convert to milliseconds
                }
            )
            await self.crawler.start()
            
            self._initialized = True
            logger.info("Document processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize document processor: {e}")
            raise
    
    async def process_file(
        self,
        file_path: Path,
        filename: str,
        content_type: str,
        extract_metadata: bool = True,
        chunk_size: int = 1000,
        chunk_overlap: int = 200
    ) -> ProcessingResult:
        """Process a file and extract content"""
        start_time = time.time()
        
        try:
            if not self._initialized:
                await self.initialize()
            
            logger.info(f"Processing file: {filename} ({content_type})")
            
            # Validate file
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            file_size = file_path.stat().st_size
            if file_size > self.settings.max_file_size:
                raise ValueError(f"File too large: {file_size} bytes")
            
            # Extract content based on file type
            if content_type == "application/pdf":
                elements = await self._process_pdf(file_path)
            elif content_type in [
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword"
            ]:
                elements = await self._process_docx(file_path)
            elif content_type in ["text/plain", "text/markdown"]:
                elements = await self._process_text(file_path)
            else:
                # Fallback to auto-detection
                elements = await self._process_auto(file_path)
            
            # Extract text content
            content = self._elements_to_text(elements)
            
            # Create chunks
            chunks = await self._create_chunks(
                content, elements, chunk_size, chunk_overlap
            )
            
            # Extract metadata
            metadata = {}
            if extract_metadata:
                metadata = await self._extract_file_metadata(
                    file_path, filename, content_type, elements
                )
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=True,
                content=content,
                chunks=chunks,
                metadata=metadata,
                word_count=len(content.split()),
                char_count=len(content),
                processing_time=processing_time,
                extracted_at=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={"error": str(e)},
                processing_time=time.time() - start_time,
                extracted_at=time.time()
            )
    
    async def process_url(
        self,
        url: str,
        extract_metadata: bool = True,
        chunk_size: int = 1000,
        chunk_overlap: int = 200
    ) -> ProcessingResult:
        """Process a URL and extract content"""
        start_time = time.time()
        
        try:
            if not self._initialized:
                await self.initialize()
            
            logger.info(f"Processing URL: {url}")
            
            # Crawl the URL
            result = await self.crawler.arun(
                url=url,
                word_count_threshold=10,
                extraction_strategy="cosine_clustering",
                enable_async=True,
                bypass_cache=True
            )
            
            if not result.success:
                raise Exception(f"Failed to crawl URL: {result.status_code}")
            
            content = result.cleaned_html or result.html or ""
            
            # Clean and extract text
            if result.markdown:
                content = result.markdown
            else:
                # Fallback to HTML cleaning
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(content, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style", "nav", "footer", "header"]):
                    script.decompose()
                
                content = soup.get_text(separator='\n', strip=True)
            
            # Create chunks
            chunks = await self._create_chunks_from_text(
                content, chunk_size, chunk_overlap
            )
            
            # Extract metadata
            metadata = {}
            if extract_metadata:
                metadata = await self._extract_url_metadata(url, result)
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=True,
                content=content,
                chunks=chunks,
                metadata=metadata,
                word_count=len(content.split()),
                char_count=len(content),
                processing_time=processing_time,
                extracted_at=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error processing URL {url}: {e}")
            return ProcessingResult(
                success=False,
                content="",
                chunks=[],
                metadata={"error": str(e), "url": url},
                processing_time=time.time() - start_time,
                extracted_at=time.time()
            )
    
    async def _process_pdf(self, file_path: Path) -> List:
        """Process PDF file using unstructured"""
        try:
            elements = partition_pdf(
                filename=str(file_path),
                strategy="hi_res",  # High resolution for better accuracy
                include_page_breaks=True,
                infer_table_structure=True,
                extract_images_in_pdf=False  # Disable for now
            )
            return elements
        except Exception as e:
            logger.warning(f"Hi-res PDF processing failed, falling back to fast: {e}")
            # Fallback to fast strategy
            elements = partition_pdf(
                filename=str(file_path),
                strategy="fast"
            )
            return elements
    
    async def _process_docx(self, file_path: Path) -> List:
        """Process DOCX file using unstructured"""
        elements = partition_docx(
            filename=str(file_path),
            infer_table_structure=True
        )
        return elements
    
    async def _process_text(self, file_path: Path) -> List:
        """Process text file using unstructured"""
        elements = partition_text(filename=str(file_path))
        return elements
    
    async def _process_auto(self, file_path: Path) -> List:
        """Process file using auto-detection"""
        elements = partition(filename=str(file_path))
        return elements
    
    def _elements_to_text(self, elements: List) -> str:
        """Convert unstructured elements to plain text"""
        text_parts = []
        
        for element in elements:
            if hasattr(element, 'text') and element.text:
                text_parts.append(element.text.strip())
        
        return '\n\n'.join(text_parts)
    
    async def _create_chunks(
        self,
        content: str,
        elements: List,
        chunk_size: int,
        chunk_overlap: int
    ) -> List[DocumentChunk]:
        """Create document chunks from elements"""
        try:
            # Try to use unstructured chunking if elements support it
            chunks = chunk_by_title(
                elements,
                max_characters=chunk_size,
                multipage_sections=True,
                combine_text_under_n_chars=chunk_overlap
            )
            
            document_chunks = []
            current_pos = 0
            
            for i, chunk in enumerate(chunks):
                chunk_text = chunk.text if hasattr(chunk, 'text') else str(chunk)
                chunk_id = hashlib.md5(f"{i}_{chunk_text[:100]}".encode()).hexdigest()
                
                # Find position in original content
                start_pos = content.find(chunk_text, current_pos)
                if start_pos == -1:
                    start_pos = current_pos
                end_pos = start_pos + len(chunk_text)
                
                document_chunks.append(DocumentChunk(
                    id=chunk_id,
                    content=chunk_text,
                    start_char=start_pos,
                    end_char=end_pos,
                    metadata={
                        "chunk_index": i,
                        "element_type": chunk.category if hasattr(chunk, 'category') else "text"
                    }
                ))
                
                current_pos = end_pos
            
            return document_chunks
            
        except Exception as e:
            logger.warning(f"Unstructured chunking failed, using text-based chunking: {e}")
            return await self._create_chunks_from_text(content, chunk_size, chunk_overlap)
    
    async def _create_chunks_from_text(
        self,
        content: str,
        chunk_size: int,
        chunk_overlap: int
    ) -> List[DocumentChunk]:
        """Create chunks from plain text"""
        chunks = []
        words = content.split()
        
        if not words:
            return chunks
        
        # Estimate words per chunk
        avg_word_length = sum(len(word) for word in words[:100]) / min(100, len(words))
        words_per_chunk = max(1, int(chunk_size / (avg_word_length + 1)))  # +1 for space
        overlap_words = max(0, int(chunk_overlap / (avg_word_length + 1)))
        
        start_idx = 0
        chunk_index = 0
        
        while start_idx < len(words):
            end_idx = min(start_idx + words_per_chunk, len(words))
            chunk_words = words[start_idx:end_idx]
            chunk_text = ' '.join(chunk_words)
            
            # Calculate character positions
            start_char = len(' '.join(words[:start_idx]))
            if start_idx > 0:
                start_char += 1  # Account for space
            end_char = start_char + len(chunk_text)
            
            chunk_id = hashlib.md5(f"{chunk_index}_{chunk_text[:50]}".encode()).hexdigest()
            
            chunks.append(DocumentChunk(
                id=chunk_id,
                content=chunk_text,
                start_char=start_char,
                end_char=end_char,
                metadata={
                    "chunk_index": chunk_index,
                    "word_count": len(chunk_words)
                }
            ))
            
            # Move to next chunk with overlap
            start_idx = max(start_idx + words_per_chunk - overlap_words, start_idx + 1)
            chunk_index += 1
            
            # Prevent infinite loop
            if start_idx >= len(words):
                break
        
        return chunks
    
    async def _extract_file_metadata(
        self,
        file_path: Path,
        filename: str,
        content_type: str,
        elements: List
    ) -> Dict[str, Any]:
        """Extract metadata from processed file"""
        metadata = {
            "filename": filename,
            "content_type": content_type,
            "file_size": file_path.stat().st_size,
            "processed_with": "unstructured",
            "element_count": len(elements),
            "extraction_method": "unstructured_partition"
        }
        
        # Extract additional metadata from elements
        if elements:
            # Count different element types
            element_types = {}
            for element in elements:
                if hasattr(element, 'category'):
                    element_types[element.category] = element_types.get(element.category, 0) + 1
            
            metadata["element_types"] = element_types
            
            # Extract document metadata if available
            first_element = elements[0]
            if hasattr(first_element, 'metadata'):
                element_metadata = first_element.metadata
                if element_metadata:
                    # Common metadata fields
                    for field in ['title', 'author', 'subject', 'creator', 'producer']:
                        if field in element_metadata:
                            metadata[field] = element_metadata[field]
        
        return metadata
    
    async def _extract_url_metadata(self, url: str, crawl_result) -> Dict[str, Any]:
        """Extract metadata from crawled URL"""
        from urllib.parse import urlparse
        
        parsed_url = urlparse(url)
        
        metadata = {
            "url": url,
            "domain": parsed_url.netloc,
            "processed_with": "crawl4ai",
            "status_code": getattr(crawl_result, 'status_code', 200),
            "crawl_success": crawl_result.success,
            "extraction_method": "crawl4ai_cosine_clustering"
        }
        
        # Extract page metadata if available
        if hasattr(crawl_result, 'metadata') and crawl_result.metadata:
            page_meta = crawl_result.metadata
            for field in ['title', 'description', 'keywords', 'author']:
                if field in page_meta:
                    metadata[field] = page_meta[field]
        
        # Extract links
        if hasattr(crawl_result, 'links') and crawl_result.links:
            metadata["link_count"] = len(crawl_result.links)
            metadata["internal_links"] = [
                link for link in crawl_result.links
                if parsed_url.netloc in link.get('href', '')
            ][:10]  # First 10 internal links
        
        return metadata
    
    async def cleanup(self):
        """Clean up resources"""
        if self.crawler:
            await self.crawler.close()
            self.crawler = None
        
        self._initialized = False
        logger.info("Document processor cleaned up")