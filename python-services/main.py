"""
Luminar Document Processing Service

FastAPI service for processing documents using Unstructured.io and Crawl4AI.
Handles PDF, DOCX, TXT files and web URLs for content extraction.
"""

import os
import uuid
import logging
import asyncio
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pathlib import Path

import aiofiles
import httpx
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, HttpUrl, validator
import redis.asyncio as redis

from document_processor import DocumentProcessor
from models import ProcessingResult, ProcessingStatus, DocumentChunk
from config import get_settings

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Luminar Document Processing Service",
    description="Document processing service for Lighthouse knowledge management",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure based on environment
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
settings = get_settings()
processor = DocumentProcessor()
redis_client: Optional[redis.Redis] = None

class ProcessFileRequest(BaseModel):
    """Request model for file processing"""
    filename: str
    content_type: str
    extract_metadata: bool = True
    chunk_size: int = 1000
    chunk_overlap: int = 200

class ProcessUrlRequest(BaseModel):
    """Request model for URL processing"""
    url: HttpUrl
    extract_metadata: bool = True
    chunk_size: int = 1000
    chunk_overlap: int = 200
    
    @validator('url')
    def validate_url(cls, v):
        if not str(v).startswith(('http://', 'https://')):
            raise ValueError('URL must start with http:// or https://')
        return v

class ProcessingStatusResponse(BaseModel):
    """Response model for processing status"""
    job_id: str
    status: str
    progress: float
    result: Optional[ProcessingResult] = None
    error: Optional[str] = None

@app.on_event("startup")
async def startup_event():
    """Initialize connections and services on startup"""
    global redis_client
    
    try:
        # Initialize Redis connection for caching and job tracking
        redis_client = redis.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=True
        )
        await redis_client.ping()
        logger.info("Connected to Redis successfully")
        
        # Initialize document processor
        await processor.initialize()
        logger.info("Document processor initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    global redis_client
    
    if redis_client:
        await redis_client.close()
        logger.info("Redis connection closed")

async def get_redis() -> redis.Redis:
    """Dependency to get Redis client"""
    if not redis_client:
        raise HTTPException(status_code=500, detail="Redis not available")
    return redis_client

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check Redis connection
        if redis_client:
            await redis_client.ping()
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "redis": "connected" if redis_client else "disconnected",
                "processor": "initialized"
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )

@app.post("/process/file", response_model=ProcessingStatusResponse)
async def process_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    extract_metadata: bool = True,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    redis_client: redis.Redis = Depends(get_redis)
):
    """
    Process uploaded file and extract content.
    Returns job ID for tracking processing status.
    """
    try:
        # Validate file type
        if not file.content_type:
            raise HTTPException(status_code=400, detail="Content type required")
        
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Store initial job status
        await redis_client.setex(
            f"job:{job_id}",
            3600,  # 1 hour TTL
            ProcessingStatus(
                job_id=job_id,
                status="queued",
                progress=0.0
            ).json()
        )
        
        # Save uploaded file temporarily
        temp_dir = Path(settings.temp_dir)
        temp_dir.mkdir(exist_ok=True)
        
        file_path = temp_dir / f"{job_id}_{file.filename}"
        
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # Start background processing
        background_tasks.add_task(
            process_file_background,
            job_id,
            file_path,
            file.filename or "unknown",
            file.content_type,
            extract_metadata,
            chunk_size,
            chunk_overlap
        )
        
        return ProcessingStatusResponse(
            job_id=job_id,
            status="queued",
            progress=0.0
        )
        
    except Exception as e:
        logger.error(f"Error starting file processing: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/process/url", response_model=ProcessingStatusResponse)
async def process_url(
    request: ProcessUrlRequest,
    background_tasks: BackgroundTasks,
    redis_client: redis.Redis = Depends(get_redis)
):
    """
    Process URL and extract content.
    Returns job ID for tracking processing status.
    """
    try:
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Store initial job status
        await redis_client.setex(
            f"job:{job_id}",
            3600,  # 1 hour TTL
            ProcessingStatus(
                job_id=job_id,
                status="queued",
                progress=0.0
            ).json()
        )
        
        # Start background processing
        background_tasks.add_task(
            process_url_background,
            job_id,
            str(request.url),
            request.extract_metadata,
            request.chunk_size,
            request.chunk_overlap
        )
        
        return ProcessingStatusResponse(
            job_id=job_id,
            status="queued",
            progress=0.0
        )
        
    except Exception as e:
        logger.error(f"Error starting URL processing: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status/{job_id}", response_model=ProcessingStatusResponse)
async def get_processing_status(
    job_id: str,
    redis_client: redis.Redis = Depends(get_redis)
):
    """Get processing status for a job"""
    try:
        status_data = await redis_client.get(f"job:{job_id}")
        
        if not status_data:
            raise HTTPException(status_code=404, detail="Job not found")
        
        status = ProcessingStatus.parse_raw(status_data)
        
        response = ProcessingStatusResponse(
            job_id=status.job_id,
            status=status.status,
            progress=status.progress,
            error=status.error
        )
        
        # Add result if processing is complete
        if status.status == "completed" and status.result:
            response.result = status.result
            
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_file_background(
    job_id: str,
    file_path: Path,
    filename: str,
    content_type: str,
    extract_metadata: bool,
    chunk_size: int,
    chunk_overlap: int
):
    """Background task for processing files"""
    try:
        # Update status to processing
        await update_job_status(job_id, "processing", 10.0)
        
        # Process the file
        result = await processor.process_file(
            file_path,
            filename,
            content_type,
            extract_metadata,
            chunk_size,
            chunk_overlap
        )
        
        # Update progress
        await update_job_status(job_id, "processing", 80.0)
        
        # Store result and mark as completed
        await update_job_status(job_id, "completed", 100.0, result=result)
        
        # Clean up temporary file
        try:
            file_path.unlink()
        except Exception as e:
            logger.warning(f"Failed to delete temp file {file_path}: {e}")
            
    except Exception as e:
        logger.error(f"Error processing file {filename}: {e}")
        await update_job_status(job_id, "failed", 0.0, error=str(e))

async def process_url_background(
    job_id: str,
    url: str,
    extract_metadata: bool,
    chunk_size: int,
    chunk_overlap: int
):
    """Background task for processing URLs"""
    try:
        # Update status to processing
        await update_job_status(job_id, "processing", 10.0)
        
        # Process the URL
        result = await processor.process_url(
            url,
            extract_metadata,
            chunk_size,
            chunk_overlap
        )
        
        # Update progress
        await update_job_status(job_id, "processing", 80.0)
        
        # Store result and mark as completed
        await update_job_status(job_id, "completed", 100.0, result=result)
        
    except Exception as e:
        logger.error(f"Error processing URL {url}: {e}")
        await update_job_status(job_id, "failed", 0.0, error=str(e))

async def update_job_status(
    job_id: str,
    status: str,
    progress: float,
    result: Optional[ProcessingResult] = None,
    error: Optional[str] = None
):
    """Update job status in Redis"""
    try:
        if not redis_client:
            return
            
        status_obj = ProcessingStatus(
            job_id=job_id,
            status=status,
            progress=progress,
            result=result,
            error=error
        )
        
        await redis_client.setex(
            f"job:{job_id}",
            3600,  # 1 hour TTL
            status_obj.json()
        )
        
    except Exception as e:
        logger.error(f"Failed to update job status: {e}")

@app.post("/process/batch")
async def process_batch(
    files: List[UploadFile] = File(...),
    urls: List[str] = [],
    background_tasks: BackgroundTasks = BackgroundTasks(),
    redis_client: redis.Redis = Depends(get_redis)
):
    """Process multiple files and URLs in batch"""
    try:
        job_ids = []
        
        # Process files
        for file in files:
            if file.content_type:
                job_id = str(uuid.uuid4())
                job_ids.append(job_id)
                
                # Store initial status
                await redis_client.setex(
                    f"job:{job_id}",
                    3600,
                    ProcessingStatus(
                        job_id=job_id,
                        status="queued",
                        progress=0.0
                    ).json()
                )
                
                # Save and queue for processing
                temp_dir = Path(settings.temp_dir)
                temp_dir.mkdir(exist_ok=True)
                file_path = temp_dir / f"{job_id}_{file.filename}"
                
                async with aiofiles.open(file_path, 'wb') as f:
                    content = await file.read()
                    await f.write(content)
                
                background_tasks.add_task(
                    process_file_background,
                    job_id,
                    file_path,
                    file.filename or "unknown",
                    file.content_type,
                    True,  # extract_metadata
                    1000,  # chunk_size
                    200    # chunk_overlap
                )
        
        # Process URLs
        for url in urls:
            job_id = str(uuid.uuid4())
            job_ids.append(job_id)
            
            await redis_client.setex(
                f"job:{job_id}",
                3600,
                ProcessingStatus(
                    job_id=job_id,
                    status="queued",
                    progress=0.0
                ).json()
            )
            
            background_tasks.add_task(
                process_url_background,
                job_id,
                url,
                True,  # extract_metadata
                1000,  # chunk_size
                200    # chunk_overlap
            )
        
        return {
            "job_ids": job_ids,
            "message": f"Started processing {len(files)} files and {len(urls)} URLs"
        }
        
    except Exception as e:
        logger.error(f"Error in batch processing: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )