"""
Configuration settings for document processing service
"""

import os
from typing import Optional
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    """Application settings"""
    
    # Service configuration
    service_name: str = "luminar-document-processor"
    debug: bool = False
    log_level: str = "INFO"
    
    # Server configuration
    host: str = "0.0.0.0"
    port: int = 8001
    workers: int = 1
    
    # Redis configuration
    redis_url: str = "redis://localhost:6379/0"
    redis_password: Optional[str] = None
    
    # File processing configuration
    temp_dir: str = "/tmp/document-processing"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_file_types: list = [
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "text/plain",
        "text/markdown",
        "text/html"
    ]
    
    # Processing configuration
    default_chunk_size: int = 1000
    default_chunk_overlap: int = 200
    max_chunks_per_document: int = 1000
    processing_timeout: int = 300  # 5 minutes
    
    # Crawl4AI configuration
    crawl_timeout: int = 30
    crawl_user_agent: str = "Luminar-DocumentProcessor/1.0"
    crawl_max_depth: int = 1
    crawl_respect_robots: bool = True
    
    # Unstructured.io configuration
    unstructured_api_key: Optional[str] = None
    unstructured_api_url: Optional[str] = None
    
    # Security
    api_key: Optional[str] = None
    cors_origins: list = ["*"]
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 8002
    
    @validator('temp_dir')
    def create_temp_dir(cls, v):
        """Ensure temp directory exists"""
        os.makedirs(v, exist_ok=True)
        return v
    
    @validator('redis_url')
    def validate_redis_url(cls, v):
        """Validate Redis URL format"""
        if not v.startswith(('redis://', 'rediss://')):
            raise ValueError('Redis URL must start with redis:// or rediss://')
        return v
    
    class Config:
        env_file = ".env"
        env_prefix = "LUMINAR_DOC_"
        case_sensitive = False

# Global settings instance
_settings: Optional[Settings] = None

def get_settings() -> Settings:
    """Get application settings"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings

# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings"""
    debug: bool = True
    log_level: str = "DEBUG"
    
class ProductionSettings(Settings):
    """Production environment settings"""
    debug: bool = False
    log_level: str = "INFO"
    workers: int = 4
    
class TestingSettings(Settings):
    """Testing environment settings"""
    debug: bool = True
    log_level: str = "DEBUG"
    temp_dir: str = "/tmp/document-processing-test"
    redis_url: str = "redis://localhost:6379/1"  # Use different DB for tests

def get_settings_for_env(env: str = None) -> Settings:
    """Get settings for specific environment"""
    env = env or os.getenv("LUMINAR_ENV", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()