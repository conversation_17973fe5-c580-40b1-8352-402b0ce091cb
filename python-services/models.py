"""
Data models for document processing service
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, HttpUrl

class DocumentChunk(BaseModel):
    """Model for document chunks"""
    id: str
    content: str
    start_char: int
    end_char: int
    metadata: Dict[str, Any] = {}

class ProcessingResult(BaseModel):
    """Result of document processing"""
    success: bool
    content: str
    chunks: List[DocumentChunk] = []
    metadata: Dict[str, Any] = {}
    word_count: int = 0
    char_count: int = 0
    processing_time: float = 0.0
    extracted_at: str = ""

class ProcessingStatus(BaseModel):
    """Status of a processing job"""
    job_id: str
    status: str  # queued, processing, completed, failed
    progress: float  # 0.0 to 100.0
    result: Optional[ProcessingResult] = None
    error: Optional[str] = None
    created_at: str = ""
    updated_at: str = ""
    
    def __init__(self, **data):
        if 'created_at' not in data:
            data['created_at'] = datetime.utcnow().isoformat()
        if 'updated_at' not in data:
            data['updated_at'] = datetime.utcnow().isoformat()
        super().__init__(**data)

class DocumentMetadata(BaseModel):
    """Document metadata extracted during processing"""
    filename: str
    content_type: str
    file_size: Optional[int] = None
    page_count: Optional[int] = None
    language: Optional[str] = None
    author: Optional[str] = None
    title: Optional[str] = None
    subject: Optional[str] = None
    creator: Optional[str] = None
    producer: Optional[str] = None
    creation_date: Optional[str] = None
    modification_date: Optional[str] = None
    keywords: List[str] = []

class UrlMetadata(BaseModel):
    """URL metadata extracted during crawling"""
    url: str
    title: Optional[str] = None
    description: Optional[str] = None
    keywords: List[str] = []
    author: Optional[str] = None
    published_date: Optional[str] = None
    domain: str = ""
    content_type: str = "text/html"
    status_code: int = 200
    final_url: Optional[str] = None  # After redirects
    crawl_depth: int = 0

class ProcessingConfig(BaseModel):
    """Configuration for document processing"""
    chunk_size: int = 1000
    chunk_overlap: int = 200
    extract_metadata: bool = True
    extract_images: bool = False
    extract_tables: bool = True
    language: str = "auto"
    max_content_length: int = 10_000_000  # 10MB
    
class ErrorDetails(BaseModel):
    """Detailed error information"""
    error_type: str
    error_message: str
    stack_trace: Optional[str] = None
    context: Dict[str, Any] = {}
    timestamp: str = ""
    
    def __init__(self, **data):
        if 'timestamp' not in data:
            data['timestamp'] = datetime.utcnow().isoformat()
        super().__init__(**data)