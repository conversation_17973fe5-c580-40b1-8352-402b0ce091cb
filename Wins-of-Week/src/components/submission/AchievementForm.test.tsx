import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { AchievementForm } from './AchievementForm'

describe('AchievementForm', () => {
  const defaultProps = {
    onSubmit: vi.fn(),
    onCancel: vi.fn(),
    isSubmitting: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders form fields', () => {
    render(<AchievementForm {...defaultProps} />)

    expect(screen.getByLabelText(/achievement title/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/impact level/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/team members involved/i)).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const submitButton = screen.getByRole('button', { name: /submit achievement/i })
    await user.click(submitButton)

    expect(screen.getByText(/title is required/i)).toBeInTheDocument()
    expect(screen.getByText(/description is required/i)).toBeInTheDocument()
    expect(defaultProps.onSubmit).not.toHaveBeenCalled()
  })

  it('validates title length', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    await user.type(titleInput, 'Short')

    const submitButton = screen.getByRole('button', { name: /submit achievement/i })
    await user.click(submitButton)

    expect(screen.getByText(/title must be at least 10 characters/i)).toBeInTheDocument()
  })

  it('validates description length', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    const descriptionInput = screen.getByLabelText(/description/i)

    await user.type(titleInput, 'Valid Achievement Title')
    await user.type(descriptionInput, 'Too short')

    const submitButton = screen.getByRole('button', { name: /submit achievement/i })
    await user.click(submitButton)

    expect(screen.getByText(/description must be at least 20 characters/i)).toBeInTheDocument()
  })

  it('submits form with valid data', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    const descriptionInput = screen.getByLabelText(/description/i)
    const impactSelect = screen.getByLabelText(/impact level/i)
    const categorySelect = screen.getByLabelText(/category/i)
    const teamMembersInput = screen.getByLabelText(/team members/i)

    await user.type(titleInput, 'Completed Major Project Ahead of Schedule')
    await user.type(
      descriptionInput,
      'Successfully delivered the customer portal project 2 weeks ahead of schedule with all features implemented.'
    )
    await user.selectOptions(impactSelect, 'high')
    await user.selectOptions(categorySelect, 'project')
    await user.type(teamMembersInput, 'John Doe, Jane Smith')

    const submitButton = screen.getByRole('button', { name: /submit achievement/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith({
        title: 'Completed Major Project Ahead of Schedule',
        description:
          'Successfully delivered the customer portal project 2 weeks ahead of schedule with all features implemented.',
        impact: 'high',
        category: 'project',
        teamMembers: ['John Doe', 'Jane Smith'],
      })
    })
  })

  it('handles cancel action', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)

    expect(defaultProps.onCancel).toHaveBeenCalled()
  })

  it('disables form when submitting', () => {
    render(<AchievementForm {...defaultProps} isSubmitting={true} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    const submitButton = screen.getByRole('button', { name: /submitting/i })

    expect(titleInput).toBeDisabled()
    expect(submitButton).toBeDisabled()
  })

  it('shows character count for description', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const descriptionInput = screen.getByLabelText(/description/i)
    await user.type(descriptionInput, 'This is a test description')

    expect(screen.getByText(/26 \/ 500/i)).toBeInTheDocument()
  })

  it('handles metrics input', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const metricsInput = screen.getByLabelText(/key metrics/i)
    await user.type(metricsInput, '30% improvement in processing time')

    const submitButton = screen.getByRole('button', { name: /submit achievement/i })
    
    // Fill other required fields
    await user.type(
      screen.getByLabelText(/achievement title/i),
      'Performance Optimization Success'
    )
    await user.type(
      screen.getByLabelText(/description/i),
      'Optimized the data processing pipeline resulting in significant improvements'
    )

    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          metrics: '30% improvement in processing time',
        })
      )
    })
  })

  it('parses team members correctly', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const teamMembersInput = screen.getByLabelText(/team members/i)
    await user.type(teamMembersInput, 'John Doe,Jane Smith,Bob Johnson')

    // Fill other required fields
    await user.type(
      screen.getByLabelText(/achievement title/i),
      'Team Collaboration Success'
    )
    await user.type(
      screen.getByLabelText(/description/i),
      'Great teamwork on the latest project delivery'
    )

    const submitButton = screen.getByRole('button', { name: /submit achievement/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          teamMembers: ['John Doe', 'Jane Smith', 'Bob Johnson'],
        })
      )
    })
  })

  it('shows preview of formatted content', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    await user.type(titleInput, 'Amazing Achievement')

    expect(screen.getByText('Amazing Achievement')).toBeInTheDocument()
  })

  it('allows file attachments', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const fileInput = screen.getByLabelText(/attachments/i)
    
    await user.upload(fileInput, file)

    expect(screen.getByText('test.pdf')).toBeInTheDocument()
  })

  it('validates file size', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    // Create a file larger than 10MB
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', {
      type: 'application/pdf',
    })
    const fileInput = screen.getByLabelText(/attachments/i)
    
    await user.upload(fileInput, largeFile)

    expect(screen.getByText(/file size must be less than 10MB/i)).toBeInTheDocument()
  })

  it('clears form on successful submission', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    const descriptionInput = screen.getByLabelText(/description/i)

    await user.type(titleInput, 'Completed Major Project')
    await user.type(
      descriptionInput,
      'Successfully delivered the project with great results'
    )

    const submitButton = screen.getByRole('button', { name: /submit achievement/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(titleInput).toHaveValue('')
      expect(descriptionInput).toHaveValue('')
    })
  })

  it('shows confirmation dialog before cancel with unsaved changes', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    await user.type(titleInput, 'Unsaved Achievement')

    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)

    expect(
      screen.getByText(/you have unsaved changes. are you sure you want to cancel/i)
    ).toBeInTheDocument()
  })

  it('handles date selection for achievement date', async () => {
    const { user } = render(<AchievementForm {...defaultProps} />)

    const dateInput = screen.getByLabelText(/achievement date/i)
    await user.type(dateInput, '2024-01-15')

    expect(dateInput).toHaveValue('2024-01-15')
  })

  it('provides impact level descriptions', () => {
    render(<AchievementForm {...defaultProps} />)

    const impactSelect = screen.getByLabelText(/impact level/i)
    
    expect(screen.getByText(/high.*significant business impact/i)).toBeInTheDocument()
    expect(screen.getByText(/medium.*moderate improvements/i)).toBeInTheDocument()
    expect(screen.getByText(/low.*minor enhancements/i)).toBeInTheDocument()
  })

  it('auto-saves draft after inactivity', async () => {
    vi.useFakeTimers()
    const onSaveDraft = vi.fn()
    const { user } = render(<AchievementForm {...defaultProps} onSaveDraft={onSaveDraft} />)

    const titleInput = screen.getByLabelText(/achievement title/i)
    await user.type(titleInput, 'Draft Achievement')

    // Wait for auto-save timeout (e.g., 30 seconds)
    vi.advanceTimersByTime(30000)

    expect(onSaveDraft).toHaveBeenCalledWith({
      title: 'Draft Achievement',
      description: '',
      impact: 'medium',
      category: '',
      teamMembers: [],
    })

    vi.useRealTimers()
  })
})