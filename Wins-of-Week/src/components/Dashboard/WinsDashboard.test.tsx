import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { WinsDashboard } from './WinsDashboard'
import { useAuthStore } from '@luminar/shared-auth'
import { useWinsStore } from '../../stores/winsStore'

// Mock the stores
vi.mock('@luminar/shared-auth', () => ({
  useAuthStore: vi.fn(),
}))

vi.mock('../../stores/winsStore', () => ({
  useWinsStore: vi.fn(),
}))

// Mock date-fns functions
vi.mock('date-fns', () => ({
  startOfWeek: vi.fn((date) => new Date('2024-01-14')),
  endOfWeek: vi.fn((date) => new Date('2024-01-20')),
  subWeeks: vi.fn((date, weeks) => {
    const newDate = new Date(date)
    newDate.setDate(newDate.getDate() - weeks * 7)
    return newDate
  }),
  format: vi.fn((date, format) => 'Jan 14'),
}))

describe('WinsDashboard', () => {
  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  }

  const mockSubmissions = [
    {
      id: 's1',
      userId: 'user-1',
      title: 'Completed Major Project',
      category: 'achievement',
      status: 'approved',
      submittedAt: '2024-01-15T10:00:00Z',
      authorName: 'Test User',
      views: 100,
      reactions: 15,
      rating: 4.5,
    },
    {
      id: 's2',
      userId: 'user-2',
      title: 'Cost Savings Initiative',
      category: 'cost_saving',
      status: 'approved',
      submittedAt: '2024-01-16T14:00:00Z',
      authorName: 'Jane Doe',
      views: 80,
      reactions: 12,
      rating: 4.8,
    },
    {
      id: 's3',
      userId: 'user-1',
      title: 'Team Recognition',
      category: 'recognition',
      status: 'pending',
      submittedAt: '2024-01-17T09:00:00Z',
      authorName: 'Test User',
      views: 50,
      reactions: 8,
      rating: 0,
    },
    {
      id: 's4',
      userId: 'user-3',
      title: 'Process Improvement',
      category: 'achievement',
      status: 'rejected',
      submittedAt: '2024-01-10T11:00:00Z',
      authorName: 'Bob Smith',
      views: 20,
      reactions: 2,
      rating: 0,
    },
  ]

  const mockApprovals = [
    {
      id: 'a1',
      submissionId: 's1',
      reviewerName: 'Manager',
      status: 'approved',
      createdAt: '2024-01-15T12:00:00Z',
      feedback: 'Great work!',
    },
    {
      id: 'a2',
      submissionId: 's2',
      reviewerName: 'Director',
      status: 'approved',
      createdAt: '2024-01-16T16:00:00Z',
      feedback: 'Excellent initiative',
    },
  ]

  const mockWinsStore = {
    submissions: mockSubmissions,
    approvals: mockApprovals,
    analytics: {},
    fetchSubmissions: vi.fn().mockResolvedValue(undefined),
    fetchApprovals: vi.fn().mockResolvedValue(undefined),
    fetchAnalytics: vi.fn().mockResolvedValue(undefined),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAuthStore as any).mockReturnValue({ user: mockUser })
    ;(useWinsStore as any).mockReturnValue(mockWinsStore)
  })

  it('renders loading state initially', () => {
    render(<WinsDashboard />)
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('fetches all data on mount', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      expect(mockWinsStore.fetchSubmissions).toHaveBeenCalled()
      expect(mockWinsStore.fetchApprovals).toHaveBeenCalled()
      expect(mockWinsStore.fetchAnalytics).toHaveBeenCalled()
    })
  })

  it('displays total submissions count', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('4')).toBeInTheDocument()
      expect(screen.getByText('Total Submissions')).toBeInTheDocument()
    })
  })

  it('displays approved wins count', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('Approved Wins')).toBeInTheDocument()
    })
  })

  it('displays pending approval count', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('Pending Approval')).toBeInTheDocument()
    })
  })

  it('calculates this week submissions', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // s1, s2, s3 are in the current week
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('This Week')).toBeInTheDocument()
    })
  })

  it('calculates average rating correctly', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // Average of rated submissions: (4.5 + 4.8) / 2 = 4.65, rounded to 4.7
      expect(screen.getByText('4.7')).toBeInTheDocument()
      expect(screen.getByText('Average Rating')).toBeInTheDocument()
    })
  })

  it('calculates total views', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // 100 + 80 + 50 + 20 = 250
      expect(screen.getByText('250')).toBeInTheDocument()
      expect(screen.getByText('Total Views')).toBeInTheDocument()
    })
  })

  it('calculates total reactions', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // 15 + 12 + 8 + 2 = 37
      expect(screen.getByText('37')).toBeInTheDocument()
      expect(screen.getByText('Total Reactions')).toBeInTheDocument()
    })
  })

  it('displays user-specific statistics', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // User has 2 submissions (s1, s3)
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('My Submissions')).toBeInTheDocument()

      // User has 1 approved submission (s1)
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('My Wins')).toBeInTheDocument()
    })
  })

  it('calculates submission streak', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // Based on mock data, user should have a streak
      expect(screen.getByText(/week streak/i)).toBeInTheDocument()
    })
  })

  it('displays category breakdown', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // Should show achievement (2), cost_saving (1), recognition (1)
      expect(screen.getByText('achievement')).toBeInTheDocument()
      expect(screen.getByText('cost_saving')).toBeInTheDocument()
      expect(screen.getByText('recognition')).toBeInTheDocument()
    })
  })

  it('shows top wins sorted by engagement', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // s2 should be first (highest combined score)
      const winTitles = screen.getAllByRole('heading', { level: 4 })
      expect(winTitles[0]).toHaveTextContent('Cost Savings Initiative')
    })
  })

  it('displays recent activity', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getByText(/recent submissions/i)).toBeInTheDocument()
      expect(screen.getByText(/recent approvals/i)).toBeInTheDocument()
    })
  })

  it('shows team leaderboard', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument()
      expect(screen.getByText('Jane Doe')).toBeInTheDocument()
    })
  })

  it('handles error state gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    mockWinsStore.fetchSubmissions.mockRejectedValueOnce(new Error('API Error'))

    render(<WinsDashboard />)

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error loading dashboard data:',
        expect.any(Error)
      )
    })

    consoleErrorSpy.mockRestore()
  })

  it('handles empty data state', async () => {
    ;(useWinsStore as any).mockReturnValue({
      ...mockWinsStore,
      submissions: [],
      approvals: [],
    })

    render(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getAllByText('0')[0]).toBeInTheDocument()
      expect(screen.getByText('Total Submissions')).toBeInTheDocument()
    })
  })

  it('filters submissions by status correctly', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // Should show correct counts for each status
      expect(screen.getByText('2')).toBeInTheDocument() // Approved
      expect(screen.getByText('1')).toBeInTheDocument() // Pending
    })
  })

  it('calculates approval rate for team members', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // Test User: 1 approved out of 2 = 50%
      // Jane Doe: 1 approved out of 1 = 100%
      expect(screen.getByText(/50%/)).toBeInTheDocument()
      expect(screen.getByText(/100%/)).toBeInTheDocument()
    })
  })

  it('sorts recent submissions by date', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      const recentSection = screen.getByText(/recent submissions/i).parentElement
      expect(recentSection).toBeInTheDocument()
      // Most recent submission should appear first
    })
  })

  it('displays submission trends over time', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // Should show trend data for multiple weeks
      expect(screen.getByText('Jan 14')).toBeInTheDocument()
    })
  })

  it('updates when data changes', async () => {
    const { rerender } = render(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('4')).toBeInTheDocument()
    })

    // Update store with new submission
    const updatedStore = {
      ...mockWinsStore,
      submissions: [...mockSubmissions, {
        id: 's5',
        userId: 'user-1',
        title: 'New Achievement',
        category: 'achievement',
        status: 'pending',
        submittedAt: new Date().toISOString(),
      }],
    }
    ;(useWinsStore as any).mockReturnValue(updatedStore)

    rerender(<WinsDashboard />)

    await waitFor(() => {
      expect(screen.getByText('5')).toBeInTheDocument()
    })
  })

  it('shows engagement score calculation', async () => {
    render(<WinsDashboard />)

    await waitFor(() => {
      // Top wins should be sorted by engagement score
      // Score = views + reactions * 2 + rating * 5
      const topWins = screen.getAllByRole('article')
      expect(topWins.length).toBeGreaterThan(0)
    })
  })
})