import { http, HttpResponse } from 'msw'

// Define request handlers for wins API endpoints
export const handlers = [
  // Submissions endpoints
  http.get('/api/submissions', () => {
    return HttpResponse.json({
      submissions: [
        {
          id: '1',
          type: 'achievement',
          title: 'Completed Project Ahead of Schedule',
          description: 'Delivered the new customer portal 2 weeks early',
          submittedBy: '<PERSON>',
          submittedAt: '2024-01-15T10:00:00Z',
          status: 'approved',
          impact: 'high',
          category: 'project',
          likes: 15,
          comments: 3,
        },
        {
          id: '2',
          type: 'cost_saving',
          title: 'Reduced AWS Costs by 30%',
          description: 'Optimized cloud infrastructure resulting in significant savings',
          submittedBy: '<PERSON>',
          submittedAt: '2024-01-14T14:30:00Z',
          status: 'pending',
          impact: 'high',
          category: 'cost',
          savingsAmount: 50000,
          likes: 8,
          comments: 2,
        },
        {
          id: '3',
          type: 'recognition',
          title: 'Outstanding Team Collaboration',
          description: 'Cross-functional team work on the Q4 initiative',
          submittedBy: '<PERSON>',
          submittedAt: '2024-01-13T09:15:00Z',
          status: 'approved',
          impact: 'medium',
          category: 'teamwork',
          recognizedPeople: ['<PERSON>', '<PERSON>'],
          likes: 12,
          comments: 5,
        },
      ],
      total: 3,
    })
  }),

  // Single submission endpoint
  http.get('/api/submissions/:id', ({ params }) => {
    return HttpResponse.json({
      id: params.id,
      type: 'achievement',
      title: 'Completed Project Ahead of Schedule',
      description: 'Delivered the new customer portal 2 weeks early with all features',
      submittedBy: 'John Doe',
      submittedAt: '2024-01-15T10:00:00Z',
      status: 'approved',
      impact: 'high',
      category: 'project',
      likes: 15,
      comments: [
        {
          id: 'c1',
          author: 'Manager',
          text: 'Great work!',
          createdAt: '2024-01-15T11:00:00Z',
        },
      ],
      attachments: [
        {
          id: 'a1',
          name: 'project-metrics.pdf',
          url: '/files/project-metrics.pdf',
          type: 'application/pdf',
        },
      ],
    })
  }),

  // Create submission endpoint
  http.post('/api/submissions', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      id: '4',
      ...body,
      submittedAt: new Date().toISOString(),
      status: 'pending',
      likes: 0,
      comments: 0,
    })
  }),

  // Like submission endpoint
  http.post('/api/submissions/:id/like', ({ params }) => {
    return HttpResponse.json({
      id: params.id,
      likes: 16,
    })
  }),

  // Add comment endpoint
  http.post('/api/submissions/:id/comments', async ({ params, request }) => {
    const body = await request.json()
    return HttpResponse.json({
      id: 'c2',
      submissionId: params.id,
      ...body,
      createdAt: new Date().toISOString(),
    })
  }),

  // Analytics endpoint
  http.get('/api/submissions/analytics', () => {
    return HttpResponse.json({
      totalSubmissions: 156,
      submissionsThisWeek: 12,
      topCategories: {
        achievement: 45,
        cost_saving: 38,
        recognition: 42,
        training_idea: 31,
      },
      engagementRate: 78,
      averageLikes: 10.5,
      weeklyTrends: [
        { week: 'Week 1', submissions: 8 },
        { week: 'Week 2', submissions: 12 },
        { week: 'Week 3', submissions: 10 },
        { week: 'Week 4', submissions: 15 },
      ],
      topContributors: [
        { name: 'John Doe', submissions: 8 },
        { name: 'Jane Smith', submissions: 6 },
        { name: 'Mike Johnson', submissions: 5 },
      ],
    })
  }),

  // Recognition templates endpoint
  http.get('/api/recognition/templates', () => {
    return HttpResponse.json({
      templates: [
        {
          id: 't1',
          title: 'Team Player',
          description: 'For exceptional collaboration and teamwork',
          icon: 'users',
        },
        {
          id: 't2',
          title: 'Innovation Champion',
          description: 'For creative solutions and new ideas',
          icon: 'lightbulb',
        },
        {
          id: 't3',
          title: 'Customer Hero',
          description: 'For going above and beyond for customers',
          icon: 'heart',
        },
      ],
    })
  }),

  // Export report endpoint
  http.post('/api/submissions/export', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      url: `/downloads/wins-report-${body.format}.${body.format}`,
      filename: `wins-report-${new Date().toISOString()}.${body.format}`,
    })
  }),
]