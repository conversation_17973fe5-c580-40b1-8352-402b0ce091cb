import { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { RouterProvider, createMemoryRouter } from '@tanstack/react-router'
import userEvent from '@testing-library/user-event'

// Create a custom render function that includes providers
export function customRender(
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  // Create a memory router for testing
  const router = createMemoryRouter({
    routeTree: {
      children: [
        {
          path: '/',
          component: () => ui,
        },
      ],
    },
  })

  return {
    user: userEvent.setup(),
    ...render(<RouterProvider router={router} />, options),
  }
}

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }