# ===================================================================
# Wins of Week (Team Achievement Platform) - Environment Configuration
# ===================================================================
# Wins of Week is a React-based platform for tracking team achievements,
# celebrating successes, and generating performance reports.

# ===================================================================
# Application Configuration
# ===================================================================
VITE_APP_TITLE=Wins of Week Platform
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=development

# ===================================================================
# API Configuration
# ===================================================================
# Main backend API endpoint (Command-Center)
VITE_API_BASE_URL=http://localhost:3000/api

# Wins-specific endpoints
VITE_SUBMISSIONS_API_URL=http://localhost:3000/api/wins/submissions
VITE_REPORTS_API_URL=http://localhost:3000/api/wins/reports
VITE_ANALYTICS_API_URL=http://localhost:3000/api/wins/analytics
VITE_SETTINGS_API_URL=http://localhost:3000/api/wins/settings

# API settings
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RATE_LIMIT=200

# ===================================================================
# Authentication Configuration
# ===================================================================
# User authentication
VITE_AUTH_TOKEN_KEY=wins_auth_token
VITE_SESSION_TIMEOUT=60
VITE_ENABLE_SSO=true

# Role-based access
VITE_ENABLE_ROLE_BASED_ACCESS=true
VITE_DEFAULT_USER_ROLE=employee
VITE_ADMIN_ROLES=admin,hr_manager,team_lead

# ===================================================================
# Submissions Management
# ===================================================================
# Submission configuration
VITE_ENABLE_WEEKLY_SUBMISSIONS=true
VITE_SUBMISSION_DEADLINE_DAY=friday
VITE_SUBMISSION_DEADLINE_TIME=17:00
VITE_ALLOW_LATE_SUBMISSIONS=true

# Submission content
VITE_MAX_SUBMISSION_LENGTH=1000
VITE_ENABLE_RICH_TEXT_EDITOR=true
VITE_ENABLE_ATTACHMENTS=true
VITE_MAX_ATTACHMENT_SIZE_MB=5

# Submission categories
VITE_ENABLE_SUBMISSION_CATEGORIES=true
VITE_DEFAULT_CATEGORIES=achievement,milestone,innovation,collaboration,customer_feedback

# ===================================================================
# Team Management
# ===================================================================
# Team structure
VITE_ENABLE_TEAM_HIERARCHY=true
VITE_MAX_TEAM_LEVELS=5
VITE_ENABLE_CROSS_FUNCTIONAL_TEAMS=true

# Team participation
VITE_ENABLE_TEAM_SUBMISSIONS=true
VITE_ENABLE_INDIVIDUAL_SUBMISSIONS=true
VITE_ENABLE_COLLABORATIVE_SUBMISSIONS=true

# Team analytics
VITE_ENABLE_TEAM_COMPARISONS=true
VITE_ENABLE_TEAM_RANKINGS=true
VITE_ENABLE_TEAM_TRENDS=true

# ===================================================================
# Recognition and Rewards
# ===================================================================
# Recognition system
VITE_ENABLE_PEER_RECOGNITION=true
VITE_ENABLE_MANAGER_RECOGNITION=true
VITE_ENABLE_POINTS_SYSTEM=false

# Badges and achievements
VITE_ENABLE_BADGE_SYSTEM=true
VITE_ENABLE_ACHIEVEMENT_LEVELS=true
VITE_ENABLE_STREAK_TRACKING=true

# Rewards
VITE_ENABLE_REWARD_SYSTEM=false
VITE_REWARD_TYPES=gift_card,extra_pto,certificate

# ===================================================================
# Reporting and Analytics
# ===================================================================
# Report types
VITE_ENABLE_WEEKLY_REPORTS=true
VITE_ENABLE_MONTHLY_REPORTS=true
VITE_ENABLE_QUARTERLY_REPORTS=true
VITE_ENABLE_ANNUAL_REPORTS=true

# Dashboard analytics
VITE_ENABLE_DASHBOARD_ANALYTICS=true
VITE_ENABLE_TREND_ANALYSIS=true
VITE_ENABLE_PERFORMANCE_METRICS=true

# Export options
VITE_ENABLE_REPORT_EXPORT=true
VITE_EXPORT_FORMATS=pdf,excel,csv,ppt

# ===================================================================
# Communication Features
# ===================================================================
# Notifications
VITE_ENABLE_SUBMISSION_REMINDERS=true
VITE_REMINDER_FREQUENCY_DAYS=2,1,0
VITE_ENABLE_DEADLINE_NOTIFICATIONS=true

# Sharing and visibility
VITE_ENABLE_PUBLIC_SHARING=true
VITE_ENABLE_TEAM_SHARING=true
VITE_ENABLE_DEPARTMENT_SHARING=true

# Comments and feedback
VITE_ENABLE_COMMENTS=true
VITE_ENABLE_LIKES_REACTIONS=true
VITE_ENABLE_FEEDBACK_SYSTEM=true

# ===================================================================
# Dashboard Configuration
# ===================================================================
# Dashboard views
VITE_DEFAULT_DASHBOARD=overview
VITE_ENABLE_PERSONAL_DASHBOARD=true
VITE_ENABLE_TEAM_DASHBOARD=true
VITE_ENABLE_MANAGER_DASHBOARD=true

# Widget configuration
VITE_ENABLE_DASHBOARD_CUSTOMIZATION=true
VITE_WIDGET_REFRESH_INTERVAL=300000
VITE_MAX_WIDGETS_PER_DASHBOARD=12

# ===================================================================
# Settings and Customization
# ===================================================================
# Organizational settings
VITE_ENABLE_CUSTOM_CATEGORIES=true
VITE_ENABLE_CUSTOM_FIELDS=true
VITE_ENABLE_WORKFLOW_CUSTOMIZATION=true

# Branding
VITE_ENABLE_CUSTOM_BRANDING=true
VITE_ENABLE_COMPANY_LOGO=true
VITE_ENABLE_COLOR_CUSTOMIZATION=true

# Localization
VITE_ENABLE_MULTI_LANGUAGE=false
VITE_DEFAULT_LANGUAGE=en
VITE_SUPPORTED_LANGUAGES=en,ar

# ===================================================================
# Performance Tracking
# ===================================================================
# Metrics collection
VITE_ENABLE_PERFORMANCE_TRACKING=true
VITE_TRACK_SUBMISSION_FREQUENCY=true
VITE_TRACK_ENGAGEMENT_METRICS=true

# Goal setting
VITE_ENABLE_GOAL_SETTING=true
VITE_ENABLE_TARGET_TRACKING=true
VITE_ENABLE_PROGRESS_VISUALIZATION=true

# ===================================================================
# Integration Features
# ===================================================================
# Calendar integration
VITE_ENABLE_CALENDAR_INTEGRATION=false
VITE_CALENDAR_PROVIDER=google
VITE_SYNC_SUBMISSION_DEADLINES=true

# Slack integration
VITE_ENABLE_SLACK_INTEGRATION=false
VITE_SLACK_WEBHOOK_URL=
VITE_SLACK_NOTIFICATIONS=true

# Email integration
VITE_ENABLE_EMAIL_INTEGRATION=true
VITE_EMAIL_TEMPLATE_CUSTOMIZATION=true

# ===================================================================
# UI/UX Configuration
# ===================================================================
# Theme and appearance
VITE_DEFAULT_THEME=light
VITE_ENABLE_THEME_SWITCHING=true
VITE_ENABLE_DARK_MODE=true

# Layout preferences
VITE_DEFAULT_VIEW=card
VITE_ENABLE_LIST_VIEW=true
VITE_ENABLE_GRID_VIEW=true
VITE_ENABLE_TIMELINE_VIEW=true

# User experience
VITE_ENABLE_GUIDED_TOUR=true
VITE_ENABLE_TOOLTIPS=true
VITE_ENABLE_KEYBOARD_SHORTCUTS=true

# ===================================================================
# Data Management
# ===================================================================
# Data retention
VITE_DATA_RETENTION_MONTHS=24
VITE_ENABLE_DATA_ARCHIVING=true
VITE_ENABLE_DATA_EXPORT=true

# Backup and sync
VITE_ENABLE_AUTO_BACKUP=true
VITE_BACKUP_FREQUENCY_HOURS=24
VITE_ENABLE_CLOUD_SYNC=false

# ===================================================================
# Security Configuration
# ===================================================================
# Data protection
VITE_ENABLE_DATA_ENCRYPTION=true
VITE_ENABLE_SECURE_STORAGE=true
VITE_ENABLE_AUDIT_LOGGING=true

# Privacy settings
VITE_ENABLE_ANONYMOUS_SUBMISSIONS=false
VITE_ENABLE_PRIVACY_CONTROLS=true
VITE_DATA_ANONYMIZATION=false

# ===================================================================
# External Services
# ===================================================================
# Error tracking
VITE_SENTRY_DSN=

# Analytics
VITE_GOOGLE_ANALYTICS_ID=
VITE_ENABLE_USER_ANALYTICS=true

# Support
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_HELP_DESK_URL=
VITE_DOCUMENTATION_URL=

# ===================================================================
# Cross-App Integration
# ===================================================================
# Integration with other Luminar applications
VITE_AMNA_URL=http://localhost:5173
VITE_ECONNECT_URL=http://localhost:5174
VITE_LIGHTHOUSE_URL=http://localhost:5175
VITE_DASHBOARD_URL=http://localhost:5176
VITE_TRAINING_URL=http://localhost:5177
VITE_VENDORS_URL=http://localhost:5178

# Shared services
VITE_SHARED_AUTH_ENABLED=true
VITE_SHARED_AUTH_DOMAIN=localhost
VITE_CROSS_APP_NAVIGATION=true

# Data sharing
VITE_ENABLE_CROSS_APP_DATA_SHARING=true
VITE_SHARE_ACHIEVEMENTS_WITH_TNA=true
VITE_SHARE_METRICS_WITH_DASHBOARD=true

# ===================================================================
# Development Configuration
# ===================================================================
# Development server
VITE_DEV_PORT=5179
VITE_HMR_PORT=5179

# Development tools
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_ROUTER_DEVTOOLS=true

# Debug settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
VITE_ENABLE_MOCK_DATA=false

# ===================================================================
# Testing Configuration
# ===================================================================
# Test environment
VITE_TEST_MODE=false
VITE_ENABLE_MSW=false
VITE_TEST_API_URL=http://localhost:3001/api

# Test data
VITE_USE_MOCK_SUBMISSIONS=false
VITE_MOCK_SUBMISSIONS_COUNT=50
VITE_MOCK_USERS_COUNT=25

# ===================================================================
# Performance Configuration
# ===================================================================
# Caching
VITE_CACHE_STRATEGY=memory
VITE_CACHE_DURATION=1800000
VITE_ENABLE_OFFLINE_MODE=false

# Loading optimization
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_PAGINATION_SIZE=20

# ===================================================================
# Build Configuration
# ===================================================================
# Build settings
VITE_BUILD_OUTPUT_DIR=dist
VITE_PUBLIC_PATH=/

# Optimization
VITE_ENABLE_MINIFICATION=true
VITE_ENABLE_COMPRESSION=true
VITE_GENERATE_SOURCE_MAPS=true

# Bundle analysis
VITE_BUNDLE_ANALYZER=false
VITE_CHUNK_SIZE_WARNING_LIMIT=500