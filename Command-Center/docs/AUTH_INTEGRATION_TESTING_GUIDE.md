# Authentication Integration Testing Guide

## Overview

This guide provides comprehensive testing procedures for the Luminar L&D Platform authentication integration, covering JWT cross-app access, Redis session management, and CORS configuration.

## Table of Contents

1. [Test Environment Setup](#test-environment-setup)
2. [JWT Cross-App Access Testing](#jwt-cross-app-access-testing)
3. [Redis Session Management Testing](#redis-session-management-testing)
4. [CORS Configuration Testing](#cors-configuration-testing)
5. [Integration Testing](#integration-testing)
6. [Security Testing](#security-testing)
7. [Performance Testing](#performance-testing)
8. [Troubleshooting](#troubleshooting)

## Test Environment Setup

### Prerequisites

```bash
# Required services
- Redis server running on localhost:6379
- PostgreSQL database
- Node.js v18+
- All L&D applications running on different ports

# Environment Variables
export JWT_SECRET="your-jwt-secret-key"
export CROSS_APP_JWT_SECRET="your-cross-app-jwt-secret"
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export NODE_ENV="development"

# Application Ports
- Command Center: http://localhost:3000
- Learning Dashboard: http://localhost:3001
- Skills Assessment: http://localhost:3002
- Talent Analytics: http://localhost:3003
- Admin Portal: http://localhost:3004
```

### Setup Commands

```bash
# 1. Install dependencies
npm install

# 2. Start Redis
redis-server

# 3. Start PostgreSQL
pg_ctl start

# 4. Run database migrations
npm run typeorm:migration:run

# 5. Start the Command Center API
npm run start:dev

# 6. Build and start shared auth library
cd shared-auth
npm run build
```

## JWT Cross-App Access Testing

### Test 1: Generate Cross-App Token

```bash
# Login to get regular JWT token
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Generate cross-app token for learning dashboard
curl -X POST http://localhost:3000/auth/cross-app/token/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "targetAppId": "learning-dashboard",
    "permissions": ["view_courses", "take_assessments"]
  }'
```

**Expected Result:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "targetApp": "learning-dashboard",
    "permissions": ["view_courses", "take_assessments"],
    "expiresIn": 3600
  }
}
```

### Test 2: Verify Cross-App Token

```bash
curl -X POST http://localhost:3000/auth/cross-app/token/verify \
  -H "Content-Type: application/json" \
  -d '{
    "token": "CROSS_APP_TOKEN",
    "appId": "learning-dashboard"
  }'
```

**Expected Result:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "payload": {
      "userId": "user-id",
      "email": "<EMAIL>",
      "roles": ["student"],
      "permissions": ["view_courses", "take_assessments"],
      "appId": "learning-dashboard",
      "sessionId": "cross_123456789_abc123"
    }
  }
}
```

### Test 3: Token Exchange

```bash
curl -X POST http://localhost:3000/auth/cross-app/token/exchange \
  -H "Content-Type: application/json" \
  -d '{
    "token": "REGULAR_JWT_TOKEN",
    "targetAppId": "skills-assessment",
    "permissions": ["take_assessments"]
  }'
```

### Test 4: App Configuration Validation

```bash
curl -X POST http://localhost:3000/auth/cross-app/app/validate \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "learning-dashboard",
    "appSecret": "ld_secret_key"
  }'
```

### Test 5: Get Available Apps

```bash
curl -X GET "http://localhost:3000/auth/cross-app/apps/config?origin=http://localhost:3001" \
  -H "Content-Type: application/json"
```

## Redis Session Management Testing

### Test 1: Create Session

```bash
# This happens automatically during login, but you can test directly:
curl -X POST http://localhost:3000/auth/sessions/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "ipAddress": "127.0.0.1",
    "userAgent": "Mozilla/5.0...",
    "deviceId": "device123",
    "loginMethod": "password"
  }'
```

### Test 2: Get User Sessions

```bash
curl -X GET http://localhost:3000/auth/sessions/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Result:**
```json
{
  "success": true,
  "data": {
    "userId": "user-id",
    "totalSessions": 2,
    "sessions": [
      {
        "deviceId": "device123",
        "userAgent": "Mozilla/5.0...",
        "ipAddress": "127.0.0.1",
        "lastActivity": "2024-01-15T10:30:00.000Z",
        "createdAt": "2024-01-15T09:00:00.000Z",
        "expiresAt": "2024-01-16T09:00:00.000Z",
        "isActive": true,
        "loginMethod": "password"
      }
    ]
  }
}
```

### Test 3: Update Session Activity

```bash
curl -X POST http://localhost:3000/auth/sessions/SESSION_ID/activity \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "ipAddress": "*************",
    "userAgent": "Updated User Agent",
    "metadata": {
      "lastPage": "/dashboard",
      "action": "page_view"
    }
  }'
```

### Test 4: Extend Session

```bash
curl -X POST http://localhost:3000/auth/sessions/SESSION_ID/extend \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "additionalSeconds": 7200
  }'
```

### Test 5: Destroy Session

```bash
curl -X DELETE http://localhost:3000/auth/sessions/SESSION_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Test 6: Session Metrics (Admin)

```bash
curl -X GET http://localhost:3000/auth/sessions/metrics/overview \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

### Test 7: Redis Integration Verification

```bash
# Connect to Redis CLI
redis-cli

# Check session keys
KEYS session:*

# Check user sessions
KEYS user_sessions:*

# Check active sessions
SMEMBERS active_sessions

# Check session data
GET session:your-session-id

# Check session TTL
TTL session:your-session-id
```

## CORS Configuration Testing

### Test 1: Validate CORS Request

```bash
curl -X POST http://localhost:3000/auth/cors/validate \
  -H "Content-Type: application/json" \
  -d '{
    "origin": "http://localhost:3001",
    "method": "POST",
    "headers": ["Content-Type", "Authorization", "X-Cross-App-Token"],
    "appId": "learning-dashboard"
  }'
```

**Expected Result:**
```json
{
  "success": true,
  "data": {
    "allowed": true,
    "appId": "learning-dashboard",
    "headers": {
      "Access-Control-Allow-Origin": "http://localhost:3001",
      "Access-Control-Allow-Methods": "GET, POST, PUT, PATCH, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Origin, Content-Type, Authorization, X-Cross-App-Token",
      "Access-Control-Allow-Credentials": "true",
      "Access-Control-Max-Age": "86400"
    }
  }
}
```

### Test 2: Preflight Request Testing

```bash
# Test preflight request for learning dashboard
curl -X OPTIONS http://localhost:3000/auth/cross-app/token/generate \
  -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type, Authorization" \
  -v
```

**Expected Headers in Response:**
```
Access-Control-Allow-Origin: http://localhost:3001
Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-Cross-App-Token
Access-Control-Allow-Credentials: true
Access-Control-Max-Age: 86400
```

### Test 3: Get CORS Configuration

```bash
curl -X GET http://localhost:3000/auth/cors/config \
  -H "Content-Type: application/json"
```

### Test 4: Check Origin Allowance

```bash
curl -X GET "http://localhost:3000/auth/cors/check/origin?origin=http://localhost:3001" \
  -H "Content-Type: application/json"
```

### Test 5: Browser CORS Testing

Create an HTML test file:

```html
<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <script>
        // Test cross-origin request
        async function testCORS() {
            try {
                const response = await fetch('http://localhost:3000/auth/cross-app/apps/config', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                console.log('CORS test successful:', data);
            } catch (error) {
                console.error('CORS test failed:', error);
            }
        }
        
        testCORS();
    </script>
</body>
</html>
```

Serve this file from different origins (3001, 3002, etc.) and test.

## Integration Testing

### Test 1: Complete SSO Flow

```bash
# Step 1: Login to Command Center
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Step 2: Generate cross-app token for learning dashboard
curl -X POST http://localhost:3000/auth/cross-app/token/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer COMMAND_CENTER_TOKEN" \
  -d '{
    "targetAppId": "learning-dashboard",
    "permissions": ["view_courses"]
  }'

# Step 3: Use cross-app token in learning dashboard
curl -X GET http://localhost:3001/api/courses \
  -H "Authorization: Bearer CROSS_APP_TOKEN" \
  -H "Origin: http://localhost:3001"
```

### Test 2: Session Synchronization

```bash
# Create session in Command Center
# Then verify session exists in Redis
redis-cli KEYS "session:*"

# Update session activity
curl -X POST http://localhost:3000/auth/sessions/SESSION_ID/activity \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"metadata": {"test": "integration"}}'

# Verify in Redis
redis-cli GET session:SESSION_ID
```

### Test 3: Cross-App Logout

```bash
# Generate logout tokens for all apps
curl -X POST http://localhost:3000/auth/cross-app/logout/all \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Verify session destruction
curl -X GET http://localhost:3000/auth/sessions/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Security Testing

### Test 1: Invalid JWT Tokens

```bash
# Test with invalid token
curl -X POST http://localhost:3000/auth/cross-app/token/generate \
  -H "Authorization: Bearer invalid_token" \
  -d '{"targetAppId": "learning-dashboard"}'

# Expected: 401 Unauthorized
```

### Test 2: CORS Origin Validation

```bash
# Test with unauthorized origin
curl -X OPTIONS http://localhost:3000/auth/cross-app/token/generate \
  -H "Origin: http://malicious-site.com" \
  -H "Access-Control-Request-Method: POST"

# Expected: CORS error
```

### Test 3: Cross-App Token Misuse

```bash
# Try to use learning dashboard token for skills assessment
curl -X POST http://localhost:3000/auth/cross-app/token/verify \
  -d '{
    "token": "LEARNING_DASHBOARD_TOKEN",
    "appId": "skills-assessment"
  }'

# Expected: Invalid token error
```

### Test 4: Session Hijacking Prevention

```bash
# Create session from one IP
curl -X POST http://localhost:3000/auth/login \
  -H "X-Forwarded-For: *************" \
  -d '{"email": "<EMAIL>", "password": "password123"}'

# Try to use session from different IP
curl -X GET http://localhost:3000/auth/sessions/me \
  -H "Authorization: Bearer TOKEN" \
  -H "X-Forwarded-For: ********"

# Verify suspicious activity detection
```

## Performance Testing

### Test 1: JWT Token Generation Performance

```bash
# Test token generation speed
time curl -X POST http://localhost:3000/auth/cross-app/token/generate \
  -H "Authorization: Bearer TOKEN" \
  -d '{"targetAppId": "learning-dashboard"}'

# Run multiple times and measure average
for i in {1..100}; do
  curl -X POST http://localhost:3000/auth/cross-app/token/generate \
    -H "Authorization: Bearer TOKEN" \
    -d '{"targetAppId": "learning-dashboard"}' \
    -w "%{time_total}\n" -o /dev/null -s
done
```

### Test 2: Redis Session Performance

```bash
# Test session creation performance
for i in {1..1000}; do
  curl -X POST http://localhost:3000/auth/login \
    -d "{\"email\": \"user$<EMAIL>\", \"password\": \"password123\"}" \
    -w "%{time_total}\n" -o /dev/null -s
done

# Check Redis memory usage
redis-cli INFO memory

# Check active sessions count
redis-cli SCARD active_sessions
```

### Test 3: CORS Preflight Cache

```bash
# First preflight request (should be slower)
time curl -X OPTIONS http://localhost:3000/api/test \
  -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: POST"

# Second request within cache period (should be faster)
time curl -X OPTIONS http://localhost:3000/api/test \
  -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: POST"
```

## Troubleshooting

### Common Issues and Solutions

#### Issue 1: CORS Preflight Failures

**Symptoms:**
- Browser console shows CORS errors
- OPTIONS requests failing

**Solution:**
```bash
# Check CORS configuration
curl -X GET http://localhost:3000/auth/cors/config

# Verify origin is allowed
curl -X GET "http://localhost:3000/auth/cors/check/origin?origin=YOUR_ORIGIN"

# Check server logs for CORS violations
```

#### Issue 2: Cross-App Token Validation Failures

**Symptoms:**
- Tokens rejected by target applications
- Invalid signature errors

**Solution:**
```bash
# Verify cross-app secret configuration
echo $CROSS_APP_JWT_SECRET

# Check token payload
node -e "console.log(JSON.stringify(require('jsonwebtoken').decode('YOUR_TOKEN'), null, 2))"

# Verify app configuration
curl -X POST http://localhost:3000/auth/cross-app/app/validate \
  -d '{"appId": "target-app", "appSecret": "secret"}'
```

#### Issue 3: Redis Session Issues

**Symptoms:**
- Sessions not persisting
- High memory usage in Redis

**Solution:**
```bash
# Check Redis connection
redis-cli ping

# Monitor Redis commands
redis-cli monitor

# Check session TTL
redis-cli TTL session:SESSION_ID

# Clean up expired sessions
curl -X POST http://localhost:3000/auth/sessions/cleanup/expired \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### Issue 4: High Memory Usage

**Symptoms:**
- Redis memory warnings
- Slow session operations

**Solution:**
```bash
# Check Redis memory info
redis-cli INFO memory

# Count active sessions
redis-cli SCARD active_sessions

# Clean up old sessions
redis-cli EVAL "
  local keys = redis.call('KEYS', 'session:*')
  local deleted = 0
  for i=1,#keys do
    if redis.call('TTL', keys[i]) < 0 then
      redis.call('DEL', keys[i])
      deleted = deleted + 1
    end
  end
  return deleted
" 0
```

### Debugging Commands

```bash
# Enable debug logging
export DEBUG=auth:*

# Check JWT token validity
node -e "
  const jwt = require('jsonwebtoken');
  try {
    const payload = jwt.verify('YOUR_TOKEN', 'YOUR_SECRET');
    console.log('Valid token:', payload);
  } catch (err) {
    console.log('Invalid token:', err.message);
  }
"

# Monitor Redis operations
redis-cli --latency-history

# Check application logs
tail -f logs/application.log | grep -E "(CORS|JWT|Session)"
```

### Health Checks

```bash
# Auth service health
curl -X GET http://localhost:3000/auth/health

# Cross-app auth health
curl -X GET http://localhost:3000/auth/cross-app/health

# Session service health
curl -X GET http://localhost:3000/auth/sessions/health/check

# CORS service health
curl -X GET http://localhost:3000/auth/cors/health

# Redis health
redis-cli ping
```

## Test Automation Scripts

### Automated Test Suite

Create `test-auth-integration.sh`:

```bash
#!/bin/bash

echo "Starting Authentication Integration Tests..."

# Configuration
API_BASE="http://localhost:3000"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="password123"

# Test 1: Login and get token
echo "Test 1: Login and get JWT token"
LOGIN_RESPONSE=$(curl -s -X POST $API_BASE/auth/login \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$TEST_EMAIL\", \"password\": \"$TEST_PASSWORD\"}")

JWT_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.accessToken')
if [ "$JWT_TOKEN" != "null" ] && [ "$JWT_TOKEN" != "" ]; then
    echo "✅ Login successful"
else
    echo "❌ Login failed"
    exit 1
fi

# Test 2: Generate cross-app token
echo "Test 2: Generate cross-app token"
CROSS_APP_RESPONSE=$(curl -s -X POST $API_BASE/auth/cross-app/token/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{"targetAppId": "learning-dashboard", "permissions": ["view_courses"]}')

CROSS_APP_TOKEN=$(echo $CROSS_APP_RESPONSE | jq -r '.data.token')
if [ "$CROSS_APP_TOKEN" != "null" ] && [ "$CROSS_APP_TOKEN" != "" ]; then
    echo "✅ Cross-app token generated"
else
    echo "❌ Cross-app token generation failed"
    exit 1
fi

# Test 3: Verify cross-app token
echo "Test 3: Verify cross-app token"
VERIFY_RESPONSE=$(curl -s -X POST $API_BASE/auth/cross-app/token/verify \
  -H "Content-Type: application/json" \
  -d "{\"token\": \"$CROSS_APP_TOKEN\", \"appId\": \"learning-dashboard\"}")

VALID=$(echo $VERIFY_RESPONSE | jq -r '.data.valid')
if [ "$VALID" == "true" ]; then
    echo "✅ Cross-app token verification successful"
else
    echo "❌ Cross-app token verification failed"
    exit 1
fi

# Test 4: Check user sessions
echo "Test 4: Check user sessions"
SESSIONS_RESPONSE=$(curl -s -X GET $API_BASE/auth/sessions/me \
  -H "Authorization: Bearer $JWT_TOKEN")

SESSION_COUNT=$(echo $SESSIONS_RESPONSE | jq -r '.data.totalSessions')
if [ "$SESSION_COUNT" -gt 0 ]; then
    echo "✅ User sessions retrieved (count: $SESSION_COUNT)"
else
    echo "❌ No user sessions found"
    exit 1
fi

# Test 5: CORS validation
echo "Test 5: CORS validation"
CORS_RESPONSE=$(curl -s -X POST $API_BASE/auth/cors/validate \
  -H "Content-Type: application/json" \
  -d '{"origin": "http://localhost:3001", "method": "POST", "headers": ["Content-Type", "Authorization"]}')

CORS_ALLOWED=$(echo $CORS_RESPONSE | jq -r '.data.allowed')
if [ "$CORS_ALLOWED" == "true" ]; then
    echo "✅ CORS validation successful"
else
    echo "❌ CORS validation failed"
    exit 1
fi

echo "🎉 All authentication integration tests passed!"
```

Make it executable:
```bash
chmod +x test-auth-integration.sh
./test-auth-integration.sh
```

## Conclusion

This testing guide covers all aspects of the authentication integration system. Regular execution of these tests ensures the reliability and security of the cross-app authentication, session management, and CORS configuration.

For continuous integration, consider automating these tests in your CI/CD pipeline and setting up monitoring alerts for authentication failures.