# Authentication Integration Environment Configuration
# Copy this file to .env and update with your values

# === JWT Configuration ===
JWT_SECRET=your-super-secure-jwt-secret-key-min-32-chars
JWT_EXPIRES_IN=15m
JWT_EXPIRES_IN_SECONDS=900
JWT_ISSUER=command-center
JWT_AUDIENCE=command-center-api

# Cross-App JWT Configuration
CROSS_APP_JWT_SECRET=your-cross-app-jwt-secret-key-different-from-main
PASSWORD_RESET_SECRET=your-password-reset-secret-key
EMAIL_VERIFICATION_SECRET=your-email-verification-secret-key

# Refresh Token Configuration
REFRESH_TOKEN_DAYS=30

# === Session Configuration ===
SESSION_TTL_SECONDS=86400
MAX_SESSIONS_PER_USER=5

# === Redis Configuration ===
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# === CORS Configuration ===
# Comma-separated list of additional allowed origins
CORS_ORIGINS=https://your-production-domain.com,https://additional-domain.com

# === L&D App Secrets ===
# These are used for app-to-app authentication
CC_APP_SECRET=command-center-app-secret-key
LD_APP_SECRET=learning-dashboard-app-secret-key
SA_APP_SECRET=skills-assessment-app-secret-key
TA_APP_SECRET=talent-analytics-app-secret-key
AP_APP_SECRET=admin-portal-app-secret-key

# === API Keys for Service-to-Service Communication ===
VALID_API_KEYS=cc_api_key_123,ld_api_key_456,sa_api_key_789

# === Database Configuration ===
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=luminar_user
DATABASE_PASSWORD=your-database-password
DATABASE_NAME=luminar_db

# === Environment ===
NODE_ENV=development
PORT=3000

# === Rate Limiting ===
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# === Security Headers ===
HELMET_CSP_DIRECTIVES=default-src 'self'; script-src 'self' 'unsafe-inline'

# === Logging ===
LOG_LEVEL=info
LOG_FORMAT=combined

# === Monitoring ===
ENABLE_METRICS=true
METRICS_PORT=9464

# === SSL/TLS (Production) ===
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/private.key

# === Production Security ===
# Set to true in production to enforce stricter security
ENFORCE_HTTPS=false
SECURE_COOKIES=false
SAME_SITE_COOKIES=lax

# === Cross-App Configuration ===
# Learning Dashboard
LD_BASE_URL=http://localhost:3001
LD_API_ENDPOINT=/api/v1

# Skills Assessment
SA_BASE_URL=http://localhost:3002
SA_API_ENDPOINT=/api/v1

# Talent Analytics
TA_BASE_URL=http://localhost:3003
TA_API_ENDPOINT=/api/v1

# Admin Portal
AP_BASE_URL=http://localhost:3004
AP_API_ENDPOINT=/api/v1

# === Feature Flags ===
ENABLE_CROSS_APP_AUTH=true
ENABLE_REDIS_SESSION=true
ENABLE_CORS_VALIDATION=true
ENABLE_SESSION_METRICS=true
ENABLE_SECURITY_MONITORING=true

# === Development Settings ===
DEBUG_AUTH=false
DEBUG_CORS=false
DEBUG_SESSIONS=false

# Mock external services in development
MOCK_EXTERNAL_APIS=false

# === Production Settings ===
# Uncomment and set for production deployment

# SSL Configuration
# HTTPS_PORT=443
# FORCE_HTTPS=true

# Production CORS Origins
# PROD_CORS_ORIGINS=https://command-center.luminar.app,https://learn.luminar.app,https://assess.luminar.app,https://analytics.luminar.app,https://admin.luminar.app

# Production Redis (if using Redis Cloud or similar)
# REDIS_URL=redis://username:password@host:port/db

# Production Database
# DATABASE_URL=postgresql://username:password@host:port/database

# CDN and Asset URLs
# CDN_URL=https://cdn.luminar.app
# STATIC_ASSETS_URL=https://assets.luminar.app

# === Health Check Configuration ===
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# === Backup Configuration ===
AUTO_BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# === Analytics and Monitoring ===
GOOGLE_ANALYTICS_ID=
SENTRY_DSN=
NEW_RELIC_LICENSE_KEY=

# === Email Configuration (for password reset, etc.) ===
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# === File Upload Configuration ===
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# === Cache Configuration ===
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000

# === Webhook Configuration ===
WEBHOOK_SECRET=your-webhook-secret-key
WEBHOOK_TIMEOUT=5000