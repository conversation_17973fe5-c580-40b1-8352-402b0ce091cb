# Database Migration & Seeding Quick Guide

## Quick Commands

### Essential Commands
```bash
# Generate Prisma client after schema changes
npm run prisma:generate

# Deploy current schema to database (development)
npm run prisma:db:push

# Seed database with test data
npm run prisma:seed

# Check migration status
npx prisma migrate status

# Open database browser
npm run prisma:studio
```

### Migration Commands
```bash
# Create and apply new migration (interactive)
npm run prisma:migrate:dev

# Apply pending migrations (production)
npm run prisma:migrate:deploy

# Reset database and apply all migrations
npm run prisma:migrate:reset

# Create baseline migration from existing database
npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma --script > prisma/migrations/YYYYMMDD_migration_name/migration.sql
npx prisma migrate resolve --applied MIGRATION_NAME
```

## Setup New Environment

### 1. Database Setup
```bash
# Ensure PostgreSQL is running with correct database
createdb command_center  # if database doesn't exist

# Verify connection
npm run prisma:generate
```

### 2. Deploy Schema
```bash
# For development (quick setup)
npm run prisma:db:push

# For production (with migration history)
npm run prisma:migrate:deploy
```

### 3. Seed Data (Development Only)
```bash
npm run prisma:seed
```

### 4. Verify Setup
```bash
npx prisma migrate status
npm run prisma:studio  # Opens at http://localhost:5555
```

## Seed Script Details

### What Gets Created
- **5 Departments**: Engineering, Product, Marketing, Sales, HR
- **7 Roles**: From Junior to Executive levels
- **6 Skills**: Technical (JS, TS, React, Node.js) + Soft Skills
- **5 Users**: Complete profiles with manager relationships
- **4 Training Courses**: With providers, costs, and skill mappings
- **2 Learning Paths**: Career progression tracks
- **Complete L&D Ecosystem**: Assessments, enrollments, skill gaps, budgets

### Sample Users
- **Alice Johnson**: Senior Software Engineer (<EMAIL>)
- **Bob Smith**: Junior Software Engineer (<EMAIL>)
- **Charlie Brown**: Engineering Manager (<EMAIL>)
- **Diana Prince**: Product Manager (<EMAIL>)
- **Eve Adams**: Marketing Specialist (<EMAIL>)

### Realistic Scenarios
- Bob has skill gaps in TypeScript and React
- Alice completed React Performance course (95.5% score)
- Bob is 40% through TypeScript Fundamentals
- Engineering has Q1 budget: $50K total, $12.5K spent
- Vendor relationships with approved proposals

## Database Schema Structure

### Core L&D Models
```
User → Department, Role, Manager
User → UserSkill → Skill
User → SkillGap → Skill
User → TrainingEnrollment → TrainingCourse
User → AssessmentResponse → Assessment
User → WeeklySubmission
```

### Training System
```
TrainingCourse ← TrainingEnrollment → User
TrainingCourse ← TrainingRecommendation → SkillGap
LearningPath → TrainingCourse, Skill, Role
```

### Vendor Management
```
Vendor → Proposal → Review
Vendor → Review
```

### System Models (Prisma Schema)
```
Analytics, SearchIndex, AuditLog
Workflow → WorkflowExecution
FeatureFlag, ApiKey, Report, Notification
```

## Troubleshooting

### Common Issues

#### Migration Status Issues
```bash
# If "not managed by Prisma Migrate"
npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma --script > prisma/migrations/0_init/migration.sql
npx prisma migrate resolve --applied 0_init
```

#### Seed Script Fails
```bash
# Check database connection
npm run prisma:generate

# Verify schema is deployed
npm run prisma:db:push

# Clear existing data (if needed)
# Note: Seed script automatically cleans data
```

#### Database Connection Issues
```bash
# Check .env file
DATABASE_URL=postgresql://postgres:postgres123!@localhost:5432/command_center

# Verify PostgreSQL is running
pg_ctl status  # or appropriate system command
```

### Reset Everything
```bash
# Complete reset (development only)
npm run prisma:migrate:reset  # Drops all data and migrations
npm run prisma:db:push        # Deploy current schema
npm run prisma:seed           # Populate with test data
```

## Production Deployment

### Environment Setup
1. Set production DATABASE_URL
2. Ensure database exists and is accessible
3. Never run seed script in production

### Migration Deployment
```bash
# Production migration deployment
NODE_ENV=production npm run prisma:migrate:deploy

# Verify deployment
NODE_ENV=production npx prisma migrate status
```

### Backup Strategy
```bash
# Before any production migration
pg_dump command_center > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore if needed
psql command_center < backup_YYYYMMDD_HHMMSS.sql
```

## File Structure
```
prisma/
├── schema.prisma          # Database schema definition
├── seed.ts               # Comprehensive seed script
└── migrations/
    └── 0_init/
        └── migration.sql  # Baseline migration
```

## Next Steps

1. **Service Development**: Create Prisma services for models without TypeORM equivalents
2. **API Development**: Build endpoints for L&D functionality
3. **Testing**: Create comprehensive test suites
4. **Documentation**: Update API documentation with new endpoints