# Real-Time System Monitoring Implementation

## Overview

This implementation successfully replaces the mock data in Luminar-Dashboard with real system monitoring capabilities. The solution provides comprehensive real-time monitoring of system resources, Docker containers, database metrics, and integration health across all L&D applications.

## Architecture

### Backend Components (Command-Center)

#### 1. System Monitoring Module (`src/modules/system-monitoring/`)

**Interfaces** (`interfaces/system-metrics.interface.ts`)
- `SystemMetrics`: Complete system status snapshot
- `CPUMetrics`, `MemoryMetrics`, `DiskMetrics`, `NetworkMetrics`: Hardware monitoring
- `ServiceMetrics`, `DockerMetrics`, `DatabaseMetrics`: Application monitoring
- `IntegrationHealth`: Service health monitoring
- `SystemEvent`: Event tracking and alerting

**Services**
- `SystemMetricsService`: Real system resource collection (CPU, memory, disk, network)
- `IntegrationHealthService`: Health monitoring for all L&D applications
- `SystemEventsService`: Event tracking and real-time notifications

**Controller** (`controllers/system-monitoring.controller.ts`)
- RESTful API endpoints for all monitoring data
- Real-time refresh capabilities
- Health check endpoints

**WebSocket Gateway** (`gateway/monitoring.gateway.ts`)
- Real-time data streaming to dashboard
- Client subscription management
- Event broadcasting

#### 2. API Endpoints

```
GET  /api/system-monitoring/metrics          - Full system metrics
GET  /api/system-monitoring/metrics/cpu      - CPU metrics only
GET  /api/system-monitoring/metrics/memory   - Memory metrics only
GET  /api/system-monitoring/metrics/disk     - Disk metrics only
GET  /api/system-monitoring/metrics/network  - Network metrics only
GET  /api/system-monitoring/metrics/docker   - Docker metrics only
GET  /api/system-monitoring/metrics/database - Database metrics only

GET  /api/system-monitoring/integrations     - All integration health
GET  /api/system-monitoring/integrations/:id - Specific integration health
POST /api/system-monitoring/integrations/:id/refresh - Refresh integration
POST /api/system-monitoring/integrations/refresh     - Refresh all integrations

GET  /api/system-monitoring/events           - System events
POST /api/system-monitoring/events/:id/acknowledge - Acknowledge event

GET  /api/system-monitoring/dashboard        - Dashboard summary
GET  /api/system-monitoring/health           - Monitoring system health
```

#### 3. WebSocket Events

**Client -> Server:**
- `subscribe`: Subscribe to real-time updates
- `getMetrics`: Request current metrics
- `getIntegrations`: Request integration status
- `getEvents`: Request system events
- `refreshIntegration`: Refresh specific integration
- `acknowledgeEvent`: Acknowledge system event

**Server -> Client:**
- `initialData`: Complete initial data set
- `metricsUpdate`: Real-time metrics updates
- `integrationsUpdate`: Integration status updates
- `newEvent`: New system events
- `eventAcknowledged`: Event acknowledgment confirmations

### Frontend Components (Luminar-Dashboard)

#### 1. Monitoring Service (`src/services/monitoring.service.ts`)

**Features:**
- WebSocket connection management with auto-reconnection
- Real-time event callbacks
- API method wrappers with error handling
- Connection status monitoring
- Utility functions for data formatting

**Key Methods:**
```typescript
// Real-time subscriptions
subscribe(type: 'metrics' | 'events' | 'integrations' | 'docker' | 'database', enabled: boolean)

// API calls
getSystemMetrics(useCache?: boolean): Promise<SystemMetrics>
getDashboardSummary(): Promise<DashboardSummary>
getIntegrations(): Promise<IntegrationSummary>
refreshIntegration(id: string): Promise<IntegrationHealth>

// Event callbacks
setCallbacks(callbacks: MonitoringEventCallbacks)
```

#### 2. Updated Dashboard Components

**Main Dashboard** (`src/routes/index.tsx`)
- Real-time system metrics display
- Connection status indicator
- Resource usage charts with live updates
- Service status cards
- Recent events feed

**Integration Dashboard** (`src/routes/integration.tsx`)
- Live integration health monitoring
- Real-time status updates
- Manual refresh capabilities
- Health check details

## Implementation Features

### 1. Real System Metrics Collection

**CPU Monitoring:**
- Usage percentage calculation
- Core count detection
- Load average tracking
- Temperature monitoring (Linux)

**Memory Monitoring:**
- Total, used, and available memory
- Usage percentage
- Swap space monitoring
- Cross-platform compatibility

**Disk Monitoring:**
- Multiple partition support
- Usage statistics per partition
- Total system storage overview
- Free space monitoring

**Network Monitoring:**
- Interface-specific statistics
- Bytes transferred tracking
- Packet count monitoring
- Network interface status

### 2. Docker Integration

**Container Monitoring:**
- Container state tracking
- Resource usage per container
- Health status monitoring
- Port configuration tracking

**Docker System Stats:**
- Total containers, images, volumes, networks
- Real-time status updates
- Container lifecycle events

### 3. Database Monitoring

**Connection Tracking:**
- Active and idle connections
- Connection pool monitoring
- Query performance metrics

**Performance Metrics:**
- Slow query detection
- Cache hit ratios
- Database size tracking
- Uptime monitoring

### 4. Integration Health Monitoring

**Supported Integrations:**
- AMNA (AI platform)
- E-Connect (Email automation)
- Lighthouse (Knowledge management)
- Training Needs Analysis
- Vendors Management
- Wins of the Week
- Luminar Dashboard

**Health Check Features:**
- Connectivity verification
- Response time measurement
- Dependency checking
- Uptime calculation
- Automated refresh capabilities

### 5. Real-Time Event System

**Event Categories:**
- System events (resource alerts)
- Docker events (container status changes)
- Database events (connection issues)
- Integration events (health status changes)
- Service events (process status changes)

**Event Management:**
- Real-time event generation
- Event acknowledgment system
- Event filtering and categorization
- Historical event tracking

### 6. WebSocket Real-Time Updates

**Features:**
- Auto-reconnection on connection loss
- Selective subscription management
- Real-time data streaming
- Connection status monitoring
- Error handling and recovery

## Installation and Setup

### 1. Backend Dependencies

```bash
cd Command-Center
npm install @nestjs/websockets @nestjs/platform-socket.io socket.io --legacy-peer-deps
```

### 2. Frontend Dependencies

```bash
cd Luminar-Dashboard
npm install socket.io-client --legacy-peer-deps
```

### 3. Environment Configuration

**Dashboard** (`.env`):
```env
VITE_API_URL=http://localhost:3000
VITE_WS_URL=ws://localhost:3000
VITE_ENABLE_REALTIME=true
VITE_ENABLE_WEBSOCKETS=true
VITE_ENABLE_SYSTEM_MONITORING=true
VITE_ENABLE_INTEGRATION_MONITORING=true
```

### 4. Module Registration

The `SystemMonitoringModule` is registered in `Command-Center/src/app.module.ts`:

```typescript
import { SystemMonitoringModule } from './modules/system-monitoring/system-monitoring.module';

@Module({
  imports: [
    // ... other modules
    SystemMonitoringModule,
  ],
})
export class AppModule {}
```

## Usage

### 1. Starting the Services

```bash
# Backend
cd Command-Center
npm run start:dev

# Frontend
cd Luminar-Dashboard
npm run dev
```

### 2. Dashboard Access

- Main Dashboard: http://localhost:3007/
- Integration Monitoring: http://localhost:3007/integration
- API Documentation: http://localhost:3000/api (Swagger UI)

### 3. API Testing

```bash
# Test system metrics
curl http://localhost:3000/api/system-monitoring/metrics

# Test integration health
curl http://localhost:3000/api/system-monitoring/integrations

# Test dashboard summary
curl http://localhost:3000/api/system-monitoring/dashboard
```

## Monitoring Data Flow

1. **Data Collection**: `SystemMetricsService` collects real system data
2. **API Endpoints**: REST APIs provide on-demand data access
3. **WebSocket Streaming**: Real-time updates pushed to connected clients
4. **Frontend Display**: Dashboard components render live data
5. **Event System**: System events trigger real-time notifications

## Performance Considerations

- **Caching**: 5-second cache for system metrics to reduce overhead
- **Selective Updates**: Clients subscribe only to needed data streams
- **Efficient Queries**: Optimized system calls for metric collection
- **Connection Management**: Automatic cleanup of disconnected clients
- **Error Handling**: Graceful fallbacks when services are unavailable

## Extensibility

The architecture supports easy addition of:
- New metric types (just extend interfaces)
- Additional integrations (add to health service)
- Custom event types (extend event service)
- New dashboard components (use monitoring service)
- Additional alerting channels (extend event system)

## Security

- JWT authentication for API endpoints
- CORS configuration for WebSocket connections
- Input validation on all endpoints
- Rate limiting capabilities
- Secure WebSocket connections

## Platform Compatibility

**Supported Operating Systems:**
- Linux (full feature support)
- macOS (full feature support)
- Windows (basic support)

**Supported Containers:**
- Docker containers and images
- Docker Compose services
- Docker networks and volumes

**Supported Databases:**
- PostgreSQL
- MySQL
- MongoDB
- Redis

This implementation provides a production-ready, real-time monitoring solution that replaces all mock data with actual system metrics while maintaining excellent performance and user experience.