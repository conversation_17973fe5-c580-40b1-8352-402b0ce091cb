import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { randomBytes } from 'crypto';
import { AuthRequest } from './auth.middleware';

@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  constructor(private configService: ConfigService) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Add security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

    // Add request ID for tracing
    req.headers['x-request-id'] =
      req.headers['x-request-id'] || randomBytes(16).toString('hex');

    // Remove sensitive headers
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');

    next();
  }
}

@Injectable()
export class AuditMiddleware implements NestMiddleware {
  use(req: AuthRequest, res: Response, next: NextFunction) {
    const start = Date.now();
    const { method, url, ip, headers } = req;

    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        timestamp: new Date().toISOString(),
        method,
        url,
        statusCode: res.statusCode,
        duration,
        ip,
        userAgent: headers['user-agent'],
        requestId: headers['x-request-id'],
        userId: req.user?.id || 'anonymous',
      };

      // Log security-relevant events
      if (
        res.statusCode >= 400 ||
        url.includes('/auth/') ||
        url.includes('/admin/')
      ) {
        console.log(
          JSON.stringify({
            type: 'security_audit',
            ...logData,
          }),
        );
      }
    });

    next();
  }
}
