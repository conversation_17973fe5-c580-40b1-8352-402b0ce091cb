import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WsAuthGuard } from '../guards/ws-auth.guard';
import { RateLimitFactory } from '../middleware/rate-limit.middleware';
import { LAndDWebSocketService } from '../../modules/l-and-d/websocket/l-and-d-websocket.service';
import {
  LAndDEvent,
  LAndDApp,
} from '../../modules/l-and-d/websocket/websocket-event.types';

export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: any;
  rooms: Set<string>;
}

@WebSocketGateway({
  namespace: '/unified',
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
@UsePipes(new ValidationPipe({ transform: true }))
export class AppGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(AppGateway.name);
  private readonly connectedClients = new Map<string, AuthenticatedSocket>();
  private readonly userSockets = new Map<string, Set<string>>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly rateLimitFactory: RateLimitFactory,
    private readonly eventEmitter: EventEmitter2,
    private readonly lAndDWebSocketService: LAndDWebSocketService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');

    // Set server instance for L&D service
    this.lAndDWebSocketService.setServer(server);

    // Configure socket middleware
    server.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = this.extractToken(socket);
        if (token) {
          const payload = await this.verifyToken(token);
          socket.userId = payload.sub;
          socket.user = payload;
        }
        next();
      } catch (error) {
        next(new Error('Authentication failed'));
      }
    });
  }

  async handleConnection(client: AuthenticatedSocket) {
    this.logger.log(`Client connected: ${client.id}`);

    // Store client connection
    this.connectedClients.set(client.id, client);

    // Track user sockets
    if (client.userId) {
      if (!this.userSockets.has(client.userId)) {
        this.userSockets.set(client.userId, new Set());
      }
      this.userSockets.get(client.userId)!.add(client.id);

      // Join user-specific room
      client.join(`user:${client.userId}`);

      // Handle L&D connection
      await this.lAndDWebSocketService.handleUserConnection(client);

      // Notify user's other connections
      this.emitToUser(
        client.userId,
        'user:connected',
        {
          socketId: client.id,
          timestamp: new Date(),
        },
        client.id,
      );
    }

    // Send connection acknowledgment
    client.emit('connected', {
      socketId: client.id,
      userId: client.userId,
      timestamp: new Date(),
    });
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`Client disconnected: ${client.id}`);

    // Handle L&D disconnection first
    if (client.userId) {
      await this.lAndDWebSocketService.handleUserDisconnection(client);
    }

    // Remove from connected clients
    this.connectedClients.delete(client.id);

    // Remove from user sockets
    if (client.userId) {
      const userSockets = this.userSockets.get(client.userId);
      if (userSockets) {
        userSockets.delete(client.id);
        if (userSockets.size === 0) {
          this.userSockets.delete(client.userId);
        }
      }

      // Notify user's other connections
      this.emitToUser(
        client.userId,
        'user:disconnected',
        {
          socketId: client.id,
          timestamp: new Date(),
        },
        client.id,
      );
    }
  }

  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: AuthenticatedSocket): void {
    client.emit('pong', { timestamp: Date.now() });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('room:join')
  async handleJoinRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room: string },
  ): Promise<void> {
    const { room } = data;

    // Validate room access
    if (!this.canJoinRoom(client, room)) {
      throw new WsException('Access denied to room');
    }

    await client.join(room);

    // Track rooms
    if (!client.rooms) {
      client.rooms = new Set();
    }
    client.rooms.add(room);

    // Notify room members
    this.server.to(room).emit('room:user_joined', {
      userId: client.userId,
      room,
      timestamp: new Date(),
    });

    // Send confirmation
    client.emit('room:joined', { room, timestamp: new Date() });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('room:leave')
  async handleLeaveRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room: string },
  ): Promise<void> {
    const { room } = data;

    await client.leave(room);

    if (client.rooms) {
      client.rooms.delete(room);
    }

    // Notify room members
    this.server.to(room).emit('room:user_left', {
      userId: client.userId,
      room,
      timestamp: new Date(),
    });

    // Send confirmation
    client.emit('room:left', { room, timestamp: new Date() });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('message:send')
  async handleMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room?: string; message: string; type?: string },
  ): Promise<void> {
    const { room, message, type = 'text' } = data;

    // Apply rate limiting
    // In production, integrate with Redis rate limiter

    const messageData = {
      id: this.generateMessageId(),
      userId: client.userId,
      message,
      type,
      timestamp: new Date(),
    };

    if (room) {
      // Send to room
      this.server.to(room).emit('message:received', {
        ...messageData,
        room,
      });
    } else {
      // Broadcast to all authenticated users
      this.server.emit('message:broadcast', messageData);
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('typing:start')
  handleTypingStart(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room?: string; conversationId?: string },
  ): void {
    const target = data.room || `conversation:${data.conversationId}`;

    this.server.to(target).emit('typing:user_started', {
      userId: client.userId,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('typing:stop')
  handleTypingStop(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { room?: string; conversationId?: string },
  ): void {
    const target = data.room || `conversation:${data.conversationId}`;

    this.server.to(target).emit('typing:user_stopped', {
      userId: client.userId,
      timestamp: new Date(),
    });
  }

  // Utility methods
  private extractToken(socket: Socket): string | null {
    // Check auth header
    const auth =
      socket.handshake.auth?.token || socket.handshake.headers?.authorization;
    if (auth && auth.startsWith('Bearer ')) {
      return auth.substring(7);
    }

    // Check query params
    const token = socket.handshake.query?.token;
    if (token) {
      return Array.isArray(token) ? token[0] : token;
    }

    return null;
  }

  private async verifyToken(token: string): Promise<any> {
    try {
      return await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('jwt.secret'),
      });
    } catch (error) {
      throw new WsException('Invalid token');
    }
  }

  private canJoinRoom(client: AuthenticatedSocket, room: string): boolean {
    // Implement room access control logic
    // For now, allow authenticated users to join any room
    return !!client.userId;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public methods for server-side events
  emitToAll(event: string, data: any): void {
    this.server.emit(event, data);
  }

  emitToRoom(room: string, event: string, data: any): void {
    this.server.to(room).emit(event, data);
  }

  emitToUser(
    userId: string,
    event: string,
    data: any,
    excludeSocketId?: string,
  ): void {
    const sockets = this.userSockets.get(userId);
    if (sockets) {
      sockets.forEach((socketId) => {
        if (socketId !== excludeSocketId) {
          this.server.to(socketId).emit(event, data);
        }
      });
    }
  }

  emitToSocket(socketId: string, event: string, data: any): void {
    this.server.to(socketId).emit(event, data);
  }

  getConnectedUsers(): string[] {
    return Array.from(this.userSockets.keys());
  }

  getUserSockets(userId: string): string[] {
    return Array.from(this.userSockets.get(userId) || []);
  }

  isUserConnected(userId: string): boolean {
    return this.userSockets.has(userId);
  }

  getConnectionCount(): number {
    return this.connectedClients.size;
  }

  // ========================================
  // L&D SPECIFIC EVENT HANDLERS
  // ========================================

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:join_apps')
  async handleJoinLAndDApps(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { apps: LAndDApp[] },
  ): Promise<void> {
    const { apps } = data;

    if (!apps || !Array.isArray(apps)) {
      throw new WsException('Invalid apps data');
    }

    // Validate apps
    const validApps: LAndDApp[] = ['training', 'vendors', 'wins'];
    const filteredApps = apps.filter((app) => validApps.includes(app));

    if (filteredApps.length === 0) {
      throw new WsException('No valid apps specified');
    }

    await this.lAndDWebSocketService.joinUserToAppRooms(client, filteredApps);

    client.emit('l&d:apps_joined', {
      apps: filteredApps,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:event')
  async handleLAndDEvent(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: LAndDEvent,
  ): Promise<void> {
    // Validate event data
    if (!data.type || !data.action || !data.payload) {
      throw new WsException('Invalid L&D event data');
    }

    // Add client info to event
    const event: LAndDEvent = {
      ...data,
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: client.userId!,
      timestamp: new Date(),
    };

    // Handle cross-app propagation if specified
    if (data.targetApps && data.targetApps.length > 0) {
      await this.lAndDWebSocketService.handleCrossAppEvent(
        data.type,
        data.targetApps,
        event,
      );
    } else {
      // Broadcast to current app only
      await this.lAndDWebSocketService.broadcastToApp(
        data.type,
        'l&d:event',
        event,
      );
    }

    // Emit event to EventEmitter for server-side handling
    this.eventEmitter.emit(`l&d.${data.type}.${data.action}`, event);

    client.emit('l&d:event_processed', {
      eventId: event.id,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:broadcast_to_app')
  async handleBroadcastToApp(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { app: LAndDApp; event: string; payload: any },
  ): Promise<void> {
    const { app, event, payload } = data;

    if (!app || !event || !payload) {
      throw new WsException('Invalid broadcast data');
    }

    await this.lAndDWebSocketService.broadcastToApp(app, event, {
      ...payload,
      sourceUserId: client.userId,
      sourceEvent: 'l&d:broadcast_to_app',
    });

    client.emit('l&d:broadcast_sent', {
      app,
      event,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:update_presence')
  async handleUpdatePresence(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: { status: 'online' | 'away' | 'busy' | 'offline'; app?: LAndDApp },
  ): Promise<void> {
    const { status, app } = data;

    if (!status) {
      throw new WsException('Status is required');
    }

    await this.lAndDWebSocketService.updateUserPresence(
      client.userId!,
      status,
      app,
    );

    client.emit('l&d:presence_updated', {
      status,
      app,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:get_online_users')
  async handleGetOnlineUsers(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { app?: LAndDApp },
  ): Promise<void> {
    const { app } = data || {};

    const onlineUsers = await this.lAndDWebSocketService.getOnlineUsers(app);

    client.emit('l&d:online_users', {
      app,
      users: onlineUsers,
      count: onlineUsers.length,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:get_presence')
  async handleGetPresence(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { userId?: string },
  ): Promise<void> {
    const targetUserId = data?.userId || client.userId!;

    const presence =
      await this.lAndDWebSocketService.getUserPresence(targetUserId);

    client.emit('l&d:presence_info', {
      userId: targetUserId,
      presence,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:get_activity')
  async handleGetActivity(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { userId?: string; limit?: number },
  ): Promise<void> {
    const targetUserId = data?.userId || client.userId!;
    const limit = data?.limit || 20;

    const activities = await this.lAndDWebSocketService.getRecentActivity(
      targetUserId,
      limit,
    );

    client.emit('l&d:activity_history', {
      userId: targetUserId,
      activities,
      count: activities.length,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:track_activity')
  async handleTrackActivity(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: { app: LAndDApp; action: string; metadata?: Record<string, any> },
  ): Promise<void> {
    const { app, action, metadata } = data;

    if (!app || !action) {
      throw new WsException('App and action are required');
    }

    await this.lAndDWebSocketService.trackUserActivity(
      client.userId!,
      app,
      action,
      metadata,
    );

    client.emit('l&d:activity_tracked', {
      app,
      action,
      timestamp: new Date(),
    });
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('l&d:get_stats')
  async handleGetStats(
    @ConnectedSocket() client: AuthenticatedSocket,
  ): Promise<void> {
    const stats = this.lAndDWebSocketService.getStats();

    client.emit('l&d:stats', {
      stats,
      timestamp: new Date(),
    });
  }

  // ========================================
  // SYNC EVENT HANDLERS
  // ========================================

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('sync:entity_update')
  async handleSyncEntityUpdate(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: {
      entityType: string;
      entityId: string;
      changes: Record<string, any>;
      app: LAndDApp;
      operation?: 'create' | 'update' | 'delete';
      metadata?: Record<string, any>;
    },
  ): Promise<void> {
    const {
      entityType,
      entityId,
      changes,
      app,
      operation = 'update',
      metadata,
    } = data;

    if (!entityType || !entityId || !changes || !app) {
      throw new WsException('Missing required sync data');
    }

    try {
      await this.lAndDWebSocketService.syncEntityData(
        client.userId!,
        entityType,
        entityId,
        changes,
        app,
        operation,
        metadata,
      );

      client.emit('sync:entity_updated', {
        entityType,
        entityId,
        operation,
        timestamp: new Date(),
      });
    } catch (error) {
      client.emit('sync:error', {
        action: 'entity_update',
        error: error.message,
        entityType,
        entityId,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('sync:subscribe')
  async handleSyncSubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: {
      app: LAndDApp;
      entityType: string;
      entityId?: string;
    },
  ): Promise<void> {
    const { app, entityType, entityId } = data;

    if (!app || !entityType) {
      throw new WsException('App and entityType are required');
    }

    try {
      const subscriptionId = await this.lAndDWebSocketService.subscribeToSync(
        client.userId!,
        app,
        entityType,
        entityId,
      );

      client.emit('sync:subscribed', {
        subscriptionId,
        app,
        entityType,
        entityId,
        timestamp: new Date(),
      });
    } catch (error) {
      client.emit('sync:error', {
        action: 'subscribe',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('sync:unsubscribe')
  async handleSyncUnsubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { subscriptionId: string },
  ): Promise<void> {
    const { subscriptionId } = data;

    if (!subscriptionId) {
      throw new WsException('Subscription ID is required');
    }

    try {
      await this.lAndDWebSocketService.unsubscribeFromSync(subscriptionId);

      client.emit('sync:unsubscribed', {
        subscriptionId,
        timestamp: new Date(),
      });
    } catch (error) {
      client.emit('sync:error', {
        action: 'unsubscribe',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('sync:lock_entity')
  async handleLockEntity(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: {
      entityType: string;
      entityId: string;
      app: LAndDApp;
      lockType?: 'read' | 'write' | 'exclusive';
      reason?: string;
    },
  ): Promise<void> {
    const { entityType, entityId, app, lockType = 'write', reason } = data;

    if (!entityType || !entityId || !app) {
      throw new WsException('EntityType, entityId, and app are required');
    }

    try {
      const lockId = await this.lAndDWebSocketService.lockEntity(
        client.userId!,
        entityType,
        entityId,
        app,
        lockType,
        reason,
      );

      client.emit('sync:entity_locked', {
        lockId,
        entityType,
        entityId,
        lockType,
        timestamp: new Date(),
      });
    } catch (error) {
      client.emit('sync:error', {
        action: 'lock_entity',
        error: error.message,
        entityType,
        entityId,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('sync:unlock_entity')
  async handleUnlockEntity(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: {
      entityType: string;
      entityId: string;
    },
  ): Promise<void> {
    const { entityType, entityId } = data;

    if (!entityType || !entityId) {
      throw new WsException('EntityType and entityId are required');
    }

    try {
      await this.lAndDWebSocketService.unlockEntity(
        client.userId!,
        entityType,
        entityId,
      );

      client.emit('sync:entity_unlocked', {
        entityType,
        entityId,
        timestamp: new Date(),
      });
    } catch (error) {
      client.emit('sync:error', {
        action: 'unlock_entity',
        error: error.message,
        entityType,
        entityId,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('sync:get_status')
  async handleGetSyncStatus(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: {
      entityType: string;
      entityId: string;
    },
  ): Promise<void> {
    const { entityType, entityId } = data;

    if (!entityType || !entityId) {
      throw new WsException('EntityType and entityId are required');
    }

    try {
      const status = this.lAndDWebSocketService.getSyncStatus(
        entityType,
        entityId,
      );

      client.emit('sync:status', {
        entityType,
        entityId,
        status,
        timestamp: new Date(),
      });
    } catch (error) {
      client.emit('sync:error', {
        action: 'get_status',
        error: error.message,
        entityType,
        entityId,
        timestamp: new Date(),
      });
    }
  }
}
