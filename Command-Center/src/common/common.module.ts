import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AppGateway } from './gateways/app.gateway';
import { WsAuthGuard } from './guards/ws-auth.guard';
import { RateLimitFactory } from './middleware/rate-limit.middleware';
import { LAndDModule } from '../modules/l-and-d/l-and-d.module';

/**
 * Common Module
 * Handles shared functionality including WebSocket gateway
 */
@Module({
  imports: [
    ConfigModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret'),
        signOptions: { expiresIn: '1h' },
      }),
      inject: [ConfigService],
    }),
    EventEmitterModule.forRoot(),
    LAndDModule,
  ],
  providers: [AppGateway, WsAuthGuard, RateLimitFactory],
  exports: [AppGateway, WsAuthGuard, RateLimitFactory],
})
export class CommonModule {}
