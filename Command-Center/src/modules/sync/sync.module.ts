import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { DataSyncService } from './data-sync.service';
import { SyncConflictResolutionService } from './sync-conflict-resolution.service';

/**
 * Sync Module
 * Handles real-time data synchronization across L&D applications
 */
@Module({
  imports: [EventEmitterModule.forRoot()],
  providers: [DataSyncService, SyncConflictResolutionService],
  exports: [DataSyncService, SyncConflictResolutionService],
})
export class SyncModule {}
