import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  DataSyncEvent,
  BatchSyncEvent,
  SyncStatus,
  SyncStatusType,
  DataOperation,
  SyncQueueItem,
  SyncSubscription,
  SyncPropagationRule,
  SyncNotification,
  EntityLock,
  SyncHistoryEntry,
} from './sync-event.types';
import { SyncConflictResolutionService } from './sync-conflict-resolution.service';
import { LAndDApp } from '../l-and-d/websocket/websocket-event.types';

/**
 * Data Sync Service
 * Core service for managing real-time data synchronization across L&D applications
 */
@Injectable()
export class DataSyncService {
  private readonly logger = new Logger(DataSyncService.name);

  // Entity sync status: entityType:entityId -> SyncStatus
  private readonly syncStatus = new Map<string, SyncStatus>();

  // Sync subscriptions: subscriptionId -> SyncSubscription
  private readonly subscriptions = new Map<string, SyncSubscription>();

  // User subscriptions: userId -> Set<subscriptionId>
  private readonly userSubscriptions = new Map<string, Set<string>>();

  // Propagation rules: ruleId -> SyncPropagationRule
  private readonly propagationRules = new Map<string, SyncPropagationRule>();

  // Entity locks: entityType:entityId -> EntityLock
  private readonly entityLocks = new Map<string, EntityLock>();

  // Sync queue for batching operations
  private readonly syncQueue: SyncQueueItem[] = [];

  // Processing state
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;

  // WebSocket service reference
  private webSocketService: any;

  constructor(
    private readonly conflictResolver: SyncConflictResolutionService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeDefaultRules();
    this.startSyncProcessor();
  }

  /**
   * Set WebSocket service reference
   */
  setWebSocketService(webSocketService: any): void {
    this.webSocketService = webSocketService;
  }

  /**
   * Sync entity update across applications
   */
  async syncEntityUpdate(
    entityType: string,
    entityId: string,
    changes: Record<string, any>,
    sourceApp: LAndDApp,
    sourceUserId: string,
    operation: DataOperation = 'update',
    metadata?: Record<string, any>,
  ): Promise<void> {
    const entityKey = this.getEntityKey(entityType, entityId);

    // Check if entity is locked
    const lock = this.entityLocks.get(entityKey);
    if (lock && lock.lockedBy !== sourceUserId) {
      throw new Error(`Entity ${entityKey} is locked by user ${lock.lockedBy}`);
    }

    // Get current sync status
    let status = this.syncStatus.get(entityKey);
    if (!status) {
      status = this.createInitialSyncStatus(entityType, entityId);
      this.syncStatus.set(entityKey, status);
    }

    // Create sync event
    const syncEvent: DataSyncEvent = {
      id: this.generateEventId(),
      entityType,
      entityId,
      operation,
      changes,
      previousState:
        status.version > 0 ? this.getPreviousState(entityKey) : undefined,
      sourceApp,
      sourceUserId,
      timestamp: new Date(),
      version: status.version + 1,
      checksum: this.generateChecksum(changes),
      metadata,
    };

    // Update sync status
    status.version = syncEvent.version;
    status.lastSyncAt = syncEvent.timestamp;
    status.status = 'pending';
    status.checksum = syncEvent.checksum;

    // Add to sync history
    const historyEntry: SyncHistoryEntry = {
      id: this.generateHistoryId(),
      eventId: syncEvent.id,
      operation,
      timestamp: syncEvent.timestamp,
      success: false, // Will be updated after processing
      processingTime: 0,
      propagatedTo: [],
    };
    status.syncHistory.unshift(historyEntry);

    // Keep only last 50 history entries
    if (status.syncHistory.length > 50) {
      status.syncHistory.splice(50);
    }

    this.logger.log(
      `Created sync event ${syncEvent.id} for ${entityKey} from ${sourceApp}`,
    );

    // Process sync event
    await this.processSyncEvent(syncEvent);
  }

  /**
   * Process individual sync event
   */
  private async processSyncEvent(event: DataSyncEvent): Promise<void> {
    const entityKey = this.getEntityKey(event.entityType, event.entityId);
    const status = this.syncStatus.get(entityKey)!;
    const historyEntry = status.syncHistory[0]!;
    const startTime = Date.now();

    try {
      // Find applicable propagation rules
      const rules = this.findApplicableRules(event);

      if (rules.length === 0) {
        this.logger.debug(
          `No propagation rules found for ${event.entityType} from ${event.sourceApp}`,
        );
        status.status = 'synced';
        historyEntry.success = true;
        historyEntry.processingTime = Date.now() - startTime;
        return;
      }

      // Queue sync for target applications
      const targetApps = new Set<LAndDApp>();
      rules.forEach((rule) => {
        rule.targetApps.forEach((app) => {
          if (app !== event.sourceApp) {
            targetApps.add(app);
          }
        });
      });

      // Add to sync queue for each target app
      for (const targetApp of targetApps) {
        const queueItem: SyncQueueItem = {
          id: this.generateQueueId(),
          event,
          targetApps: [targetApp],
          priority: this.calculateEventPriority(event),
          attempts: 0,
          maxAttempts: 3,
          scheduledFor: new Date(),
          status: 'queued',
        };

        this.syncQueue.push(queueItem);
      }

      // Sort queue by priority
      this.syncQueue.sort((a, b) => {
        const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

      status.status = 'pending';
      status.pendingEvents = [event.id];

      historyEntry.success = true;
      historyEntry.processingTime = Date.now() - startTime;
      historyEntry.propagatedTo = Array.from(targetApps);

      this.logger.log(
        `Queued sync event ${event.id} for propagation to ${targetApps.size} apps`,
      );

      // Emit sync event
      this.eventEmitter.emit('sync.event.created', event);

      // Send real-time notifications to subscribers
      await this.notifySubscribers(event);
    } catch (error) {
      status.status = 'error';
      status.errorMessage = error.message;

      historyEntry.success = false;
      historyEntry.errorMessage = error.message;
      historyEntry.processingTime = Date.now() - startTime;

      this.logger.error(`Failed to process sync event ${event.id}:`, error);

      // Emit error event
      this.eventEmitter.emit('sync.event.failed', {
        event,
        error: error.message,
      });
    }
  }

  /**
   * Propagate sync event to target applications
   */
  async propagateChange(
    event: DataSyncEvent,
    targetApps: LAndDApp[],
  ): Promise<void> {
    const entityKey = this.getEntityKey(event.entityType, event.entityId);

    for (const targetApp of targetApps) {
      try {
        // Check for conflicts with existing data in target app
        const existingEvents = await this.getRecentEventsForEntity(
          event.entityType,
          event.entityId,
          targetApp,
        );

        for (const existingEvent of existingEvents) {
          const conflict = await this.conflictResolver.detectConflict(
            event,
            existingEvent,
          );
          if (conflict) {
            const status = this.syncStatus.get(entityKey)!;
            status.status = 'conflict';
            status.conflicts.push(conflict.id);

            this.logger.warn(
              `Conflict detected during propagation: ${conflict.id}`,
            );

            // Send conflict notification
            await this.sendSyncNotification({
              id: this.generateNotificationId(),
              type: 'conflict_detected',
              entityType: event.entityType,
              entityId: event.entityId,
              targetUsers: [event.sourceUserId, existingEvent.sourceUserId],
              message: `Data conflict detected for ${event.entityType} ${event.entityId}`,
              data: { conflict },
              timestamp: new Date(),
            });

            return; // Stop propagation due to conflict
          }
        }

        // Apply transformations if needed
        const transformedEvent = await this.applyTransformations(
          event,
          targetApp,
        );

        // Simulate propagation to target app (in production, this would call actual app APIs)
        await this.propagateToApp(transformedEvent, targetApp);

        this.logger.debug(`Propagated event ${event.id} to ${targetApp}`);
      } catch (error) {
        this.logger.error(
          `Failed to propagate event ${event.id} to ${targetApp}:`,
          error,
        );

        // Send error notification
        await this.sendSyncNotification({
          id: this.generateNotificationId(),
          type: 'sync_error',
          entityType: event.entityType,
          entityId: event.entityId,
          targetUsers: [event.sourceUserId],
          message: `Failed to sync ${event.entityType} ${event.entityId} to ${targetApp}`,
          data: { error: error.message, targetApp },
          timestamp: new Date(),
        });
      }
    }

    // Update sync status after successful propagation
    const status = this.syncStatus.get(entityKey);
    if (status && status.conflicts.length === 0) {
      status.status = 'synced';
      status.pendingEvents = [];
    }
  }

  /**
   * Subscribe to sync events for entity type
   */
  async subscribeToSync(
    userId: string,
    app: LAndDApp,
    entityType: string,
    entityId?: string,
    filters?: {
      fields?: string[];
      operations?: DataOperation[];
      sourceApps?: LAndDApp[];
    },
  ): Promise<string> {
    const subscription: SyncSubscription = {
      id: this.generateSubscriptionId(),
      userId,
      app,
      entityType,
      entityId,
      filters,
      isActive: true,
      createdAt: new Date(),
      lastActivity: new Date(),
    };

    this.subscriptions.set(subscription.id, subscription);

    // Track user subscriptions
    if (!this.userSubscriptions.has(userId)) {
      this.userSubscriptions.set(userId, new Set());
    }
    this.userSubscriptions.get(userId)!.add(subscription.id);

    this.logger.log(`User ${userId} subscribed to ${entityType} sync events`);

    return subscription.id;
  }

  /**
   * Unsubscribe from sync events
   */
  async unsubscribeFromSync(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    subscription.isActive = false;
    this.subscriptions.delete(subscriptionId);

    // Remove from user subscriptions
    const userSubs = this.userSubscriptions.get(subscription.userId);
    if (userSubs) {
      userSubs.delete(subscriptionId);
      if (userSubs.size === 0) {
        this.userSubscriptions.delete(subscription.userId);
      }
    }

    this.logger.log(`Unsubscribed ${subscriptionId} from sync events`);
  }

  /**
   * Get sync status for entity
   */
  getSyncStatus(entityType: string, entityId: string): SyncStatus | undefined {
    const entityKey = this.getEntityKey(entityType, entityId);
    return this.syncStatus.get(entityKey);
  }

  /**
   * Lock entity for editing
   */
  async lockEntity(
    entityType: string,
    entityId: string,
    userId: string,
    app: LAndDApp,
    lockType: 'read' | 'write' | 'exclusive' = 'write',
    duration: number = 300000, // 5 minutes default
    reason?: string,
  ): Promise<string> {
    const entityKey = this.getEntityKey(entityType, entityId);

    // Check for existing lock
    const existingLock = this.entityLocks.get(entityKey);
    if (existingLock && existingLock.expiresAt > new Date()) {
      if (existingLock.lockedBy !== userId) {
        throw new Error(
          `Entity is locked by user ${existingLock.lockedBy} until ${existingLock.expiresAt}`,
        );
      }
      // Extend existing lock
      existingLock.expiresAt = new Date(Date.now() + duration);
      return existingLock.id;
    }

    const lock: EntityLock = {
      id: this.generateLockId(),
      entityType,
      entityId,
      lockedBy: userId,
      app,
      lockType,
      acquiredAt: new Date(),
      expiresAt: new Date(Date.now() + duration),
      reason,
      metadata: {
        userAgent: 'WebSocket',
        sessionId: `${app}_${userId}`,
      },
    };

    this.entityLocks.set(entityKey, lock);

    this.logger.log(
      `Entity ${entityKey} locked by user ${userId} until ${lock.expiresAt}`,
    );

    // Send lock notification to other subscribers
    await this.notifyLockChange(lock, 'locked');

    return lock.id;
  }

  /**
   * Unlock entity
   */
  async unlockEntity(
    entityType: string,
    entityId: string,
    userId: string,
  ): Promise<void> {
    const entityKey = this.getEntityKey(entityType, entityId);
    const lock = this.entityLocks.get(entityKey);

    if (!lock) {
      this.logger.warn(`No lock found for entity ${entityKey}`);
      return;
    }

    if (lock.lockedBy !== userId) {
      throw new Error(
        `Entity can only be unlocked by the user who locked it: ${lock.lockedBy}`,
      );
    }

    this.entityLocks.delete(entityKey);

    this.logger.log(`Entity ${entityKey} unlocked by user ${userId}`);

    // Send unlock notification
    await this.notifyLockChange(lock, 'unlocked');
  }

  /**
   * Get service statistics
   */
  getStats() {
    const totalEntities = this.syncStatus.size;
    const conflictCount = Array.from(this.syncStatus.values()).filter(
      (s) => s.status === 'conflict',
    ).length;
    const pendingCount = Array.from(this.syncStatus.values()).filter(
      (s) => s.status === 'pending',
    ).length;
    const lockedCount = this.entityLocks.size;

    return {
      entities: {
        total: totalEntities,
        synced: Array.from(this.syncStatus.values()).filter(
          (s) => s.status === 'synced',
        ).length,
        pending: pendingCount,
        conflicts: conflictCount,
        errors: Array.from(this.syncStatus.values()).filter(
          (s) => s.status === 'error',
        ).length,
      },
      queue: {
        size: this.syncQueue.length,
        processing: this.isProcessing,
      },
      subscriptions: {
        total: this.subscriptions.size,
        active: Array.from(this.subscriptions.values()).filter(
          (s) => s.isActive,
        ).length,
        users: this.userSubscriptions.size,
      },
      locks: {
        total: lockedCount,
        expired: Array.from(this.entityLocks.values()).filter(
          (l) => l.expiresAt <= new Date(),
        ).length,
      },
      conflicts: this.conflictResolver.getConflictStats(),
    };
  }

  /**
   * Start sync queue processor
   */
  private startSyncProcessor(): void {
    this.processingInterval = setInterval(async () => {
      if (this.isProcessing || this.syncQueue.length === 0) return;

      this.isProcessing = true;

      try {
        const item = this.syncQueue.shift();
        if (item && item.status === 'queued') {
          await this.processSyncQueueItem(item);
        }
      } catch (error) {
        this.logger.error('Error processing sync queue item:', error);
      } finally {
        this.isProcessing = false;
      }
    }, 100); // Process every 100ms

    // Cleanup expired locks every minute
    setInterval(() => this.cleanupExpiredLocks(), 60000);
  }

  /**
   * Process sync queue item
   */
  private async processSyncQueueItem(item: SyncQueueItem): Promise<void> {
    item.status = 'processing';
    item.processingStarted = new Date();

    try {
      await this.propagateChange(item.event, item.targetApps);

      item.status = 'completed';
      item.completedAt = new Date();

      this.logger.debug(`Completed sync queue item ${item.id}`);
    } catch (error) {
      item.attempts++;
      item.errorMessage = error.message;

      if (item.attempts >= item.maxAttempts) {
        item.status = 'failed';
        this.logger.error(
          `Sync queue item ${item.id} failed after ${item.attempts} attempts`,
        );
      } else {
        item.status = 'queued';
        item.scheduledFor = new Date(
          Date.now() + 1000 * Math.pow(2, item.attempts),
        ); // Exponential backoff
        this.syncQueue.push(item); // Re-queue for retry
        this.logger.warn(
          `Retrying sync queue item ${item.id}, attempt ${item.attempts}/${item.maxAttempts}`,
        );
      }
    }
  }

  /**
   * Notify subscribers of sync events
   */
  private async notifySubscribers(event: DataSyncEvent): Promise<void> {
    const relevantSubscriptions = Array.from(
      this.subscriptions.values(),
    ).filter((sub) => this.isSubscriptionRelevant(sub, event));

    for (const subscription of relevantSubscriptions) {
      try {
        if (
          this.webSocketService &&
          this.webSocketService.isUserConnected(subscription.userId)
        ) {
          await this.webSocketService.sendToUser(
            subscription.userId,
            'sync:event',
            {
              subscriptionId: subscription.id,
              event,
              timestamp: new Date(),
            },
          );

          subscription.lastActivity = new Date();
        }
      } catch (error) {
        this.logger.error(
          `Failed to notify subscriber ${subscription.userId}:`,
          error,
        );
      }
    }
  }

  /**
   * Check if subscription is relevant to event
   */
  private isSubscriptionRelevant(
    subscription: SyncSubscription,
    event: DataSyncEvent,
  ): boolean {
    if (!subscription.isActive) return false;
    if (subscription.entityType !== event.entityType) return false;
    if (subscription.entityId && subscription.entityId !== event.entityId)
      return false;

    if (subscription.filters) {
      if (
        subscription.filters.operations &&
        !subscription.filters.operations.includes(event.operation)
      ) {
        return false;
      }
      if (
        subscription.filters.sourceApps &&
        !subscription.filters.sourceApps.includes(event.sourceApp)
      ) {
        return false;
      }
    }

    return true;
  }

  /**
   * Find applicable propagation rules for event
   */
  private findApplicableRules(event: DataSyncEvent): SyncPropagationRule[] {
    return Array.from(this.propagationRules.values())
      .filter((rule) => {
        if (!rule.isActive) return false;
        if (rule.sourceApp !== event.sourceApp) return false;
        if (rule.entityType !== event.entityType) return false;

        // Check conditions
        if (
          rule.conditions.operations &&
          !rule.conditions.operations.includes(event.operation)
        ) {
          return false;
        }

        return true;
      })
      .sort((a, b) => b.priority - a.priority); // Higher priority first
  }

  /**
   * Calculate event priority for queue processing
   */
  private calculateEventPriority(
    event: DataSyncEvent,
  ): 'low' | 'medium' | 'high' | 'urgent' {
    // Critical operations get urgent priority
    if (event.operation === 'delete') return 'urgent';

    // Real-time operations get high priority
    if (event.metadata?.realtime) return 'high';

    // Batch operations get lower priority
    if (event.metadata?.batchId) return 'low';

    return 'medium';
  }

  /**
   * Send sync notification
   */
  private async sendSyncNotification(
    notification: SyncNotification,
  ): Promise<void> {
    this.eventEmitter.emit('sync.notification', notification);

    // Send to affected users via WebSocket
    for (const userId of notification.targetUsers) {
      if (
        this.webSocketService &&
        this.webSocketService.isUserConnected(userId)
      ) {
        await this.webSocketService.sendToUser(
          userId,
          'sync:notification',
          notification,
        );
      }
    }
  }

  /**
   * Notify about lock changes
   */
  private async notifyLockChange(
    lock: EntityLock,
    action: 'locked' | 'unlocked',
  ): Promise<void> {
    const entityKey = this.getEntityKey(lock.entityType, lock.entityId);

    // Find subscribers for this entity
    const subscribers = Array.from(this.subscriptions.values()).filter(
      (sub) =>
        sub.isActive &&
        sub.entityType === lock.entityType &&
        (!sub.entityId || sub.entityId === lock.entityId),
    );

    for (const subscription of subscribers) {
      if (
        this.webSocketService &&
        this.webSocketService.isUserConnected(subscription.userId)
      ) {
        await this.webSocketService.sendToUser(
          subscription.userId,
          'sync:lock_changed',
          {
            action,
            lock,
            entityKey,
            timestamp: new Date(),
          },
        );
      }
    }
  }

  /**
   * Helper methods
   */
  private getEntityKey(entityType: string, entityId: string): string {
    return `${entityType}:${entityId}`;
  }

  private createInitialSyncStatus(
    entityType: string,
    entityId: string,
  ): SyncStatus {
    return {
      entityType,
      entityId,
      status: 'synced',
      lastSyncAt: new Date(),
      version: 0,
      checksum: '',
      conflicts: [],
      pendingEvents: [],
      syncHistory: [],
    };
  }

  private generateEventId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateQueueId(): string {
    return `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateLockId(): string {
    return `lock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateHistoryId(): string {
    return `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateChecksum(data: Record<string, any>): string {
    return `chk_${Date.now()}_${JSON.stringify(data).length}`;
  }

  private getPreviousState(entityKey: string): Record<string, any> {
    // In production, this would fetch from database
    return {};
  }

  private async getRecentEventsForEntity(
    entityType: string,
    entityId: string,
    app: LAndDApp,
  ): Promise<DataSyncEvent[]> {
    // In production, this would query event store
    return [];
  }

  private async applyTransformations(
    event: DataSyncEvent,
    targetApp: LAndDApp,
  ): Promise<DataSyncEvent> {
    // Apply any necessary transformations for target app
    return { ...event };
  }

  private async propagateToApp(
    event: DataSyncEvent,
    targetApp: LAndDApp,
  ): Promise<void> {
    // In production, this would call target app's API
    this.logger.debug(`Propagated event ${event.id} to ${targetApp}`);
  }

  private cleanupExpiredLocks(): void {
    const now = new Date();
    let cleanedCount = 0;

    this.entityLocks.forEach((lock, entityKey) => {
      if (lock.expiresAt <= now) {
        this.entityLocks.delete(entityKey);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} expired locks`);
    }
  }

  private initializeDefaultRules(): void {
    // Add default propagation rules for L&D entities
    const rules: SyncPropagationRule[] = [
      {
        id: 'training_to_all',
        sourceApp: 'training',
        targetApps: ['vendors', 'wins'],
        entityType: 'course',
        conditions: {
          operations: ['create', 'update'],
        },
        isActive: true,
        priority: 1,
        createdAt: new Date(),
      },
      {
        id: 'vendor_to_training',
        sourceApp: 'vendors',
        targetApps: ['training'],
        entityType: 'vendor',
        conditions: {
          operations: ['create', 'update'],
        },
        isActive: true,
        priority: 1,
        createdAt: new Date(),
      },
    ];

    rules.forEach((rule) => {
      this.propagationRules.set(rule.id, rule);
    });

    this.logger.log('Initialized default sync propagation rules');
  }

  /**
   * Cleanup on module destroy
   */
  onModuleDestroy(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
  }
}
