import { LAndDApp } from '../l-and-d/websocket/websocket-event.types';

/**
 * Data Synchronization Types for L&D Platform
 * Defines all data sync events, conflicts, and resolution mechanisms
 */

// Data change operation types
export type DataOperation = 'create' | 'update' | 'delete' | 'batch_update';

// Sync event interface
export interface DataSyncEvent {
  id: string;
  entityType: string;
  entityId: string;
  operation: DataOperation;
  changes: Record<string, any>;
  previousState?: Record<string, any>;
  sourceApp: LAndDApp;
  sourceUserId: string;
  timestamp: Date;
  version: number;
  checksum: string; // For data integrity
  metadata?: {
    reason?: string;
    batchId?: string;
    parentEventId?: string;
    affectedFields?: string[];
    [key: string]: any;
  };
}

// Batch sync event for multiple operations
export interface BatchSyncEvent {
  id: string;
  batchId: string;
  events: DataSyncEvent[];
  sourceApp: LAndDApp;
  sourceUserId: string;
  timestamp: Date;
  totalEvents: number;
  metadata?: Record<string, any>;
}

// Sync conflict types
export type ConflictType =
  | 'concurrent_update' // Two users updating same entity simultaneously
  | 'version_mismatch' // Version numbers don't match
  | 'data_inconsistency' // Data validation failures
  | 'schema_mismatch' // Entity structure mismatch
  | 'permission_conflict' // User permissions conflict
  | 'business_rule_violation'; // Business logic violation

// Sync conflict interface
export interface SyncConflict {
  id: string;
  entityType: string;
  entityId: string;
  conflictType: ConflictType;
  localEvent: DataSyncEvent;
  remoteEvent: DataSyncEvent;
  localChanges: Record<string, any>;
  remoteChanges: Record<string, any>;
  conflictingFields: string[];
  timestamp: Date;
  status: 'pending' | 'resolved' | 'failed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  metadata?: {
    autoResolvable?: boolean;
    resolutionStrategy?: ConflictResolutionStrategy;
    affectedUsers?: string[];
    [key: string]: any;
  };
}

// Conflict resolution strategies
export type ConflictResolutionStrategy =
  | 'local_wins' // Keep local changes
  | 'remote_wins' // Accept remote changes
  | 'merge_fields' // Merge non-conflicting fields
  | 'timestamp_wins' // Most recent change wins
  | 'user_priority' // Higher priority user wins
  | 'manual_review' // Requires manual intervention
  | 'custom_strategy'; // Application-specific logic

// Resolved change interface
export interface ResolvedChange {
  conflictId: string;
  entityType: string;
  entityId: string;
  resolvedData: Record<string, any>;
  resolution: ConflictResolutionStrategy;
  resolvedBy: 'system' | 'user';
  resolvedByUserId?: string;
  timestamp: Date;
  appliedEvents: string[]; // Event IDs that were applied
  discardedEvents: string[]; // Event IDs that were discarded
  metadata?: Record<string, any>;
}

// Sync status for entities
export type SyncStatusType =
  | 'synced' // Up to date across all apps
  | 'pending' // Changes waiting to be propagated
  | 'conflict' // Has unresolved conflicts
  | 'error' // Sync failed due to error
  | 'locked' // Entity is locked for editing
  | 'outdated'; // Local version is behind

// Entity sync status
export interface SyncStatus {
  entityType: string;
  entityId: string;
  status: SyncStatusType;
  lastSyncAt: Date;
  version: number;
  checksum: string;
  conflicts: string[]; // Conflict IDs
  pendingEvents: string[]; // Event IDs waiting to be processed
  errorMessage?: string;
  lockedBy?: {
    userId: string;
    app: LAndDApp;
    timestamp: Date;
    reason?: string;
  };
  syncHistory: SyncHistoryEntry[];
}

// Sync history entry
export interface SyncHistoryEntry {
  id: string;
  eventId: string;
  operation: DataOperation;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  processingTime: number; // in milliseconds
  propagatedTo: LAndDApp[];
}

// Sync subscription for real-time updates
export interface SyncSubscription {
  id: string;
  userId: string;
  app: LAndDApp;
  entityType: string;
  entityId?: string; // If undefined, subscribes to all entities of this type
  filters?: {
    fields?: string[];
    operations?: DataOperation[];
    sourceApps?: LAndDApp[];
  };
  isActive: boolean;
  createdAt: Date;
  lastActivity: Date;
}

// Sync propagation rule
export interface SyncPropagationRule {
  id: string;
  sourceApp: LAndDApp;
  targetApps: LAndDApp[];
  entityType: string;
  conditions: {
    fields?: string[];
    operations?: DataOperation[];
    userRoles?: string[];
    customCondition?: string; // JavaScript expression
  };
  transformations?: {
    fieldMappings?: Record<string, string>;
    dataTransform?: string; // JavaScript function
    excludeFields?: string[];
  };
  isActive: boolean;
  priority: number;
  createdAt: Date;
}

// Real-time sync notification
export interface SyncNotification {
  id: string;
  type:
    | 'sync_success'
    | 'sync_error'
    | 'conflict_detected'
    | 'conflict_resolved';
  entityType: string;
  entityId: string;
  targetUsers: string[];
  message: string;
  data?: {
    event?: DataSyncEvent;
    conflict?: SyncConflict;
    resolution?: ResolvedChange;
    [key: string]: any;
  };
  timestamp: Date;
}

// Sync performance metrics
export interface SyncMetrics {
  entityType: string;
  period: 'hour' | 'day' | 'week' | 'month';
  totalEvents: number;
  successfulSyncs: number;
  failedSyncs: number;
  conflicts: number;
  resolvedConflicts: number;
  averageProcessingTime: number;
  propagationLatency: {
    [app in LAndDApp]: number;
  };
  errorRates: {
    [error: string]: number;
  };
  timestamp: Date;
}

// Entity lock for preventing concurrent modifications
export interface EntityLock {
  id: string;
  entityType: string;
  entityId: string;
  lockedBy: string; // User ID
  app: LAndDApp;
  lockType: 'read' | 'write' | 'exclusive';
  acquiredAt: Date;
  expiresAt: Date;
  reason?: string;
  metadata?: Record<string, any>;
}

// Sync queue item for batching operations
export interface SyncQueueItem {
  id: string;
  event: DataSyncEvent;
  targetApps: LAndDApp[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
  attempts: number;
  maxAttempts: number;
  scheduledFor: Date;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  errorMessage?: string;
  processingStarted?: Date;
  completedAt?: Date;
}

// Sync configuration per entity type
export interface SyncConfiguration {
  entityType: string;
  enabledApps: LAndDApp[];
  conflictResolution: {
    defaultStrategy: ConflictResolutionStrategy;
    fieldStrategies?: Record<string, ConflictResolutionStrategy>;
    autoResolve: boolean;
    maxRetries: number;
  };
  propagation: {
    immediate: boolean;
    batchSize?: number;
    batchTimeout?: number; // milliseconds
    retryDelay?: number; // milliseconds
  };
  versioning: {
    enabled: boolean;
    keepHistory: boolean;
    maxVersions?: number;
  };
  validation: {
    enforceSchema: boolean;
    customValidators?: string[];
  };
  isActive: boolean;
  updatedAt: Date;
}

// Schema definition for entity types
export interface EntitySchema {
  entityType: string;
  version: string;
  fields: {
    [fieldName: string]: {
      type: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array';
      required: boolean;
      unique?: boolean;
      syncable: boolean;
      conflictResolution?: ConflictResolutionStrategy;
      validation?: {
        pattern?: string;
        min?: number;
        max?: number;
        enum?: any[];
      };
    };
  };
  relationships?: {
    [fieldName: string]: {
      type: 'one-to-one' | 'one-to-many' | 'many-to-many';
      targetEntity: string;
      cascadeSync?: boolean;
    };
  };
  indexes: string[];
  createdAt: Date;
  updatedAt: Date;
}
