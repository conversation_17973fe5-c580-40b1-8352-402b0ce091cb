import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  SyncConflict,
  ConflictResolutionStrategy,
  ResolvedChange,
  DataSyncEvent,
  ConflictType,
  SyncConfiguration,
} from './sync-event.types';

/**
 * Sync Conflict Resolution Service
 * Handles detection, resolution, and management of data synchronization conflicts
 */
@Injectable()
export class SyncConflictResolutionService {
  private readonly logger = new Logger(SyncConflictResolutionService.name);

  // Active conflicts: conflictId -> SyncConflict
  private readonly conflicts = new Map<string, SyncConflict>();

  // Entity configurations: entityType -> SyncConfiguration
  private readonly configurations = new Map<string, SyncConfiguration>();

  // Conflict resolution history
  private readonly resolutionHistory: ResolvedChange[] = [];

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.initializeDefaultConfigurations();
  }

  /**
   * Detect and create conflict between two events
   */
  async detectConflict(
    localEvent: DataSyncEvent,
    remoteEvent: DataSyncEvent,
  ): Promise<SyncConflict | null> {
    // Same entity check
    if (
      localEvent.entityType !== remoteEvent.entityType ||
      localEvent.entityId !== remoteEvent.entityId
    ) {
      return null;
    }

    // Skip if same event
    if (localEvent.id === remoteEvent.id) {
      return null;
    }

    const conflictType = this.determineConflictType(localEvent, remoteEvent);
    if (!conflictType) {
      return null;
    }

    const conflictingFields = this.identifyConflictingFields(
      localEvent.changes,
      remoteEvent.changes,
    );

    if (
      conflictingFields.length === 0 &&
      conflictType === 'concurrent_update'
    ) {
      // No actual field conflicts, changes can be merged
      return null;
    }

    const conflict: SyncConflict = {
      id: this.generateConflictId(),
      entityType: localEvent.entityType,
      entityId: localEvent.entityId,
      conflictType,
      localEvent,
      remoteEvent,
      localChanges: localEvent.changes,
      remoteChanges: remoteEvent.changes,
      conflictingFields,
      timestamp: new Date(),
      status: 'pending',
      priority: this.calculateConflictPriority(conflictType, conflictingFields),
      metadata: {
        autoResolvable: this.isAutoResolvable(conflictType, conflictingFields),
        resolutionStrategy: this.getDefaultResolutionStrategy(
          localEvent.entityType,
        ),
        affectedUsers: [localEvent.sourceUserId, remoteEvent.sourceUserId],
      },
    };

    this.conflicts.set(conflict.id, conflict);

    this.logger.warn(
      `Conflict detected: ${conflict.id} for ${conflict.entityType}:${conflict.entityId}`,
    );

    // Emit conflict event
    this.eventEmitter.emit('sync.conflict.detected', conflict);

    // Auto-resolve if possible
    if (conflict.metadata.autoResolvable) {
      const resolution = await this.autoResolveConflict(conflict);
      if (resolution) {
        return null; // Conflict was auto-resolved
      }
    }

    return conflict;
  }

  /**
   * Resolve conflict using specified strategy
   */
  async resolveConflict(
    conflictId: string,
    strategy: ConflictResolutionStrategy,
    resolvedByUserId?: string,
    customData?: Record<string, any>,
  ): Promise<ResolvedChange | null> {
    const conflict = this.conflicts.get(conflictId);
    if (!conflict) {
      this.logger.error(`Conflict not found: ${conflictId}`);
      return null;
    }

    if (conflict.status !== 'pending') {
      this.logger.warn(
        `Conflict ${conflictId} is not in pending status: ${conflict.status}`,
      );
      return null;
    }

    let resolvedData: Record<string, any>;
    let appliedEvents: string[] = [];
    let discardedEvents: string[] = [];

    try {
      switch (strategy) {
        case 'local_wins':
          resolvedData = { ...conflict.localChanges };
          appliedEvents = [conflict.localEvent.id];
          discardedEvents = [conflict.remoteEvent.id];
          break;

        case 'remote_wins':
          resolvedData = { ...conflict.remoteChanges };
          appliedEvents = [conflict.remoteEvent.id];
          discardedEvents = [conflict.localEvent.id];
          break;

        case 'merge_fields':
          resolvedData = this.mergeFields(conflict);
          appliedEvents = [conflict.localEvent.id, conflict.remoteEvent.id];
          break;

        case 'timestamp_wins':
          if (conflict.localEvent.timestamp >= conflict.remoteEvent.timestamp) {
            resolvedData = { ...conflict.localChanges };
            appliedEvents = [conflict.localEvent.id];
            discardedEvents = [conflict.remoteEvent.id];
          } else {
            resolvedData = { ...conflict.remoteChanges };
            appliedEvents = [conflict.remoteEvent.id];
            discardedEvents = [conflict.localEvent.id];
          }
          break;

        case 'user_priority':
          resolvedData = await this.resolveByUserPriority(conflict);
          appliedEvents = [conflict.localEvent.id]; // Default, can be changed by priority logic
          break;

        case 'custom_strategy':
          if (customData) {
            resolvedData = customData;
            appliedEvents = [conflict.localEvent.id, conflict.remoteEvent.id];
          } else {
            throw new Error('Custom data required for custom strategy');
          }
          break;

        default:
          throw new Error(`Unsupported resolution strategy: ${strategy}`);
      }

      const resolution: ResolvedChange = {
        conflictId,
        entityType: conflict.entityType,
        entityId: conflict.entityId,
        resolvedData,
        resolution: strategy,
        resolvedBy: resolvedByUserId ? 'user' : 'system',
        resolvedByUserId,
        timestamp: new Date(),
        appliedEvents,
        discardedEvents,
        metadata: {
          originalConflict: conflict,
          customData,
        },
      };

      // Update conflict status
      conflict.status = 'resolved';

      // Store resolution
      this.resolutionHistory.unshift(resolution);

      // Keep only last 1000 resolutions
      if (this.resolutionHistory.length > 1000) {
        this.resolutionHistory.splice(1000);
      }

      this.logger.log(
        `Conflict resolved: ${conflictId} using ${strategy} strategy`,
      );

      // Emit resolution event
      this.eventEmitter.emit('sync.conflict.resolved', resolution);

      return resolution;
    } catch (error) {
      conflict.status = 'failed';
      this.logger.error(`Failed to resolve conflict ${conflictId}:`, error);

      this.eventEmitter.emit('sync.conflict.failed', {
        conflict,
        error: error.message,
      });

      return null;
    }
  }

  /**
   * Get all pending conflicts
   */
  getPendingConflicts(entityType?: string): SyncConflict[] {
    const conflicts = Array.from(this.conflicts.values()).filter(
      (c) => c.status === 'pending',
    );

    if (entityType) {
      return conflicts.filter((c) => c.entityType === entityType);
    }

    return conflicts.sort((a, b) => {
      // Sort by priority then timestamp
      const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
      const priorityDiff =
        priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;

      return a.timestamp.getTime() - b.timestamp.getTime();
    });
  }

  /**
   * Get conflict by ID
   */
  getConflict(conflictId: string): SyncConflict | undefined {
    return this.conflicts.get(conflictId);
  }

  /**
   * Get resolution history
   */
  getResolutionHistory(limit: number = 50): ResolvedChange[] {
    return this.resolutionHistory.slice(0, limit);
  }

  /**
   * Get conflicts for entity
   */
  getEntityConflicts(entityType: string, entityId: string): SyncConflict[] {
    return Array.from(this.conflicts.values()).filter(
      (c) => c.entityType === entityType && c.entityId === entityId,
    );
  }

  /**
   * Update sync configuration for entity type
   */
  updateConfiguration(
    entityType: string,
    config: Partial<SyncConfiguration>,
  ): void {
    const existing =
      this.configurations.get(entityType) ||
      this.getDefaultConfiguration(entityType);
    const updated = { ...existing, ...config, updatedAt: new Date() };
    this.configurations.set(entityType, updated);

    this.logger.log(
      `Updated sync configuration for entity type: ${entityType}`,
    );
  }

  /**
   * Get sync configuration for entity type
   */
  getConfiguration(entityType: string): SyncConfiguration {
    return (
      this.configurations.get(entityType) ||
      this.getDefaultConfiguration(entityType)
    );
  }

  /**
   * Get conflict statistics
   */
  getConflictStats() {
    const allConflicts = Array.from(this.conflicts.values());
    const pending = allConflicts.filter((c) => c.status === 'pending');
    const resolved = allConflicts.filter((c) => c.status === 'resolved');
    const failed = allConflicts.filter((c) => c.status === 'failed');

    const byType = {} as Record<ConflictType, number>;
    allConflicts.forEach((c) => {
      byType[c.conflictType] = (byType[c.conflictType] || 0) + 1;
    });

    const byPriority = {} as Record<string, number>;
    allConflicts.forEach((c) => {
      byPriority[c.priority] = (byPriority[c.priority] || 0) + 1;
    });

    return {
      total: allConflicts.length,
      pending: pending.length,
      resolved: resolved.length,
      failed: failed.length,
      byType,
      byPriority,
      resolutionHistory: this.resolutionHistory.length,
    };
  }

  /**
   * Cleanup old conflicts and resolutions
   */
  cleanup(): void {
    const now = new Date();
    const retentionPeriod = 7 * 24 * 60 * 60 * 1000; // 7 days
    let removedCount = 0;

    // Remove old resolved/failed conflicts
    this.conflicts.forEach((conflict, id) => {
      if (
        (conflict.status === 'resolved' || conflict.status === 'failed') &&
        now.getTime() - conflict.timestamp.getTime() > retentionPeriod
      ) {
        this.conflicts.delete(id);
        removedCount++;
      }
    });

    // Keep only last 500 resolution history entries
    if (this.resolutionHistory.length > 500) {
      this.resolutionHistory.splice(500);
    }

    if (removedCount > 0) {
      this.logger.debug(`Cleaned up ${removedCount} old conflicts`);
    }
  }

  /**
   * Determine type of conflict between events
   */
  private determineConflictType(
    localEvent: DataSyncEvent,
    remoteEvent: DataSyncEvent,
  ): ConflictType | null {
    // Version mismatch
    if (localEvent.version !== remoteEvent.version) {
      return 'version_mismatch';
    }

    // Concurrent updates to same entity
    if (
      localEvent.operation === 'update' &&
      remoteEvent.operation === 'update'
    ) {
      const timeDiff = Math.abs(
        localEvent.timestamp.getTime() - remoteEvent.timestamp.getTime(),
      );
      // Consider concurrent if within 30 seconds
      if (timeDiff < 30000) {
        return 'concurrent_update';
      }
    }

    // Delete vs update conflict
    if (
      (localEvent.operation === 'delete' &&
        remoteEvent.operation === 'update') ||
      (localEvent.operation === 'update' && remoteEvent.operation === 'delete')
    ) {
      return 'data_inconsistency';
    }

    return null;
  }

  /**
   * Identify fields that conflict between changes
   */
  private identifyConflictingFields(
    localChanges: Record<string, any>,
    remoteChanges: Record<string, any>,
  ): string[] {
    const conflictingFields: string[] = [];

    // Check for fields present in both changes with different values
    for (const field in localChanges) {
      if (field in remoteChanges) {
        const localValue = JSON.stringify(localChanges[field]);
        const remoteValue = JSON.stringify(remoteChanges[field]);

        if (localValue !== remoteValue) {
          conflictingFields.push(field);
        }
      }
    }

    return conflictingFields;
  }

  /**
   * Calculate conflict priority based on type and fields
   */
  private calculateConflictPriority(
    conflictType: ConflictType,
    conflictingFields: string[],
  ): 'low' | 'medium' | 'high' | 'critical' {
    // Critical priority conflicts
    if (conflictType === 'data_inconsistency') {
      return 'critical';
    }

    // High priority for version mismatches
    if (conflictType === 'version_mismatch') {
      return 'high';
    }

    // Priority based on number of conflicting fields
    if (conflictingFields.length > 5) {
      return 'high';
    } else if (conflictingFields.length > 2) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Check if conflict can be auto-resolved
   */
  private isAutoResolvable(
    conflictType: ConflictType,
    conflictingFields: string[],
  ): boolean {
    // Never auto-resolve data inconsistencies
    if (conflictType === 'data_inconsistency') {
      return false;
    }

    // Auto-resolve simple field conflicts
    if (conflictType === 'concurrent_update' && conflictingFields.length <= 2) {
      return true;
    }

    return false;
  }

  /**
   * Get default resolution strategy for entity type
   */
  private getDefaultResolutionStrategy(
    entityType: string,
  ): ConflictResolutionStrategy {
    const config = this.getConfiguration(entityType);
    return config.conflictResolution.defaultStrategy;
  }

  /**
   * Auto-resolve conflict if possible
   */
  private async autoResolveConflict(
    conflict: SyncConflict,
  ): Promise<ResolvedChange | null> {
    const config = this.getConfiguration(conflict.entityType);

    if (!config.conflictResolution.autoResolve) {
      return null;
    }

    try {
      return await this.resolveConflict(
        conflict.id,
        config.conflictResolution.defaultStrategy,
      );
    } catch (error) {
      this.logger.error(
        `Auto-resolution failed for conflict ${conflict.id}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Merge non-conflicting fields
   */
  private mergeFields(conflict: SyncConflict): Record<string, any> {
    const merged = { ...conflict.localChanges };

    // Add non-conflicting fields from remote
    for (const field in conflict.remoteChanges) {
      if (!conflict.conflictingFields.includes(field)) {
        merged[field] = conflict.remoteChanges[field];
      }
    }

    return merged;
  }

  /**
   * Resolve conflict based on user priority
   */
  private async resolveByUserPriority(
    conflict: SyncConflict,
  ): Promise<Record<string, any>> {
    // Simplified priority logic - in production, this would check user roles/permissions
    // For now, assume local user has priority
    return { ...conflict.localChanges };
  }

  /**
   * Initialize default configurations for entity types
   */
  private initializeDefaultConfigurations(): void {
    const defaultConfig: SyncConfiguration = {
      entityType: 'default',
      enabledApps: ['training', 'vendors', 'wins'],
      conflictResolution: {
        defaultStrategy: 'timestamp_wins',
        autoResolve: true,
        maxRetries: 3,
      },
      propagation: {
        immediate: true,
        batchSize: 50,
        batchTimeout: 5000,
        retryDelay: 1000,
      },
      versioning: {
        enabled: true,
        keepHistory: true,
        maxVersions: 10,
      },
      validation: {
        enforceSchema: true,
      },
      isActive: true,
      updatedAt: new Date(),
    };

    // Set up configurations for L&D entity types
    const entityTypes = [
      'course',
      'assessment',
      'enrollment',
      'progress',
      'vendor',
      'proposal',
      'contract',
      'rfp',
      'submission',
      'win',
      'achievement',
      'team',
    ];

    entityTypes.forEach((entityType) => {
      this.configurations.set(entityType, {
        ...defaultConfig,
        entityType,
      });
    });

    this.logger.log('Initialized default sync configurations');
  }

  /**
   * Get default configuration for entity type
   */
  private getDefaultConfiguration(entityType: string): SyncConfiguration {
    return {
      entityType,
      enabledApps: ['training', 'vendors', 'wins'],
      conflictResolution: {
        defaultStrategy: 'timestamp_wins',
        autoResolve: true,
        maxRetries: 3,
      },
      propagation: {
        immediate: true,
        batchSize: 50,
        batchTimeout: 5000,
        retryDelay: 1000,
      },
      versioning: {
        enabled: true,
        keepHistory: true,
        maxVersions: 10,
      },
      validation: {
        enforceSchema: true,
      },
      isActive: true,
      updatedAt: new Date(),
    };
  }

  /**
   * Generate unique conflict ID
   */
  private generateConflictId(): string {
    return `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
