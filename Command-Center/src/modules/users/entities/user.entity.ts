import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  BeforeInsert,
  Index,
  OneToMany,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { Document } from '../../documents/entities/document.entity';
import { Conversation } from '../../conversations/entities/conversation.entity';
import { File } from '../../files/entities/file.entity';
import { AiConversation } from '../../ai/entities/ai-conversation.entity';
import { Folder } from '../../documents/entities/folder.entity';
import { Tag } from '../../documents/entities/tag.entity';

@Entity('users')
@Index('idx_user_email', ['email'])
@Index('idx_user_created', ['createdAt'])
@Index('idx_user_active_status', ['isActive', 'deletedAt'])
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  @Index('idx_user_email_unique')
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  @Index('idx_user_username')
  username: string;

  @Column({ nullable: true })
  avatarUrl: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  phoneNumber: string;

  @Column({ type: 'text', nullable: true })
  bio: string;

  @Column({ type: 'jsonb', default: {} })
  preferences: Record<string, any>;

  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @Column({ type: 'varchar', array: true, default: [] })
  roles: string[];

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: false })
  isEmailVerified: boolean;

  @Column({ nullable: true })
  emailVerifiedAt: Date;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @Column({ type: 'inet', nullable: true })
  lastLoginIp: string;

  @Column({ type: 'int', default: 0 })
  loginCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  deletedAt: Date;

  // Relations
  @OneToMany(() => Document, (document) => document.owner)
  documents: Document[];

  @OneToMany(() => Conversation, (conversation) => conversation.owner)
  conversations: Conversation[];

  @OneToMany(() => File, (file) => file.uploadedBy)
  files: File[];

  @OneToMany(() => AiConversation, (conversation) => conversation.user)
  aiConversations: AiConversation[];

  @OneToMany(() => Folder, (folder) => folder.owner)
  folders: Folder[];

  @OneToMany(() => Tag, (tag) => tag.owner)
  tags: Tag[];

  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }

  async comparePassword(attempt: string): Promise<boolean> {
    return await bcrypt.compare(attempt, this.password);
  }
}
