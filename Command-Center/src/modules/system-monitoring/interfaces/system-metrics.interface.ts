export interface SystemMetrics {
  timestamp: Date;
  cpu: CPUMetrics;
  memory: MemoryMetrics;
  disk: DiskMetrics;
  network: NetworkMetrics;
  services: ServiceMetrics;
  docker: DockerMetrics;
  database: DatabaseMetrics;
}

export interface CPUMetrics {
  usage: number; // percentage
  cores: number;
  load: number[];
  temperature?: number;
}

export interface MemoryMetrics {
  total: number; // bytes
  used: number; // bytes
  available: number; // bytes
  percentage: number;
  swap?: {
    total: number;
    used: number;
  };
}

export interface DiskMetrics {
  total: number; // bytes
  used: number; // bytes
  available: number; // bytes
  percentage: number;
  partitions: DiskPartition[];
}

export interface DiskPartition {
  device: string;
  mountpoint: string;
  total: number;
  used: number;
  available: number;
  percentage: number;
}

export interface NetworkMetrics {
  interfaces: NetworkInterface[];
  totalBytesIn: number;
  totalBytesOut: number;
  packetsIn: number;
  packetsOut: number;
}

export interface NetworkInterface {
  name: string;
  bytesIn: number;
  bytesOut: number;
  packetsIn: number;
  packetsOut: number;
  speed?: number;
  isUp: boolean;
}

export interface ServiceMetrics {
  total: number;
  running: number;
  stopped: number;
  error: number;
  services: ServiceStatus[];
}

export interface ServiceStatus {
  name: string;
  status: 'running' | 'stopped' | 'error' | 'starting' | 'stopping';
  pid?: number;
  uptime?: number;
  memory?: number;
  cpu?: number;
}

export interface DockerMetrics {
  containers: ContainerMetrics;
  images: number;
  volumes: number;
  networks: number;
  containerList: DockerContainer[];
}

export interface ContainerMetrics {
  total: number;
  running: number;
  stopped: number;
  paused: number;
}

export interface DockerContainer {
  id: string;
  name: string;
  image: string;
  status: string;
  state: 'running' | 'stopped' | 'paused' | 'restarting' | 'removing' | 'exited';
  created: Date;
  ports: string[];
  uptime?: string;
  health?: 'healthy' | 'unhealthy' | 'starting' | 'none';
  stats?: {
    cpu: number;
    memory: {
      usage: number;
      limit: number;
      percentage: number;
    };
    network: {
      rx: number;
      tx: number;
    };
  };
}

export interface DatabaseMetrics {
  connections: {
    active: number;
    idle: number;
    total: number;
    max: number;
  };
  queries: {
    total: number;
    slow: number;
    failed: number;
    averageTime: number;
  };
  size: {
    total: number;
    data: number;
    index: number;
  };
  uptime: number; // seconds
  performance: {
    cacheHitRatio: number;
    replicationLag?: number;
    locksWaiting: number;
  };
}

export interface IntegrationHealth {
  id: string;
  name: string;
  description: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  responseTime: number; // milliseconds
  uptime: number; // percentage
  endpoint: string;
  version?: string;
  dependencies?: IntegrationDependency[];
  healthChecks: HealthCheck[];
}

export interface IntegrationDependency {
  name: string;
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
}

export interface HealthCheck {
  name: string;
  status: 'pass' | 'fail' | 'warn';
  responseTime: number;
  message?: string;
  details?: any;
}

export interface SystemEvent {
  id: string;
  timestamp: Date;
  type: 'info' | 'warning' | 'error' | 'success';
  category: 'system' | 'docker' | 'database' | 'integration' | 'service';
  title: string;
  message: string;
  source: string;
  metadata?: any;
  acknowledged?: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

export interface MonitoringConfig {
  intervals: {
    metrics: number; // milliseconds
    health: number; // milliseconds
    events: number; // milliseconds
  };
  thresholds: {
    cpu: number;
    memory: number;
    disk: number;
    responseTime: number;
  };
  retention: {
    metrics: number; // days
    events: number; // days
  };
  alerts: {
    enabled: boolean;
    channels: string[];
  };
}