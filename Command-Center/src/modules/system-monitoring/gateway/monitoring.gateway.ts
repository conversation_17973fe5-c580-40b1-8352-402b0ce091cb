import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { OnEvent } from '@nestjs/event-emitter';
import { SystemMetricsService } from '../services/system-metrics.service';
import { IntegrationHealthService } from '../services/integration-health.service';
import { SystemEventsService } from '../services/system-events.service';
import { SystemEvent } from '../interfaces/system-metrics.interface';

interface ClientSubscriptions {
  metrics: boolean;
  events: boolean;
  integrations: boolean;
  docker: boolean;
  database: boolean;
}

@WebSocketGateway({
  namespace: '/monitoring',
  cors: {
    origin: process.env.DASHBOARD_URL || 'http://localhost:3007',
    credentials: true,
  },
})
export class MonitoringGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(MonitoringGateway.name);
  private clientSubscriptions = new Map<string, ClientSubscriptions>();
  private metricsInterval: NodeJS.Timeout | null = null;
  private connectedClients = new Set<string>();

  constructor(
    private readonly systemMetricsService: SystemMetricsService,
    private readonly integrationHealthService: IntegrationHealthService,
    private readonly systemEventsService: SystemEventsService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('Monitoring WebSocket Gateway initialized');
    this.startMetricsInterval();
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    this.connectedClients.add(client.id);
    
    // Initialize default subscriptions
    this.clientSubscriptions.set(client.id, {
      metrics: false,
      events: false,
      integrations: false,
      docker: false,
      database: false,
    });

    // Send initial data
    this.sendInitialData(client);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.connectedClients.delete(client.id);
    this.clientSubscriptions.delete(client.id);

    // Stop metrics interval if no clients are connected
    if (this.connectedClients.size === 0 && this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
      this.logger.log('Stopped metrics interval - no connected clients');
    }
  }

  @SubscribeMessage('subscribe')
  handleSubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { type: keyof ClientSubscriptions; enabled: boolean },
  ) {
    const subscriptions = this.clientSubscriptions.get(client.id);
    if (subscriptions) {
      subscriptions[data.type] = data.enabled;
      this.logger.debug(`Client ${client.id} ${data.enabled ? 'subscribed to' : 'unsubscribed from'} ${data.type}`);
      
      // Send initial data for newly subscribed types
      if (data.enabled) {
        this.sendInitialDataForType(client, data.type);
      }
    }
  }

  @SubscribeMessage('getMetrics')
  async handleGetMetrics(@ConnectedSocket() client: Socket) {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics(false);
      client.emit('metrics', metrics);
    } catch (error) {
      this.logger.error('Failed to get metrics for client:', error);
      client.emit('error', { message: 'Failed to retrieve metrics' });
    }
  }

  @SubscribeMessage('getIntegrations')
  async handleGetIntegrations(@ConnectedSocket() client: Socket) {
    try {
      const integrations = await this.integrationHealthService.getIntegrationSummary();
      client.emit('integrations', integrations);
    } catch (error) {
      this.logger.error('Failed to get integrations for client:', error);
      client.emit('error', { message: 'Failed to retrieve integrations' });
    }
  }

  @SubscribeMessage('getEvents')
  async handleGetEvents(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { limit?: number; type?: string; category?: string },
  ) {
    try {
      const events = this.systemEventsService.getEvents(data);
      client.emit('events', events);
    } catch (error) {
      this.logger.error('Failed to get events for client:', error);
      client.emit('error', { message: 'Failed to retrieve events' });
    }
  }

  @SubscribeMessage('acknowledgeEvent')
  async handleAcknowledgeEvent(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { eventId: string; acknowledgedBy: string },
  ) {
    try {
      const success = this.systemEventsService.acknowledgeEvent(data.eventId, data.acknowledgedBy);
      client.emit('eventAcknowledged', { eventId: data.eventId, success });
      
      if (success) {
        // Broadcast to all clients
        this.server.emit('eventAcknowledged', { eventId: data.eventId });
      }
    } catch (error) {
      this.logger.error('Failed to acknowledge event:', error);
      client.emit('error', { message: 'Failed to acknowledge event' });
    }
  }

  @SubscribeMessage('refreshIntegration')
  async handleRefreshIntegration(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { integrationId: string },
  ) {
    try {
      const health = await this.integrationHealthService.refreshIntegrationHealth(data.integrationId);
      client.emit('integrationRefreshed', health);
      
      // Broadcast to all subscribed clients
      this.broadcastToSubscribed('integrations', 'integrationUpdated', health);
    } catch (error) {
      this.logger.error('Failed to refresh integration:', error);
      client.emit('error', { message: 'Failed to refresh integration' });
    }
  }

  private async sendInitialData(client: Socket) {
    try {
      const [metrics, integrations, events] = await Promise.all([
        this.systemMetricsService.getSystemMetrics(),
        this.integrationHealthService.getIntegrationSummary(),
        this.systemEventsService.getEvents({ limit: 20 }),
      ]);

      client.emit('initialData', {
        metrics,
        integrations,
        events,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Failed to send initial data:', error);
      client.emit('error', { message: 'Failed to load initial data' });
    }
  }

  private async sendInitialDataForType(client: Socket, type: keyof ClientSubscriptions) {
    try {
      switch (type) {
        case 'metrics':
          const metrics = await this.systemMetricsService.getSystemMetrics();
          client.emit('metrics', metrics);
          break;
        case 'integrations':
          const integrations = await this.integrationHealthService.getIntegrationSummary();
          client.emit('integrations', integrations);
          break;
        case 'events':
          const events = this.systemEventsService.getEvents({ limit: 20 });
          client.emit('events', events);
          break;
        case 'docker':
          const dockerMetrics = await this.systemMetricsService.getSystemMetrics();
          client.emit('dockerMetrics', dockerMetrics.docker);
          break;
        case 'database':
          const databaseMetrics = await this.systemMetricsService.getSystemMetrics();
          client.emit('databaseMetrics', databaseMetrics.database);
          break;
      }
    } catch (error) {
      this.logger.error(`Failed to send initial data for ${type}:`, error);
    }
  }

  private startMetricsInterval() {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    this.metricsInterval = setInterval(async () => {
      if (this.connectedClients.size === 0) return;

      try {
        const metrics = await this.systemMetricsService.getSystemMetrics();
        this.broadcastToSubscribed('metrics', 'metricsUpdate', metrics);
      } catch (error) {
        this.logger.error('Failed to broadcast metrics update:', error);
      }
    }, 5000); // Update every 5 seconds

    this.logger.log('Started metrics interval');
  }

  private broadcastToSubscribed(
    subscriptionType: keyof ClientSubscriptions,
    event: string,
    data: any,
  ) {
    this.clientSubscriptions.forEach((subscriptions, clientId) => {
      if (subscriptions[subscriptionType]) {
        const client = this.server.sockets.get(clientId);
        if (client) {
          client.emit(event, data);
        }
      }
    });
  }

  // Event listeners for real-time updates
  @OnEvent('system.event.new')
  handleNewSystemEvent(event: SystemEvent) {
    this.broadcastToSubscribed('events', 'newEvent', event);
  }

  @OnEvent('integration.health.changed')
  async handleIntegrationHealthChanged(data: any) {
    try {
      const integrations = await this.integrationHealthService.getIntegrationSummary();
      this.broadcastToSubscribed('integrations', 'integrationsUpdate', integrations);
    } catch (error) {
      this.logger.error('Failed to broadcast integration update:', error);
    }
  }

  @OnEvent('docker.container.status.changed')
  async handleDockerStatusChanged(data: any) {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      this.broadcastToSubscribed('docker', 'dockerUpdate', metrics.docker);
    } catch (error) {
      this.logger.error('Failed to broadcast Docker update:', error);
    }
  }

  @OnEvent('database.metrics.updated')
  async handleDatabaseMetricsUpdated(data: any) {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      this.broadcastToSubscribed('database', 'databaseUpdate', metrics.database);
    } catch (error) {
      this.logger.error('Failed to broadcast database update:', error);
    }
  }

  // Admin methods
  getConnectionStatus() {
    return {
      connectedClients: this.connectedClients.size,
      clientSubscriptions: Array.from(this.clientSubscriptions.entries()),
      metricsIntervalActive: this.metricsInterval !== null,
    };
  }

  // Method to trigger events for testing
  triggerTestEvent() {
    this.systemEventsService.simulateEvents();
  }
}