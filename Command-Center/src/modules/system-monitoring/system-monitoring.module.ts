import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { SystemMonitoringController } from './controllers/system-monitoring.controller';
import { SystemMetricsService } from './services/system-metrics.service';
import { IntegrationHealthService } from './services/integration-health.service';
import { SystemEventsService } from './services/system-events.service';
import { MonitoringGateway } from './gateway/monitoring.gateway';

@Module({
  imports: [
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 3,
    }),
    ConfigModule,
    EventEmitterModule,
  ],
  controllers: [SystemMonitoringController],
  providers: [
    SystemMetricsService,
    IntegrationHealthService,
    SystemEventsService,
    MonitoringGateway,
  ],
  exports: [
    SystemMetricsService,
    IntegrationHealthService,
    SystemEventsService,
    MonitoringGateway,
  ],
})
export class SystemMonitoringModule {}