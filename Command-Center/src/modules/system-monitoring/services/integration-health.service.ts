import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, timeout } from 'rxjs';
import {
  IntegrationHealth,
  HealthCheck,
  IntegrationDependency,
} from '../interfaces/system-metrics.interface';

interface IntegrationConfig {
  id: string;
  name: string;
  description: string;
  baseUrl: string;
  healthEndpoint: string;
  timeout: number;
  dependencies?: string[];
}

@Injectable()
export class IntegrationHealthService {
  private readonly logger = new Logger(IntegrationHealthService.name);
  private healthCache = new Map<string, IntegrationHealth>();
  private readonly CACHE_DURATION = 30000; // 30 seconds

  private readonly integrationConfigs: IntegrationConfig[] = [
    {
      id: 'amna',
      name: 'AMNA',
      description: 'AI-powered content analysis and automation platform',
      baseUrl: process.env.AMNA_URL || 'http://localhost:3001',
      healthEndpoint: '/api/health',
      timeout: 5000,
      dependencies: ['database', 'openai-api'],
    },
    {
      id: 'econnect',
      name: 'E-Connect',
      description: 'Email communication and automation platform',
      baseUrl: process.env.ECONNECT_URL || 'http://localhost:3002',
      healthEndpoint: '/api/health',
      timeout: 5000,
      dependencies: ['database', 'smtp-server'],
    },
    {
      id: 'lighthouse',
      name: 'Lighthouse',
      description: 'Research and knowledge management system',
      baseUrl: process.env.LIGHTHOUSE_URL || 'http://localhost:3003',
      healthEndpoint: '/api/health',
      timeout: 5000,
      dependencies: ['database', 'elasticsearch'],
    },
    {
      id: 'training-needs',
      name: 'Training Needs Analysis',
      description: 'Training requirements and gap analysis platform',
      baseUrl: process.env.TRAINING_NEEDS_URL || 'http://localhost:3004',
      healthEndpoint: '/api/health',
      timeout: 5000,
      dependencies: ['database'],
    },
    {
      id: 'vendors',
      name: 'Vendors',
      description: 'Vendor management and procurement platform',
      baseUrl: process.env.VENDORS_URL || 'http://localhost:3005',
      healthEndpoint: '/api/health',
      timeout: 5000,
      dependencies: ['database'],
    },
    {
      id: 'wins-of-week',
      name: 'Wins of the Week',
      description: 'Achievement tracking and reporting platform',
      baseUrl: process.env.WINS_URL || 'http://localhost:3006',
      healthEndpoint: '/api/health',
      timeout: 5000,
      dependencies: ['database'],
    },
    {
      id: 'luminar-dashboard',
      name: 'Luminar Dashboard',
      description: 'Unified monitoring and analytics dashboard',
      baseUrl: process.env.DASHBOARD_URL || 'http://localhost:3007',
      healthEndpoint: '/api/health',
      timeout: 5000,
      dependencies: ['command-center'],
    },
  ];

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async getAllIntegrationsHealth(): Promise<IntegrationHealth[]> {
    const healthPromises = this.integrationConfigs.map(config =>
      this.getIntegrationHealth(config.id)
    );

    const results = await Promise.allSettled(healthPromises);
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        // Return unhealthy status for failed checks
        const config = this.integrationConfigs[index];
        return {
          id: config.id,
          name: config.name,
          description: config.description,
          status: 'unhealthy' as const,
          lastCheck: new Date(),
          responseTime: 0,
          uptime: 0,
          endpoint: `${config.baseUrl}${config.healthEndpoint}`,
          healthChecks: [{
            name: 'connectivity',
            status: 'fail' as const,
            responseTime: 0,
            message: result.reason?.message || 'Health check failed',
          }],
        };
      }
    });
  }

  async getIntegrationHealth(integrationId: string): Promise<IntegrationHealth> {
    const cached = this.healthCache.get(integrationId);
    if (cached && (Date.now() - cached.lastCheck.getTime()) < this.CACHE_DURATION) {
      return cached;
    }

    const config = this.integrationConfigs.find(c => c.id === integrationId);
    if (!config) {
      throw new Error(`Integration config not found for ID: ${integrationId}`);
    }

    const startTime = Date.now();
    let health: IntegrationHealth;

    try {
      const healthChecks: HealthCheck[] = [];
      
      // Primary connectivity check
      const connectivityCheck = await this.performConnectivityCheck(config);
      healthChecks.push(connectivityCheck);
      
      // Endpoint-specific health check
      const endpointCheck = await this.performEndpointHealthCheck(config);
      healthChecks.push(endpointCheck);
      
      // Dependency checks
      if (config.dependencies) {
        const dependencyChecks = await this.performDependencyChecks(config);
        healthChecks.push(...dependencyChecks);
      }

      const responseTime = Date.now() - startTime;
      const overallStatus = this.calculateOverallStatus(healthChecks);
      const uptime = this.calculateUptime(integrationId, overallStatus === 'healthy');

      health = {
        id: config.id,
        name: config.name,
        description: config.description,
        status: overallStatus,
        lastCheck: new Date(),
        responseTime,
        uptime,
        endpoint: `${config.baseUrl}${config.healthEndpoint}`,
        healthChecks,
      };

    } catch (error) {
      this.logger.error(`Health check failed for ${config.name}:`, error);
      
      health = {
        id: config.id,
        name: config.name,
        description: config.description,
        status: 'unhealthy',
        lastCheck: new Date(),
        responseTime: Date.now() - startTime,
        uptime: this.calculateUptime(integrationId, false),
        endpoint: `${config.baseUrl}${config.healthEndpoint}`,
        healthChecks: [{
          name: 'connectivity',
          status: 'fail',
          responseTime: Date.now() - startTime,
          message: error.message,
        }],
      };
    }

    this.healthCache.set(integrationId, health);
    return health;
  }

  private async performConnectivityCheck(config: IntegrationConfig): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${config.baseUrl}${config.healthEndpoint}`)
          .pipe(timeout(config.timeout))
      );
      
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'connectivity',
        status: response.status >= 200 && response.status < 300 ? 'pass' : 'fail',
        responseTime,
        message: response.status >= 200 && response.status < 300 
          ? 'Service is reachable' 
          : `HTTP ${response.status}`,
        details: {
          statusCode: response.status,
          headers: response.headers,
        },
      };
    } catch (error) {
      return {
        name: 'connectivity',
        status: 'fail',
        responseTime: Date.now() - startTime,
        message: error.message || 'Connection failed',
        details: { error: error.message },
      };
    }
  }

  private async performEndpointHealthCheck(config: IntegrationConfig): Promise<HealthCheck> {
    const startTime = Date.now();
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${config.baseUrl}${config.healthEndpoint}`)
          .pipe(timeout(config.timeout))
      );
      
      const responseTime = Date.now() - startTime;
      const data = response.data;
      
      // Check if the response indicates healthy status
      const isHealthy = data && (
        data.status === 'healthy' || 
        data.status === 'ok' || 
        data.health === 'ok' ||
        (data.checks && Object.values(data.checks).every(check => 
          typeof check === 'object' && check !== null && (check as any).status === 'ok'
        ))
      );
      
      return {
        name: 'endpoint_health',
        status: isHealthy ? 'pass' : 'warn',
        responseTime,
        message: isHealthy ? 'Service reports healthy' : 'Service health unclear',
        details: data,
      };
    } catch (error) {
      return {
        name: 'endpoint_health',
        status: 'fail',
        responseTime: Date.now() - startTime,
        message: 'Health endpoint check failed',
        details: { error: error.message },
      };
    }
  }

  private async performDependencyChecks(config: IntegrationConfig): Promise<HealthCheck[]> {
    const checks: HealthCheck[] = [];
    
    if (!config.dependencies) return checks;
    
    for (const dependency of config.dependencies) {
      const startTime = Date.now();
      let check: HealthCheck;
      
      try {
        // This could be expanded to actually check specific dependencies
        // For now, we'll simulate dependency checks
        const isHealthy = await this.checkDependency(dependency);
        
        check = {
          name: `dependency_${dependency}`,
          status: isHealthy ? 'pass' : 'fail',
          responseTime: Date.now() - startTime,
          message: isHealthy ? `${dependency} is available` : `${dependency} is unavailable`,
        };
      } catch (error) {
        check = {
          name: `dependency_${dependency}`,
          status: 'fail',
          responseTime: Date.now() - startTime,
          message: `Failed to check ${dependency}`,
          details: { error: error.message },
        };
      }
      
      checks.push(check);
    }
    
    return checks;
  }

  private async checkDependency(dependency: string): Promise<boolean> {
    // This would implement actual dependency checks
    // For example, checking database connectivity, external API availability, etc.
    
    switch (dependency) {
      case 'database':
        // Check database connectivity
        // For now, return a simulated result
        return Math.random() > 0.1; // 90% uptime simulation
        
      case 'openai-api':
        try {
          // Could ping OpenAI API health endpoint
          return Math.random() > 0.05; // 95% uptime simulation
        } catch {
          return false;
        }
        
      case 'smtp-server':
        // Check SMTP server connectivity
        return Math.random() > 0.02; // 98% uptime simulation
        
      case 'elasticsearch':
        // Check Elasticsearch connectivity
        return Math.random() > 0.08; // 92% uptime simulation
        
      case 'command-center':
        try {
          // Self-check
          return true;
        } catch {
          return false;
        }
        
      default:
        return Math.random() > 0.1; // Default 90% uptime
    }
  }

  private calculateOverallStatus(healthChecks: HealthCheck[]): IntegrationHealth['status'] {
    if (healthChecks.length === 0) return 'unknown';
    
    const hasFailures = healthChecks.some(check => check.status === 'fail');
    const hasWarnings = healthChecks.some(check => check.status === 'warn');
    
    if (hasFailures) return 'unhealthy';
    if (hasWarnings) return 'degraded';
    return 'healthy';
  }

  private calculateUptime(integrationId: string, isCurrentlyHealthy: boolean): number {
    // This would be calculated from historical data
    // For now, return a simulated uptime percentage
    const baseUptime = isCurrentlyHealthy ? 99.5 : 85.0;
    return Math.max(0, Math.min(100, baseUptime + (Math.random() - 0.5) * 10));
  }

  async getIntegrationSummary() {
    const allHealth = await this.getAllIntegrationsHealth();
    
    const summary = {
      total: allHealth.length,
      healthy: allHealth.filter(h => h.status === 'healthy').length,
      degraded: allHealth.filter(h => h.status === 'degraded').length,
      unhealthy: allHealth.filter(h => h.status === 'unhealthy').length,
      unknown: allHealth.filter(h => h.status === 'unknown').length,
      averageResponseTime: allHealth.reduce((sum, h) => sum + h.responseTime, 0) / allHealth.length,
      averageUptime: allHealth.reduce((sum, h) => sum + h.uptime, 0) / allHealth.length,
      lastUpdate: new Date(),
    };
    
    return {
      summary,
      integrations: allHealth,
    };
  }

  // Method to refresh a specific integration's health
  async refreshIntegrationHealth(integrationId: string): Promise<IntegrationHealth> {
    this.healthCache.delete(integrationId);
    return this.getIntegrationHealth(integrationId);
  }

  // Method to refresh all integrations' health
  async refreshAllIntegrationsHealth(): Promise<IntegrationHealth[]> {
    this.healthCache.clear();
    return this.getAllIntegrationsHealth();
  }
}