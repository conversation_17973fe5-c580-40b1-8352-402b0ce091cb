import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { SystemEvent, SystemMetrics } from '../interfaces/system-metrics.interface';

@Injectable()
export class SystemEventsService {
  private readonly logger = new Logger(SystemEventsService.name);
  private events: SystemEvent[] = [];
  private readonly MAX_EVENTS = 1000;
  private eventIdCounter = 1;

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.initializeDefaultEvents();
  }

  private initializeDefaultEvents() {
    // Add some initial events for demonstration
    const initialEvents: Omit<SystemEvent, 'id'>[] = [
      {
        timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        type: 'success',
        category: 'system',
        title: 'System Monitoring Started',
        message: 'Real-time system monitoring has been successfully initialized',
        source: 'system-monitoring',
      },
      {
        timestamp: new Date(Date.now() - 3 * 60 * 1000), // 3 minutes ago
        type: 'info',
        category: 'integration',
        title: 'Health Check Completed',
        message: 'All integration health checks completed successfully',
        source: 'integration-health',
      },
      {
        timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
        type: 'success',
        category: 'docker',
        title: 'Container Status Update',
        message: 'All Docker containers are running normally',
        source: 'docker-monitor',
      },
    ];

    initialEvents.forEach(event => this.addEvent(event));
  }

  addEvent(eventData: Omit<SystemEvent, 'id'>): SystemEvent {
    const event: SystemEvent = {
      id: `event_${this.eventIdCounter++}`,
      ...eventData,
    };

    this.events.unshift(event); // Add to beginning for latest-first order

    // Maintain maximum event count
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(0, this.MAX_EVENTS);
    }

    // Emit event for real-time updates
    this.eventEmitter.emit('system.event.new', event);

    this.logger.debug(`New system event: ${event.type} - ${event.title}`);
    return event;
  }

  getEvents(options: {
    limit?: number;
    type?: SystemEvent['type'];
    category?: SystemEvent['category'];
    since?: Date;
    acknowledged?: boolean;
  } = {}): SystemEvent[] {
    let filteredEvents = [...this.events];

    // Apply filters
    if (options.type) {
      filteredEvents = filteredEvents.filter(event => event.type === options.type);
    }

    if (options.category) {
      filteredEvents = filteredEvents.filter(event => event.category === options.category);
    }

    if (options.since) {
      filteredEvents = filteredEvents.filter(event => event.timestamp >= options.since);
    }

    if (options.acknowledged !== undefined) {
      filteredEvents = filteredEvents.filter(event => 
        options.acknowledged ? event.acknowledged === true : !event.acknowledged
      );
    }

    // Apply limit
    if (options.limit && options.limit > 0) {
      filteredEvents = filteredEvents.slice(0, options.limit);
    }

    return filteredEvents;
  }

  acknowledgeEvent(eventId: string, acknowledgedBy: string): boolean {
    const event = this.events.find(e => e.id === eventId);
    if (!event) {
      return false;
    }

    event.acknowledged = true;
    event.acknowledgedBy = acknowledgedBy;
    event.acknowledgedAt = new Date();

    this.eventEmitter.emit('system.event.acknowledged', event);
    return true;
  }

  getEventsSummary() {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastHour = new Date(now.getTime() - 60 * 60 * 1000);

    const last24HoursEvents = this.events.filter(e => e.timestamp >= last24Hours);
    const lastHourEvents = this.events.filter(e => e.timestamp >= lastHour);

    return {
      total: this.events.length,
      last24Hours: {
        total: last24HoursEvents.length,
        error: last24HoursEvents.filter(e => e.type === 'error').length,
        warning: last24HoursEvents.filter(e => e.type === 'warning').length,
        success: last24HoursEvents.filter(e => e.type === 'success').length,
        info: last24HoursEvents.filter(e => e.type === 'info').length,
      },
      lastHour: {
        total: lastHourEvents.length,
        error: lastHourEvents.filter(e => e.type === 'error').length,
        warning: lastHourEvents.filter(e => e.type === 'warning').length,
        success: lastHourEvents.filter(e => e.type === 'success').length,
        info: lastHourEvents.filter(e => e.type === 'info').length,
      },
      unacknowledged: this.events.filter(e => !e.acknowledged).length,
    };
  }

  // Event handlers for system metrics changes
  @OnEvent('system.metrics.threshold.exceeded')
  handleMetricsThresholdExceeded(data: { metric: string; value: number; threshold: number }) {
    this.addEvent({
      timestamp: new Date(),
      type: 'warning',
      category: 'system',
      title: `${data.metric} Threshold Exceeded`,
      message: `${data.metric} is at ${data.value}%, exceeding threshold of ${data.threshold}%`,
      source: 'metrics-monitor',
      metadata: data,
    });
  }

  @OnEvent('system.service.status.changed')
  handleServiceStatusChanged(data: { service: string; status: string; previousStatus: string }) {
    const type = data.status === 'running' ? 'success' : 
                 data.status === 'error' ? 'error' : 'warning';

    this.addEvent({
      timestamp: new Date(),
      type,
      category: 'service',
      title: `Service Status Changed`,
      message: `Service ${data.service} changed from ${data.previousStatus} to ${data.status}`,
      source: 'service-monitor',
      metadata: data,
    });
  }

  @OnEvent('docker.container.status.changed')
  handleDockerContainerStatusChanged(data: { container: string; status: string; previousStatus: string }) {
    const type = data.status === 'running' ? 'success' : 
                 data.status === 'exited' ? 'error' : 'info';

    this.addEvent({
      timestamp: new Date(),
      type,
      category: 'docker',
      title: `Container Status Changed`,
      message: `Container ${data.container} changed from ${data.previousStatus} to ${data.status}`,
      source: 'docker-monitor',
      metadata: data,
    });
  }

  @OnEvent('integration.health.changed')
  handleIntegrationHealthChanged(data: { integration: string; status: string; previousStatus: string }) {
    const type = data.status === 'healthy' ? 'success' : 
                 data.status === 'unhealthy' ? 'error' : 'warning';

    this.addEvent({
      timestamp: new Date(),
      type,
      category: 'integration',
      title: `Integration Health Changed`,
      message: `Integration ${data.integration} health changed from ${data.previousStatus} to ${data.status}`,
      source: 'integration-monitor',
      metadata: data,
    });
  }

  @OnEvent('database.connection.issue')
  handleDatabaseConnectionIssue(data: { database: string; error: string }) {
    this.addEvent({
      timestamp: new Date(),
      type: 'error',
      category: 'database',
      title: `Database Connection Issue`,
      message: `Failed to connect to ${data.database}: ${data.error}`,
      source: 'database-monitor',
      metadata: data,
    });
  }

  // Method to simulate events for demonstration
  simulateEvents() {
    const eventTypes: SystemEvent['type'][] = ['info', 'warning', 'error', 'success'];
    const categories: SystemEvent['category'][] = ['system', 'docker', 'database', 'integration', 'service'];
    
    const randomType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    
    const sampleMessages = {
      info: [
        'System backup completed successfully',
        'New deployment detected',
        'Configuration updated',
        'Scheduled maintenance starting',
      ],
      warning: [
        'High memory usage detected',
        'Slow query performance',
        'Certificate expiring soon',
        'Disk space running low',
      ],
      error: [
        'Service connection failed',
        'Database query timeout',
        'Authentication error',
        'Critical system error detected',
      ],
      success: [
        'All services healthy',
        'Backup completed successfully',
        'Performance optimization applied',
        'Security scan passed',
      ],
    };

    const messages = sampleMessages[randomType];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    this.addEvent({
      timestamp: new Date(),
      type: randomType,
      category: randomCategory,
      title: `${randomCategory.charAt(0).toUpperCase() + randomCategory.slice(1)} Event`,
      message: randomMessage,
      source: `${randomCategory}-monitor`,
    });
  }

  // Method to clear old events
  clearOldEvents(olderThanDays: number = 30) {
    const cutoff = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
    const initialCount = this.events.length;
    
    this.events = this.events.filter(event => event.timestamp >= cutoff);
    
    const removedCount = initialCount - this.events.length;
    if (removedCount > 0) {
      this.logger.log(`Cleared ${removedCount} events older than ${olderThanDays} days`);
    }
  }
}