import { Injectable, Logger } from '@nestjs/common';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as os from 'os';
import * as fs from 'fs/promises';
import * as path from 'path';
import {
  SystemMetrics,
  CPUMetrics,
  MemoryMetrics,
  DiskMetrics,
  NetworkMetrics,
  ServiceMetrics,
  DockerMetrics,
  DatabaseMetrics,
  DiskPartition,
  NetworkInterface,
  ServiceStatus,
  DockerContainer,
  ContainerMetrics,
} from '../interfaces/system-metrics.interface';

const execAsync = promisify(exec);

@Injectable()
export class SystemMetricsService {
  private readonly logger = new Logger(SystemMetricsService.name);
  private metricsCache: SystemMetrics | null = null;
  private lastUpdate = 0;
  private readonly CACHE_DURATION = 5000; // 5 seconds

  async getSystemMetrics(useCache = true): Promise<SystemMetrics> {
    const now = Date.now();
    
    if (useCache && this.metricsCache && (now - this.lastUpdate) < this.CACHE_DURATION) {
      return this.metricsCache;
    }

    try {
      const [cpu, memory, disk, network, services, docker, database] = await Promise.all([
        this.getCPUMetrics(),
        this.getMemoryMetrics(),
        this.getDiskMetrics(),
        this.getNetworkMetrics(),
        this.getServiceMetrics(),
        this.getDockerMetrics(),
        this.getDatabaseMetrics(),
      ]);

      const metrics: SystemMetrics = {
        timestamp: new Date(),
        cpu,
        memory,
        disk,
        network,
        services,
        docker,
        database,
      };

      this.metricsCache = metrics;
      this.lastUpdate = now;
      
      return metrics;
    } catch (error) {
      this.logger.error('Failed to collect system metrics:', error);
      throw error;
    }
  }

  private async getCPUMetrics(): Promise<CPUMetrics> {
    try {
      const cpus = os.cpus();
      const loadAvg = os.loadavg();
      
      // Calculate CPU usage
      let totalIdle = 0;
      let totalTick = 0;
      
      cpus.forEach(cpu => {
        for (const type in cpu.times) {
          totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
      });
      
      const idle = totalIdle / cpus.length;
      const total = totalTick / cpus.length;
      const usage = 100 - Math.round(100 * idle / total);

      // Try to get CPU temperature (Linux only)
      let temperature: number | undefined;
      try {
        if (process.platform === 'linux') {
          const tempData = await fs.readFile('/sys/class/thermal/thermal_zone0/temp', 'utf8');
          temperature = parseInt(tempData.trim()) / 1000; // Convert from millidegrees
        }
      } catch {
        // Temperature not available
      }

      return {
        usage: Math.max(0, Math.min(100, usage)),
        cores: cpus.length,
        load: loadAvg,
        temperature,
      };
    } catch (error) {
      this.logger.error('Failed to get CPU metrics:', error);
      return {
        usage: 0,
        cores: os.cpus().length,
        load: [0, 0, 0],
      };
    }
  }

  private async getMemoryMetrics(): Promise<MemoryMetrics> {
    try {
      const total = os.totalmem();
      const free = os.freemem();
      const used = total - free;
      const percentage = Math.round((used / total) * 100);

      let swap: { total: number; used: number } | undefined;
      
      // Try to get swap information (Linux/macOS)
      if (process.platform === 'linux') {
        try {
          const { stdout } = await execAsync('free -b | grep Swap');
          const swapLine = stdout.trim().split(/\s+/);
          if (swapLine.length >= 3) {
            swap = {
              total: parseInt(swapLine[1]) || 0,
              used: parseInt(swapLine[2]) || 0,
            };
          }
        } catch {
          // Swap info not available
        }
      }

      return {
        total,
        used,
        available: free,
        percentage,
        swap,
      };
    } catch (error) {
      this.logger.error('Failed to get memory metrics:', error);
      const total = os.totalmem();
      return {
        total,
        used: 0,
        available: total,
        percentage: 0,
      };
    }
  }

  private async getDiskMetrics(): Promise<DiskMetrics> {
    try {
      const partitions: DiskPartition[] = [];
      let totalSize = 0;
      let totalUsed = 0;

      if (process.platform === 'win32') {
        // Windows
        try {
          const { stdout } = await execAsync('wmic logicaldisk get size,freespace,caption /format:csv');
          const lines = stdout.split('\n').filter(line => line.includes(','));
          
          for (const line of lines) {
            const parts = line.split(',');
            if (parts.length >= 4) {
              const device = parts[1];
              const freeSpace = parseInt(parts[2]) || 0;
              const size = parseInt(parts[3]) || 0;
              const used = size - freeSpace;
              
              if (size > 0) {
                partitions.push({
                  device,
                  mountpoint: device,
                  total: size,
                  used,
                  available: freeSpace,
                  percentage: Math.round((used / size) * 100),
                });
                totalSize += size;
                totalUsed += used;
              }
            }
          }
        } catch (error) {
          this.logger.warn('Failed to get Windows disk info:', error);
        }
      } else {
        // Unix-like systems
        try {
          const { stdout } = await execAsync('df -B1 2>/dev/null || df -k');
          const lines = stdout.split('\n').slice(1);
          
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 6 && !parts[5].startsWith('/dev/loop') && !parts[5].startsWith('/snap')) {
              const device = parts[0];
              const mountpoint = parts[5];
              let total: number, used: number, available: number;
              
              if (stdout.includes('-B1')) {
                // df -B1 output (bytes)
                total = parseInt(parts[1]) || 0;
                used = parseInt(parts[2]) || 0;
                available = parseInt(parts[3]) || 0;
              } else {
                // df -k output (kilobytes)
                total = (parseInt(parts[1]) || 0) * 1024;
                used = (parseInt(parts[2]) || 0) * 1024;
                available = (parseInt(parts[3]) || 0) * 1024;
              }
              
              if (total > 0) {
                partitions.push({
                  device,
                  mountpoint,
                  total,
                  used,
                  available,
                  percentage: Math.round((used / total) * 100),
                });
                totalSize += total;
                totalUsed += used;
              }
            }
          }
        } catch (error) {
          this.logger.warn('Failed to get Unix disk info:', error);
        }
      }

      return {
        total: totalSize,
        used: totalUsed,
        available: totalSize - totalUsed,
        percentage: totalSize > 0 ? Math.round((totalUsed / totalSize) * 100) : 0,
        partitions,
      };
    } catch (error) {
      this.logger.error('Failed to get disk metrics:', error);
      return {
        total: 0,
        used: 0,
        available: 0,
        percentage: 0,
        partitions: [],
      };
    }
  }

  private async getNetworkMetrics(): Promise<NetworkMetrics> {
    try {
      const interfaces: NetworkInterface[] = [];
      const networkInterfaces = os.networkInterfaces();
      let totalBytesIn = 0;
      let totalBytesOut = 0;
      let packetsIn = 0;
      let packetsOut = 0;

      // Get network statistics
      if (process.platform === 'linux') {
        try {
          const netData = await fs.readFile('/proc/net/dev', 'utf8');
          const lines = netData.split('\n').slice(2);
          
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 17) {
              const name = parts[0].replace(':', '');
              const bytesIn = parseInt(parts[1]) || 0;
              const packetsInCount = parseInt(parts[2]) || 0;
              const bytesOut = parseInt(parts[9]) || 0;
              const packetsOutCount = parseInt(parts[10]) || 0;
              
              const netInterface = networkInterfaces[name];
              const isUp = netInterface && netInterface.some(iface => !iface.internal);
              
              interfaces.push({
                name,
                bytesIn,
                bytesOut,
                packetsIn: packetsInCount,
                packetsOut: packetsOutCount,
                isUp: isUp || false,
              });
              
              if (isUp) {
                totalBytesIn += bytesIn;
                totalBytesOut += bytesOut;
                packetsIn += packetsInCount;
                packetsOut += packetsOutCount;
              }
            }
          }
        } catch (error) {
          this.logger.warn('Failed to read Linux network stats:', error);
        }
      } else {
        // Fallback for other platforms
        for (const [name, addrs] of Object.entries(networkInterfaces)) {
          if (addrs) {
            const isUp = addrs.some(addr => !addr.internal);
            interfaces.push({
              name,
              bytesIn: 0,
              bytesOut: 0,
              packetsIn: 0,
              packetsOut: 0,
              isUp,
            });
          }
        }
      }

      return {
        interfaces,
        totalBytesIn,
        totalBytesOut,
        packetsIn,
        packetsOut,
      };
    } catch (error) {
      this.logger.error('Failed to get network metrics:', error);
      return {
        interfaces: [],
        totalBytesIn: 0,
        totalBytesOut: 0,
        packetsIn: 0,
        packetsOut: 0,
      };
    }
  }

  private async getServiceMetrics(): Promise<ServiceMetrics> {
    try {
      const services: ServiceStatus[] = [];
      let running = 0;
      let stopped = 0;
      let error = 0;

      // Try to get systemd services (Linux)
      if (process.platform === 'linux') {
        try {
          const { stdout } = await execAsync('systemctl list-units --type=service --no-pager --no-legend');
          const lines = stdout.split('\n').filter(line => line.trim());
          
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length >= 4) {
              const name = parts[0];
              const load = parts[1];
              const active = parts[2];
              const sub = parts[3];
              
              let status: ServiceStatus['status'] = 'stopped';
              if (active === 'active' && sub === 'running') {
                status = 'running';
                running++;
              } else if (active === 'failed') {
                status = 'error';
                error++;
              } else {
                stopped++;
              }
              
              services.push({
                name: name.replace('.service', ''),
                status,
              });
            }
          }
        } catch (error) {
          this.logger.warn('Failed to get systemd services:', error);
        }
      }

      // Add some common Node.js processes
      try {
        const { stdout } = await execAsync('ps aux | grep node || ps -ef | grep node');
        const nodeProcesses = stdout.split('\n')
          .filter(line => line.includes('node') && !line.includes('grep'))
          .slice(0, 10); // Limit to 10 processes
        
        for (const line of nodeProcesses) {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 11) {
            const pid = parseInt(parts[1]);
            const cpu = parseFloat(parts[2]) || 0;
            const memory = parseFloat(parts[3]) || 0;
            const command = parts.slice(10).join(' ');
            
            services.push({
              name: `node-${pid}`,
              status: 'running',
              pid,
              cpu,
              memory,
            });
            running++;
          }
        }
      } catch (error) {
        this.logger.warn('Failed to get Node.js processes:', error);
      }

      return {
        total: services.length,
        running,
        stopped,
        error,
        services,
      };
    } catch (error) {
      this.logger.error('Failed to get service metrics:', error);
      return {
        total: 0,
        running: 0,
        stopped: 0,
        error: 0,
        services: [],
      };
    }
  }

  private async getDockerMetrics(): Promise<DockerMetrics> {
    try {
      let containerList: DockerContainer[] = [];
      let containers: ContainerMetrics = {
        total: 0,
        running: 0,
        stopped: 0,
        paused: 0,
      };
      let images = 0;
      let volumes = 0;
      let networks = 0;

      // Check if Docker is available
      try {
        const { stdout: psOutput } = await execAsync('docker ps -a --format "{{.ID}}|{{.Names}}|{{.Image}}|{{.Status}}|{{.State}}|{{.CreatedAt}}|{{.Ports}}"');
        const containerLines = psOutput.split('\n').filter(line => line.trim());
        
        containers.total = containerLines.length;
        
        for (const line of containerLines) {
          const [id, name, image, status, state, created, ports] = line.split('|');
          
          let containerState: DockerContainer['state'] = 'stopped';
          if (state === 'running') {
            containerState = 'running';
            containers.running++;
          } else if (state === 'paused') {
            containerState = 'paused';
            containers.paused++;
          } else if (state === 'restarting') {
            containerState = 'restarting';
          } else if (state === 'removing') {
            containerState = 'removing';
          } else if (state === 'exited') {
            containerState = 'exited';
            containers.stopped++;
          } else {
            containers.stopped++;
          }
          
          const container: DockerContainer = {
            id: id?.substring(0, 12) || '',
            name: name || '',
            image: image || '',
            status: status || '',
            state: containerState,
            created: new Date(created || ''),
            ports: ports ? ports.split(',').map(p => p.trim()) : [],
          };

          // Try to get container stats if running
          if (containerState === 'running') {
            try {
              const { stdout: statsOutput } = await execAsync(`docker stats ${id} --no-stream --format "{{.CPUPerc}}|{{.MemUsage}}|{{.NetIO}}"`);
              const [cpuPerc, memUsage, netIO] = statsOutput.trim().split('|');
              
              if (cpuPerc && memUsage) {
                const cpu = parseFloat(cpuPerc.replace('%', '')) || 0;
                const memParts = memUsage.split(' / ');
                if (memParts.length === 2) {
                  const usage = this.parseSize(memParts[0]);
                  const limit = this.parseSize(memParts[1]);
                  
                  container.stats = {
                    cpu,
                    memory: {
                      usage,
                      limit,
                      percentage: limit > 0 ? Math.round((usage / limit) * 100) : 0,
                    },
                    network: {
                      rx: 0, // Docker stats format varies, simplifying for now
                      tx: 0,
                    },
                  };
                }
              }
            } catch (error) {
              // Stats not available for this container
            }
          }
          
          containerList.push(container);
        }

        // Get images count
        try {
          const { stdout: imagesOutput } = await execAsync('docker images -q | wc -l');
          images = parseInt(imagesOutput.trim()) || 0;
        } catch (error) {
          this.logger.warn('Failed to count Docker images:', error);
        }

        // Get volumes count
        try {
          const { stdout: volumesOutput } = await execAsync('docker volume ls -q | wc -l');
          volumes = parseInt(volumesOutput.trim()) || 0;
        } catch (error) {
          this.logger.warn('Failed to count Docker volumes:', error);
        }

        // Get networks count
        try {
          const { stdout: networksOutput } = await execAsync('docker network ls -q | wc -l');
          networks = parseInt(networksOutput.trim()) || 0;
        } catch (error) {
          this.logger.warn('Failed to count Docker networks:', error);
        }

      } catch (error) {
        this.logger.warn('Docker not available or failed to get container info:', error);
      }

      return {
        containers,
        images,
        volumes,
        networks,
        containerList,
      };
    } catch (error) {
      this.logger.error('Failed to get Docker metrics:', error);
      return {
        containers: {
          total: 0,
          running: 0,
          stopped: 0,
          paused: 0,
        },
        images: 0,
        volumes: 0,
        networks: 0,
        containerList: [],
      };
    }
  }

  private async getDatabaseMetrics(): Promise<DatabaseMetrics> {
    // This would integrate with your actual database connections
    // For now, returning mock data structure that can be filled with real data
    try {
      return {
        connections: {
          active: Math.floor(Math.random() * 50) + 10,
          idle: Math.floor(Math.random() * 20) + 5,
          total: 0,
          max: 100,
        },
        queries: {
          total: Math.floor(Math.random() * 10000) + 5000,
          slow: Math.floor(Math.random() * 10),
          failed: Math.floor(Math.random() * 5),
          averageTime: Math.random() * 100 + 10,
        },
        size: {
          total: Math.floor(Math.random() * 1000000000) + 500000000, // ~500MB to 1.5GB
          data: 0,
          index: 0,
        },
        uptime: Math.floor(Math.random() * 86400 * 30), // Up to 30 days
        performance: {
          cacheHitRatio: Math.random() * 20 + 80, // 80-100%
          locksWaiting: Math.floor(Math.random() * 10),
        },
      };
    } catch (error) {
      this.logger.error('Failed to get database metrics:', error);
      return {
        connections: { active: 0, idle: 0, total: 0, max: 0 },
        queries: { total: 0, slow: 0, failed: 0, averageTime: 0 },
        size: { total: 0, data: 0, index: 0 },
        uptime: 0,
        performance: { cacheHitRatio: 0, locksWaiting: 0 },
      };
    }
  }

  private parseSize(sizeStr: string): number {
    const match = sizeStr.match(/^([\d.]+)\s*([KMGT]?i?B?)$/);
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();
    
    switch (unit) {
      case 'KB': case 'KIB': return value * 1024;
      case 'MB': case 'MIB': return value * 1024 * 1024;
      case 'GB': case 'GIB': return value * 1024 * 1024 * 1024;
      case 'TB': case 'TIB': return value * 1024 * 1024 * 1024 * 1024;
      default: return value;
    }
  }
}