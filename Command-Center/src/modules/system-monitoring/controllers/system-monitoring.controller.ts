import {
  Controller,
  Get,
  Post,
  Query,
  Param,
  Body,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { SystemMetricsService } from '../services/system-metrics.service';
import { IntegrationHealthService } from '../services/integration-health.service';
import { SystemEventsService } from '../services/system-events.service';
import { MonitoringGateway } from '../gateway/monitoring.gateway';

@ApiTags('System Monitoring')
@Controller('api/system-monitoring')
export class SystemMonitoringController {
  private readonly logger = new Logger(SystemMonitoringController.name);

  constructor(
    private readonly systemMetricsService: SystemMetricsService,
    private readonly integrationHealthService: IntegrationHealthService,
    private readonly systemEventsService: SystemEventsService,
    private readonly monitoringGateway: MonitoringGateway,
  ) {}

  /**
   * Get current system metrics
   */
  @Get('metrics')
  @ApiOperation({ summary: 'Get current system metrics' })
  @ApiResponse({ status: 200, description: 'System metrics retrieved successfully' })
  @ApiQuery({ name: 'useCache', required: false, type: Boolean, description: 'Use cached metrics if available' })
  async getSystemMetrics(@Query('useCache') useCache: boolean = true) {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics(useCache);
      return {
        success: true,
        data: metrics,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get system metrics:', error);
      return {
        success: false,
        error: 'Failed to retrieve system metrics',
        timestamp: new Date(),
      };
    }
  }

  /**
   * Get CPU metrics only
   */
  @Get('metrics/cpu')
  @ApiOperation({ summary: 'Get CPU metrics' })
  @ApiResponse({ status: 200, description: 'CPU metrics retrieved successfully' })
  async getCPUMetrics() {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      return {
        success: true,
        data: metrics.cpu,
        timestamp: metrics.timestamp,
      };
    } catch (error) {
      this.logger.error('Failed to get CPU metrics:', error);
      return {
        success: false,
        error: 'Failed to retrieve CPU metrics',
      };
    }
  }

  /**
   * Get memory metrics only
   */
  @Get('metrics/memory')
  @ApiOperation({ summary: 'Get memory metrics' })
  @ApiResponse({ status: 200, description: 'Memory metrics retrieved successfully' })
  async getMemoryMetrics() {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      return {
        success: true,
        data: metrics.memory,
        timestamp: metrics.timestamp,
      };
    } catch (error) {
      this.logger.error('Failed to get memory metrics:', error);
      return {
        success: false,
        error: 'Failed to retrieve memory metrics',
      };
    }
  }

  /**
   * Get disk metrics only
   */
  @Get('metrics/disk')
  @ApiOperation({ summary: 'Get disk metrics' })
  @ApiResponse({ status: 200, description: 'Disk metrics retrieved successfully' })
  async getDiskMetrics() {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      return {
        success: true,
        data: metrics.disk,
        timestamp: metrics.timestamp,
      };
    } catch (error) {
      this.logger.error('Failed to get disk metrics:', error);
      return {
        success: false,
        error: 'Failed to retrieve disk metrics',
      };
    }
  }

  /**
   * Get network metrics only
   */
  @Get('metrics/network')
  @ApiOperation({ summary: 'Get network metrics' })
  @ApiResponse({ status: 200, description: 'Network metrics retrieved successfully' })
  async getNetworkMetrics() {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      return {
        success: true,
        data: metrics.network,
        timestamp: metrics.timestamp,
      };
    } catch (error) {
      this.logger.error('Failed to get network metrics:', error);
      return {
        success: false,
        error: 'Failed to retrieve network metrics',
      };
    }
  }

  /**
   * Get Docker metrics only
   */
  @Get('metrics/docker')
  @ApiOperation({ summary: 'Get Docker metrics' })
  @ApiResponse({ status: 200, description: 'Docker metrics retrieved successfully' })
  async getDockerMetrics() {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      return {
        success: true,
        data: metrics.docker,
        timestamp: metrics.timestamp,
      };
    } catch (error) {
      this.logger.error('Failed to get Docker metrics:', error);
      return {
        success: false,
        error: 'Failed to retrieve Docker metrics',
      };
    }
  }

  /**
   * Get database metrics only
   */
  @Get('metrics/database')
  @ApiOperation({ summary: 'Get database metrics' })
  @ApiResponse({ status: 200, description: 'Database metrics retrieved successfully' })
  async getDatabaseMetrics() {
    try {
      const metrics = await this.systemMetricsService.getSystemMetrics();
      return {
        success: true,
        data: metrics.database,
        timestamp: metrics.timestamp,
      };
    } catch (error) {
      this.logger.error('Failed to get database metrics:', error);
      return {
        success: false,
        error: 'Failed to retrieve database metrics',
      };
    }
  }

  /**
   * Get all integrations health status
   */
  @Get('integrations')
  @ApiOperation({ summary: 'Get all integrations health status' })
  @ApiResponse({ status: 200, description: 'Integration health status retrieved successfully' })
  async getAllIntegrationsHealth() {
    try {
      const health = await this.integrationHealthService.getIntegrationSummary();
      return {
        success: true,
        data: health,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get integrations health:', error);
      return {
        success: false,
        error: 'Failed to retrieve integrations health',
      };
    }
  }

  /**
   * Get specific integration health status
   */
  @Get('integrations/:id')
  @ApiOperation({ summary: 'Get specific integration health status' })
  @ApiParam({ name: 'id', description: 'Integration ID' })
  @ApiResponse({ status: 200, description: 'Integration health status retrieved successfully' })
  async getIntegrationHealth(@Param('id') integrationId: string) {
    try {
      const health = await this.integrationHealthService.getIntegrationHealth(integrationId);
      return {
        success: true,
        data: health,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get integration health for ${integrationId}:`, error);
      return {
        success: false,
        error: `Failed to retrieve health for integration: ${integrationId}`,
      };
    }
  }

  /**
   * Refresh specific integration health
   */
  @Post('integrations/:id/refresh')
  @ApiOperation({ summary: 'Refresh specific integration health' })
  @ApiParam({ name: 'id', description: 'Integration ID' })
  @ApiResponse({ status: 200, description: 'Integration health refreshed successfully' })
  async refreshIntegrationHealth(@Param('id') integrationId: string) {
    try {
      const health = await this.integrationHealthService.refreshIntegrationHealth(integrationId);
      return {
        success: true,
        data: health,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to refresh integration health for ${integrationId}:`, error);
      return {
        success: false,
        error: `Failed to refresh health for integration: ${integrationId}`,
      };
    }
  }

  /**
   * Refresh all integrations health
   */
  @Post('integrations/refresh')
  @ApiOperation({ summary: 'Refresh all integrations health' })
  @ApiResponse({ status: 200, description: 'All integrations health refreshed successfully' })
  async refreshAllIntegrationsHealth() {
    try {
      const health = await this.integrationHealthService.refreshAllIntegrationsHealth();
      return {
        success: true,
        data: health,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to refresh all integrations health:', error);
      return {
        success: false,
        error: 'Failed to refresh all integrations health',
      };
    }
  }

  /**
   * Get system events
   */
  @Get('events')
  @ApiOperation({ summary: 'Get system events' })
  @ApiResponse({ status: 200, description: 'System events retrieved successfully' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Maximum number of events to return' })
  @ApiQuery({ name: 'type', required: false, type: String, description: 'Filter by event type' })
  @ApiQuery({ name: 'category', required: false, type: String, description: 'Filter by event category' })
  @ApiQuery({ name: 'since', required: false, type: String, description: 'Return events since this timestamp' })
  @ApiQuery({ name: 'acknowledged', required: false, type: Boolean, description: 'Filter by acknowledgment status' })
  async getSystemEvents(
    @Query('limit') limit?: number,
    @Query('type') type?: string,
    @Query('category') category?: string,
    @Query('since') since?: string,
    @Query('acknowledged') acknowledged?: boolean,
  ) {
    try {
      const options: any = {};
      
      if (limit) options.limit = limit;
      if (type) options.type = type;
      if (category) options.category = category;
      if (since) options.since = new Date(since);
      if (acknowledged !== undefined) options.acknowledged = acknowledged;

      const events = this.systemEventsService.getEvents(options);
      const summary = this.systemEventsService.getEventsSummary();

      return {
        success: true,
        data: {
          events,
          summary,
        },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get system events:', error);
      return {
        success: false,
        error: 'Failed to retrieve system events',
      };
    }
  }

  /**
   * Acknowledge an event
   */
  @Post('events/:id/acknowledge')
  @ApiOperation({ summary: 'Acknowledge a system event' })
  @ApiParam({ name: 'id', description: 'Event ID' })
  @ApiResponse({ status: 200, description: 'Event acknowledged successfully' })
  async acknowledgeEvent(
    @Param('id') eventId: string,
    @Body() body: { acknowledgedBy: string },
  ) {
    try {
      const success = this.systemEventsService.acknowledgeEvent(eventId, body.acknowledgedBy);
      return {
        success,
        message: success ? 'Event acknowledged successfully' : 'Event not found',
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to acknowledge event:', error);
      return {
        success: false,
        error: 'Failed to acknowledge event',
      };
    }
  }

  /**
   * Get system monitoring dashboard summary
   */
  @Get('dashboard')
  @ApiOperation({ summary: 'Get system monitoring dashboard summary' })
  @ApiResponse({ status: 200, description: 'Dashboard summary retrieved successfully' })
  async getDashboardSummary() {
    try {
      const [metrics, integrations, eventsSummary] = await Promise.all([
        this.systemMetricsService.getSystemMetrics(),
        this.integrationHealthService.getIntegrationSummary(),
        this.systemEventsService.getEventsSummary(),
      ]);

      const summary = {
        system: {
          cpu: metrics.cpu.usage,
          memory: metrics.memory.percentage,
          disk: metrics.disk.percentage,
          services: {
            total: metrics.services.total,
            running: metrics.services.running,
            issues: metrics.services.error + metrics.services.stopped,
          },
          docker: {
            total: metrics.docker.containers.total,
            running: metrics.docker.containers.running,
            issues: metrics.docker.containers.stopped + metrics.docker.containers.paused,
          },
        },
        integrations: {
          total: integrations.summary.total,
          healthy: integrations.summary.healthy,
          issues: integrations.summary.degraded + integrations.summary.unhealthy,
          averageUptime: integrations.summary.averageUptime,
        },
        events: eventsSummary,
        timestamp: new Date(),
      };

      return {
        success: true,
        data: summary,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get dashboard summary:', error);
      return {
        success: false,
        error: 'Failed to retrieve dashboard summary',
      };
    }
  }

  /**
   * Get WebSocket connection status
   */
  @Get('websocket/status')
  @ApiOperation({ summary: 'Get WebSocket connection status' })
  @ApiResponse({ status: 200, description: 'WebSocket status retrieved successfully' })
  async getWebSocketStatus() {
    try {
      const status = this.monitoringGateway.getConnectionStatus();
      return {
        success: true,
        data: status,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get WebSocket status:', error);
      return {
        success: false,
        error: 'Failed to retrieve WebSocket status',
      };
    }
  }

  /**
   * Trigger a test event (for development/testing)
   */
  @Post('test/event')
  @ApiOperation({ summary: 'Trigger a test event' })
  @ApiResponse({ status: 200, description: 'Test event triggered successfully' })
  async triggerTestEvent() {
    try {
      this.monitoringGateway.triggerTestEvent();
      return {
        success: true,
        message: 'Test event triggered successfully',
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to trigger test event:', error);
      return {
        success: false,
        error: 'Failed to trigger test event',
      };
    }
  }

  /**
   * Health check endpoint for the monitoring system itself
   */
  @Get('health')
  @ApiOperation({ summary: 'Health check for monitoring system' })
  @ApiResponse({ status: 200, description: 'Monitoring system health status' })
  async healthCheck() {
    try {
      const [metricsHealth, integrationsHealth, eventsHealth] = await Promise.allSettled([
        this.systemMetricsService.getSystemMetrics(),
        this.integrationHealthService.getAllIntegrationsHealth(),
        this.systemEventsService.getEventsSummary(),
      ]);

      const status = {
        status: 'healthy',
        components: {
          metrics: metricsHealth.status === 'fulfilled' ? 'healthy' : 'unhealthy',
          integrations: integrationsHealth.status === 'fulfilled' ? 'healthy' : 'unhealthy',
          events: eventsHealth.status === 'fulfilled' ? 'healthy' : 'unhealthy',
          websocket: this.monitoringGateway.getConnectionStatus().metricsIntervalActive ? 'healthy' : 'warning',
        },
        timestamp: new Date(),
      };

      // Overall status is unhealthy if any component is unhealthy
      if (Object.values(status.components).includes('unhealthy')) {
        status.status = 'unhealthy';
      } else if (Object.values(status.components).includes('warning')) {
        status.status = 'warning';
      }

      return status;
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date(),
      };
    }
  }
}