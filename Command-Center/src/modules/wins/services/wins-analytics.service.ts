import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';

@Injectable()
export class WinsAnalyticsService {
  constructor(private prisma: PrismaService) {}

  async getDashboardStats(userId?: string, departmentId?: string) {
    // Simplified analytics implementation for MVP
    const where: any = {};
    if (userId) where.userId = userId;
    if (departmentId) where.user = { departmentId };

    const [
      totalSubmissions,
      thisWeekSubmissions,
      pendingReviews,
      recentSubmissions,
    ] = await Promise.all([
      this.prisma.weeklySubmission.count({ where }),
      this.prisma.weeklySubmission.count({
        where: {
          ...where,
          weekStartDate: {
            gte: this.getWeekStart(new Date()),
          },
        },
      }),
      this.prisma.weeklySubmission.count({
        where: {
          ...where,
          status: 'submitted',
        },
      }),
      this.prisma.weeklySubmission.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 5,
      }),
    ]);

    return {
      totalSubmissions,
      thisWeekSubmissions,
      pendingReviews,
      totalAchievements: 0, // Placeholder for MVP
      totalCostSavings: 0, // Placeholder for MVP
      topPerformers: [], // Placeholder for MVP
      recentSubmissions,
    };
  }

  async getSubmissionSummary(timeRange?: string) {
    // Simplified summary for MVP
    return {
      submissionTrends: [],
      achievementBreakdown: [],
      departmentComparison: [],
      costSavingsTrends: [],
    };
  }

  async getTopPerformers(limit: number = 10, timeRange?: string) {
    const submissions = await this.prisma.weeklySubmission.groupBy({
      by: ['userId'],
      _count: {
        userId: true,
      },
      orderBy: {
        _count: {
          userId: 'desc',
        },
      },
      take: limit,
    });

    // Get user details for top performers
    const userIds = submissions.map((s) => s.userId);
    const users = await this.prisma.user.findMany({
      where: {
        id: { in: userIds },
      },
      select: {
        id: true,
        name: true,
        email: true,
        department: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return submissions.map((submission) => {
      const user = users.find((u) => u.id === submission.userId);
      return {
        user,
        submissionCount: submission._count.userId,
        achievementCount: 0, // Placeholder
        totalCostSavings: 0, // Placeholder
        impactScore: submission._count.userId * 10, // Simple calculation
      };
    });
  }

  async getTeamProgress(departmentId: string, timeRange?: string) {
    const submissions = await this.prisma.weeklySubmission.findMany({
      where: {
        user: {
          departmentId,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    const department = await this.prisma.department.findUnique({
      where: { id: departmentId },
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            users: true,
          },
        },
      },
    });

    return {
      department,
      submissionRate:
        department?._count.users > 0
          ? submissions.length / department._count.users
          : 0,
      averageImpactScore: 0, // Placeholder
      totalCostSavings: 0, // Placeholder
      memberProgress: [], // Placeholder
    };
  }

  async getCostSavingsTrends(timeRange?: string) {
    // Simplified cost savings for MVP
    return {
      totalSavings: 0,
      monthlySavings: [],
      departmentBreakdown: [],
      initiativeTypes: [],
    };
  }

  private getWeekStart(date: Date): Date {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    return new Date(date.setDate(diff));
  }
}
