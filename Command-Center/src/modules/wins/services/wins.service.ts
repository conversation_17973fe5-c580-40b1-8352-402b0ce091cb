import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { WeeklySubmission } from '@prisma/client';
import {
  CreateSubmissionDto,
  UpdateSubmissionDto,
} from '../dto/submission.dto';
import { SubmissionFiltersDto } from '../dto/submission-filters.dto';
import { WinsAnalyticsService } from './wins-analytics.service';
import { ReportGeneratorService } from './report-generator.service';
import { AchievementService } from './achievement.service';

@Injectable()
export class WinsService {
  constructor(
    private prisma: PrismaService,
    private winsAnalytics: WinsAnalyticsService,
    private reportGenerator: ReportGeneratorService,
    private achievementService: AchievementService,
  ) {}

  // =============================================================================
  // WEEKLY SUBMISSION MANAGEMENT
  // =============================================================================

  async createSubmission(data: CreateSubmissionDto): Promise<WeeklySubmission> {
    const { userId, weekStartDate, weekEndDate } = data;

    // Validate week dates
    const startDate = new Date(weekStartDate);
    const endDate = new Date(weekEndDate);

    if (startDate >= endDate) {
      throw new BadRequestException('Week start date must be before end date');
    }

    const daysDifference = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
    );
    if (daysDifference !== 7) {
      throw new BadRequestException('Week must be exactly 7 days');
    }

    // Check for existing submission for the same week
    const existingSubmission = await this.prisma.weeklySubmission.findUnique({
      where: {
        userId_weekStartDate: {
          userId,
          weekStartDate: startDate,
        },
      },
    });

    if (existingSubmission) {
      throw new BadRequestException('Submission already exists for this week');
    }

    // Validate and process achievements
    const processedAchievements = await this.validateAndProcessAchievements(
      data.achievements || [],
    );

    // Validate and process cost initiatives
    const processedCostInitiatives =
      await this.validateAndProcessCostInitiatives(data.costInitiatives || []);

    const submission = await this.prisma.weeklySubmission.create({
      data: {
        userId,
        weekStartDate: startDate,
        weekEndDate: endDate,
        achievements: processedAchievements,
        recognitions: data.recognitions || [],
        costInitiatives: processedCostInitiatives,
        trainingIdeas: JSON.stringify(data.trainingIdeas || []),
        progressUpdates: JSON.stringify(data.progressUpdates || []),
        weeklyHighlight: data.weeklyHighlight,
        challenges: data.challenges,
        nextWeekFocus: data.nextWeekFocus,
        attachments: JSON.stringify(data.attachments || []),
        status: 'draft',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            department: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Log submission creation
    await this.prisma.analytics.create({
      data: {
        event: 'wins_submission_created',
        properties: {
          submissionId: submission.id,
          userId,
          weekStartDate,
          achievementCount: processedAchievements.length,
          costInitiativeCount: processedCostInitiatives.length,
          trainingIdeaCount: data.trainingIdeas?.length || 0,
        },
      },
    });

    return submission;
  }

  async getSubmissions(filters: SubmissionFiltersDto) {
    const {
      userId,
      departmentId,
      status,
      weekStartDate,
      weekEndDate,
      search,
      page = 1,
      limit = 10,
      sortBy = 'weekStartDate',
      sortOrder = 'desc',
    } = filters;

    const where: any = {};

    if (userId) where.userId = userId;
    if (status) where.status = status;
    if (search) {
      where.OR = [
        { weeklyHighlight: { contains: search, mode: 'insensitive' } },
        { challenges: { contains: search, mode: 'insensitive' } },
        { nextWeekFocus: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (departmentId) {
      where.user = {
        departmentId,
      };
    }

    if (weekStartDate) {
      where.weekStartDate = { gte: new Date(weekStartDate) };
    }

    if (weekEndDate) {
      where.weekEndDate = { lte: new Date(weekEndDate) };
    }

    const [submissions, total] = await Promise.all([
      this.prisma.weeklySubmission.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              department: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.weeklySubmission.count({ where }),
    ]);

    // Process submissions to include calculated metrics
    const processedSubmissions = submissions.map((submission) => ({
      ...submission,
      metrics: this.calculateSubmissionMetrics(submission),
    }));

    return {
      submissions: processedSubmissions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getSubmissionById(
    id: string,
  ): Promise<WeeklySubmission & { metrics?: any }> {
    const submission = await this.prisma.weeklySubmission.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            department: {
              select: {
                id: true,
                name: true,
              },
            },
            role: {
              select: {
                id: true,
                title: true,
                level: true,
              },
            },
          },
        },
      },
    });

    if (!submission) {
      throw new NotFoundException(`Submission with ID ${id} not found`);
    }

    return {
      ...submission,
      metrics: this.calculateSubmissionMetrics(submission),
    };
  }

  async updateSubmission(
    id: string,
    data: UpdateSubmissionDto,
  ): Promise<WeeklySubmission & { metrics?: any }> {
    const submission = await this.getSubmissionById(id);

    // Validate status transitions
    if (
      data.status &&
      !this.isValidStatusTransition(submission.status, data.status)
    ) {
      throw new BadRequestException(
        `Invalid status transition from ${submission.status} to ${data.status}`,
      );
    }

    // Process achievements if provided
    let processedAchievements = submission.achievements;
    if (data.achievements) {
      processedAchievements = await this.validateAndProcessAchievements(
        data.achievements,
      );
    }

    // Process cost initiatives if provided
    let processedCostInitiatives = submission.costInitiatives;
    if (data.costInitiatives) {
      processedCostInitiatives = await this.validateAndProcessCostInitiatives(
        data.costInitiatives,
      );
    }

    const updated = await this.prisma.weeklySubmission.update({
      where: { id },
      data: {
        ...(data.weeklyHighlight && { weeklyHighlight: data.weeklyHighlight }),
        ...(data.challenges && { challenges: data.challenges }),
        ...(data.nextWeekFocus && { nextWeekFocus: data.nextWeekFocus }),
        ...(data.status && { status: data.status }),
        ...(data.submissionDate && { submissionDate: data.submissionDate }),
        ...(data.achievements && { achievements: processedAchievements }),
        ...(data.costInitiatives && {
          costInitiatives: processedCostInitiatives,
        }),
        ...(data.trainingIdeas && {
          trainingIdeas: JSON.stringify(data.trainingIdeas),
        }),
        ...(data.progressUpdates && {
          progressUpdates: JSON.stringify(data.progressUpdates),
        }),
        ...(data.attachments && {
          attachments: JSON.stringify(data.attachments),
        }),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            department: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Log status change
    if (data.status && data.status !== submission.status) {
      await this.prisma.analytics.create({
        data: {
          event: 'wins_submission_status_changed',
          properties: {
            submissionId: id,
            userId: submission.userId,
            previousStatus: submission.status,
            newStatus: data.status,
            weekStartDate: submission.weekStartDate,
          },
        },
      });

      // Process achievements if submission is approved
      if (data.status === 'reviewed') {
        await this.achievementService.processApprovedSubmission(updated);
      }
    }

    return {
      ...updated,
      metrics: this.calculateSubmissionMetrics(updated),
    };
  }

  async deleteSubmission(id: string): Promise<void> {
    const submission = await this.getSubmissionById(id);

    if (submission.status === 'submitted' || submission.status === 'reviewed') {
      throw new BadRequestException(
        'Cannot delete submitted or reviewed submissions',
      );
    }

    await this.prisma.weeklySubmission.delete({
      where: { id },
    });

    // Log deletion
    await this.prisma.analytics.create({
      data: {
        event: 'wins_submission_deleted',
        properties: {
          submissionId: id,
          userId: submission.userId,
          weekStartDate: submission.weekStartDate,
          status: submission.status,
        },
      },
    });
  }

  // =============================================================================
  // SUBMISSION WORKFLOW
  // =============================================================================

  async submitForReview(id: string): Promise<WeeklySubmission> {
    const submission = await this.getSubmissionById(id);

    if (submission.status !== 'draft') {
      throw new BadRequestException(
        'Only draft submissions can be submitted for review',
      );
    }

    // Validate required fields
    if (
      !submission.achievements ||
      (submission.achievements as any[]).length === 0
    ) {
      throw new BadRequestException('At least one achievement is required');
    }

    return await this.updateSubmission(id, {
      status: 'submitted' as any,
      submissionDate: new Date(),
    });
  }

  async approveSubmission(
    id: string,
    reviewerId: string,
  ): Promise<WeeklySubmission> {
    const submission = await this.getSubmissionById(id);

    if (submission.status !== 'submitted') {
      throw new BadRequestException(
        'Only submitted submissions can be approved',
      );
    }

    const approved = await this.updateSubmission(id, {
      status: 'reviewed' as any,
    });

    // Log approval
    await this.prisma.analytics.create({
      data: {
        event: 'wins_submission_approved',
        properties: {
          submissionId: id,
          userId: submission.userId,
          reviewerId,
          weekStartDate: submission.weekStartDate,
          achievementCount: (submission.achievements as any[]).length,
        },
      },
    });

    return approved;
  }

  // =============================================================================
  // HELPER METHODS
  // =============================================================================

  private async validateAndProcessAchievements(
    achievements: any[],
  ): Promise<any[]> {
    return achievements.map((achievement) => ({
      ...achievement,
      id: achievement.id || this.generateId(),
      date: new Date(achievement.date),
      impact: achievement.impact || 'medium',
      metrics: achievement.metrics || '',
    }));
  }

  private async validateAndProcessCostInitiatives(
    costInitiatives: any[],
  ): Promise<any[]> {
    return costInitiatives.map((initiative) => ({
      ...initiative,
      id: initiative.id || this.generateId(),
      implementationDate: new Date(initiative.implementationDate),
      estimatedSavings: parseFloat(initiative.estimatedSavings.toString()),
      actualSavings: initiative.actualSavings
        ? parseFloat(initiative.actualSavings.toString())
        : null,
      status: initiative.status || 'proposed',
      roi: initiative.roi || null,
    }));
  }

  private calculateSubmissionMetrics(submission: WeeklySubmission): any {
    const achievements = (submission.achievements as any[]) || [];
    const costInitiatives = (submission.costInitiatives as any[]) || [];
    const trainingIdeas = (submission.trainingIdeas as any[]) || [];
    const progressUpdates = (submission.progressUpdates as any[]) || [];

    const totalCostSavings = costInitiatives.reduce(
      (sum, initiative) => sum + (initiative.estimatedSavings || 0),
      0,
    );

    const completedProgress = progressUpdates.filter(
      (update) => update.status === 'completed',
    ).length;

    const overallProgressPercent =
      progressUpdates.length > 0
        ? progressUpdates.reduce(
            (sum, update) => sum + (update.currentProgress || 0),
            0,
          ) / progressUpdates.length
        : 0;

    return {
      achievementCount: achievements.length,
      costInitiativeCount: costInitiatives.length,
      trainingIdeaCount: trainingIdeas.length,
      progressUpdateCount: progressUpdates.length,
      totalCostSavings,
      completedProgress,
      overallProgressPercent: Math.round(overallProgressPercent),
      impactScore: this.calculateImpactScore(
        achievements,
        costInitiatives,
        progressUpdates,
      ),
    };
  }

  private calculateImpactScore(
    achievements: any[],
    costInitiatives: any[],
    progressUpdates: any[],
  ): number {
    let score = 0;

    // Achievement impact
    achievements.forEach((achievement) => {
      switch (achievement.impact) {
        case 'high':
          score += 3;
          break;
        case 'medium':
          score += 2;
          break;
        case 'low':
          score += 1;
          break;
      }
    });

    // Cost savings impact
    const totalSavings = costInitiatives.reduce(
      (sum, initiative) => sum + (initiative.estimatedSavings || 0),
      0,
    );
    if (totalSavings > 10000) score += 5;
    else if (totalSavings > 5000) score += 3;
    else if (totalSavings > 1000) score += 2;
    else if (totalSavings > 0) score += 1;

    // Progress completion impact
    const completedCount = progressUpdates.filter(
      (update) => update.status === 'completed',
    ).length;
    score += completedCount * 2;

    return Math.min(score, 100); // Cap at 100
  }

  private isValidStatusTransition(
    currentStatus: string,
    newStatus: string,
  ): boolean {
    const validTransitions = {
      draft: ['submitted'],
      submitted: ['reviewed', 'draft'],
      reviewed: [],
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  // =============================================================================
  // ANALYTICS & REPORTING
  // =============================================================================

  async getDashboardStats(userId?: string, departmentId?: string) {
    return await this.winsAnalytics.getDashboardStats(userId, departmentId);
  }

  async getSubmissionSummary(timeRange?: string) {
    return await this.winsAnalytics.getSubmissionSummary(timeRange);
  }

  async getTopPerformers(limit: number = 10, timeRange?: string) {
    return await this.winsAnalytics.getTopPerformers(limit, timeRange);
  }

  async generateReport(type: string, parameters: any) {
    return await this.reportGenerator.generateReport(type, parameters);
  }

  async getTeamProgress(departmentId: string, timeRange?: string) {
    return await this.winsAnalytics.getTeamProgress(departmentId, timeRange);
  }

  async getCostSavingsTrends(timeRange?: string) {
    return await this.winsAnalytics.getCostSavingsTrends(timeRange);
  }
}
