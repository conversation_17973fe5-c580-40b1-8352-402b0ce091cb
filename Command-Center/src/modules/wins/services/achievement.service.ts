import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';

@Injectable()
export class AchievementService {
  constructor(private prisma: PrismaService) {}

  async processApprovedSubmission(submission: any) {
    // Process achievements when submission is approved
    const achievements = (submission.achievements as any[]) || [];

    // For MVP, we'll just log the achievement processing
    await this.prisma.analytics.create({
      data: {
        event: 'achievements_processed',
        properties: {
          submissionId: submission.id,
          userId: submission.userId,
          achievementCount: achievements.length,
          weekStartDate: submission.weekStartDate,
          totalImpactScore: this.calculateTotalImpactScore(achievements),
        },
      },
    });

    // Calculate and store achievement points/scores
    for (const achievement of achievements) {
      await this.processIndividualAchievement(
        submission.userId,
        achievement,
        submission.weekStartDate,
      );
    }

    return {
      processed: achievements.length,
      totalImpactScore: this.calculateTotalImpactScore(achievements),
    };
  }

  private async processIndividualAchievement(
    userId: string,
    achievement: any,
    weekDate: Date,
  ) {
    // Calculate achievement score based on impact
    const score = this.calculateAchievementScore(achievement);

    // For MVP, we'll store this in analytics for now
    await this.prisma.analytics.create({
      data: {
        event: 'individual_achievement_scored',
        properties: {
          userId,
          achievementId: achievement.id,
          title: achievement.title,
          impact: achievement.impact,
          score,
          weekDate,
        },
      },
    });

    return score;
  }

  private calculateAchievementScore(achievement: any): number {
    // Simple scoring system for MVP
    const impactScores = {
      low: 10,
      medium: 25,
      high: 50,
    };

    let baseScore = impactScores[achievement.impact] || 10;

    // Bonus points for measurable metrics
    if (achievement.metrics && achievement.metrics.trim().length > 0) {
      baseScore += 10;
    }

    // Bonus points for detailed descriptions
    if (achievement.description && achievement.description.length > 100) {
      baseScore += 5;
    }

    return baseScore;
  }

  private calculateTotalImpactScore(achievements: any[]): number {
    return achievements.reduce((total, achievement) => {
      return total + this.calculateAchievementScore(achievement);
    }, 0);
  }

  async getUserAchievementStats(userId: string, timeRange?: string) {
    // Get user achievement statistics
    const achievements = await this.prisma.analytics.findMany({
      where: {
        event: 'individual_achievement_scored',
        properties: {
          path: ['userId'],
          equals: userId,
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
    });

    const totalScore = achievements.reduce((sum, achievement) => {
      return sum + (achievement.properties as any).score || 0;
    }, 0);

    return {
      totalAchievements: achievements.length,
      totalScore,
      averageScore:
        achievements.length > 0 ? totalScore / achievements.length : 0,
      recentAchievements: achievements.slice(0, 5),
    };
  }

  async getDepartmentAchievementStats(
    departmentId: string,
    timeRange?: string,
  ) {
    // Get department-wide achievement statistics
    const users = await this.prisma.user.findMany({
      where: { departmentId },
      select: { id: true, name: true },
    });

    const userIds = users.map((u) => u.id);
    const achievements = await this.prisma.analytics.findMany({
      where: {
        event: 'individual_achievement_scored',
        properties: {
          path: ['userId'],
          in: userIds,
        },
      },
    });

    const totalScore = achievements.reduce((sum, achievement) => {
      return sum + (achievement.properties as any).score || 0;
    }, 0);

    return {
      departmentId,
      totalUsers: users.length,
      totalAchievements: achievements.length,
      totalScore,
      averageScorePerUser: users.length > 0 ? totalScore / users.length : 0,
      participationRate:
        users.length > 0
          ? (new Set(achievements.map((a) => (a.properties as any).userId))
              .size /
              users.length) *
            100
          : 0,
    };
  }
}
