import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsArray,
  IsEnum,
  IsDateString,
  IsNotEmpty,
  MaxLength,
  ValidateNested,
  IsUUID,
  IsNumber,
  Min,
  IsJSON,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export enum SubmissionStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  REVIEWED = 'reviewed',
}

export enum AchievementImpact {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

export enum CostInitiativeStatus {
  PROPOSED = 'proposed',
  APPROVED = 'approved',
  IMPLEMENTED = 'implemented',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export class AchievementDto {
  @ApiPropertyOptional({
    description: 'Achievement ID (auto-generated if not provided)',
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({
    description: 'Achievement title',
    example: 'Completed major project milestone',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: 'Detailed description of the achievement' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  description: string;

  @ApiProperty({ description: 'Date when achievement was completed' })
  @IsDateString()
  date: string;

  @ApiProperty({
    description: 'Impact level of the achievement',
    enum: AchievementImpact,
    example: AchievementImpact.HIGH,
  })
  @IsEnum(AchievementImpact)
  impact: AchievementImpact;

  @ApiPropertyOptional({ description: 'Quantifiable metrics or results' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  metrics?: string;

  @ApiPropertyOptional({ description: 'Category or type of achievement' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  category?: string;
}

export class CostInitiativeDto {
  @ApiPropertyOptional({
    description: 'Initiative ID (auto-generated if not provided)',
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({
    description: 'Cost initiative title',
    example: 'Process automation implementation',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: 'Detailed description of the cost initiative' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  description: string;

  @ApiProperty({ description: 'Implementation date' })
  @IsDateString()
  implementationDate: string;

  @ApiProperty({ description: 'Estimated cost savings amount', example: 5000 })
  @IsNumber()
  @Min(0)
  estimatedSavings: number;

  @ApiPropertyOptional({
    description: 'Actual cost savings achieved',
    example: 4500,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  actualSavings?: number;

  @ApiProperty({
    description: 'Current status of the initiative',
    enum: CostInitiativeStatus,
    example: CostInitiativeStatus.IMPLEMENTED,
  })
  @IsEnum(CostInitiativeStatus)
  status: CostInitiativeStatus;

  @ApiPropertyOptional({ description: 'Return on investment percentage' })
  @IsOptional()
  @IsNumber()
  roi?: number;

  @ApiPropertyOptional({ description: 'Category of cost savings' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  category?: string;
}

export class TrainingIdeaDto {
  @ApiProperty({
    description: 'Training idea title',
    example: 'Advanced Excel workshop',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: 'Detailed description of the training need' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  description: string;

  @ApiPropertyOptional({ description: 'Target audience or department' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  targetAudience?: string;

  @ApiPropertyOptional({ description: 'Expected benefits or outcomes' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  expectedBenefits?: string;

  @ApiPropertyOptional({ description: 'Priority level (1-5)' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  priority?: number;
}

export class ProgressUpdateDto {
  @ApiProperty({
    description: 'Project or task title',
    example: 'CRM system upgrade',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: 'Current progress description' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  description: string;

  @ApiProperty({ description: 'Progress percentage (0-100)', example: 75 })
  @IsNumber()
  @Min(0)
  currentProgress: number;

  @ApiPropertyOptional({ description: 'Status of the project' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  status?: string;

  @ApiPropertyOptional({ description: 'Expected completion date' })
  @IsOptional()
  @IsDateString()
  expectedCompletion?: string;

  @ApiPropertyOptional({ description: 'Any blockers or challenges' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  blockers?: string;
}

export class AttachmentDto {
  @ApiProperty({
    description: 'File name',
    example: 'achievement_screenshot.png',
  })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({ description: 'File URL or path' })
  @IsString()
  @IsNotEmpty()
  fileUrl: string;

  @ApiPropertyOptional({ description: 'File size in bytes' })
  @IsOptional()
  @IsNumber()
  fileSize?: number;

  @ApiPropertyOptional({ description: 'MIME type' })
  @IsOptional()
  @IsString()
  mimeType?: string;

  @ApiPropertyOptional({ description: 'Description of the attachment' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}

export class CreateSubmissionDto {
  @ApiPropertyOptional({
    description: 'User ID (defaults to authenticated user)',
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiProperty({
    description: 'Week start date (Monday)',
    example: '2024-01-15',
  })
  @IsDateString()
  weekStartDate: string;

  @ApiProperty({ description: 'Week end date (Sunday)', example: '2024-01-21' })
  @IsDateString()
  weekEndDate: string;

  @ApiProperty({
    description: 'List of achievements for the week',
    type: [AchievementDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AchievementDto)
  achievements: AchievementDto[];

  @ApiPropertyOptional({
    description: 'List of recognitions received',
    example: ['Praise from client', 'Team appreciation'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  recognitions?: string[];

  @ApiPropertyOptional({
    description: 'Cost-saving initiatives',
    type: [CostInitiativeDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CostInitiativeDto)
  costInitiatives?: CostInitiativeDto[];

  @ApiPropertyOptional({
    description: 'Training ideas and suggestions',
    type: [TrainingIdeaDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TrainingIdeaDto)
  trainingIdeas?: TrainingIdeaDto[];

  @ApiPropertyOptional({
    description: 'Progress updates on ongoing projects',
    type: [ProgressUpdateDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProgressUpdateDto)
  progressUpdates?: ProgressUpdateDto[];

  @ApiProperty({ description: 'Main highlight of the week' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  weeklyHighlight: string;

  @ApiPropertyOptional({ description: 'Challenges faced during the week' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  challenges?: string;

  @ApiPropertyOptional({ description: 'Focus areas for next week' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  nextWeekFocus?: string;

  @ApiPropertyOptional({
    description: 'File attachments',
    type: [AttachmentDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttachmentDto)
  attachments?: AttachmentDto[];
}

export class UpdateSubmissionDto extends PartialType(CreateSubmissionDto) {
  @ApiPropertyOptional({
    description: 'Submission status',
    enum: SubmissionStatus,
  })
  @IsOptional()
  @IsEnum(SubmissionStatus)
  status?: SubmissionStatus;

  @ApiPropertyOptional({
    description: 'Submission date (automatically set when submitted)',
  })
  @IsOptional()
  @IsDateString()
  submissionDate?: Date;
}

export class SubmissionResponseDto {
  @ApiProperty({ description: 'Submission unique identifier' })
  id: string;

  @ApiProperty({ description: 'User ID who created the submission' })
  userId: string;

  @ApiProperty({ description: 'Week start date' })
  weekStartDate: Date;

  @ApiProperty({ description: 'Week end date' })
  weekEndDate: Date;

  @ApiProperty({ description: 'List of achievements', type: [AchievementDto] })
  achievements: AchievementDto[];

  @ApiPropertyOptional({ description: 'List of recognitions' })
  recognitions?: string[];

  @ApiPropertyOptional({
    description: 'Cost-saving initiatives',
    type: [CostInitiativeDto],
  })
  costInitiatives?: CostInitiativeDto[];

  @ApiPropertyOptional({
    description: 'Training ideas',
    type: [TrainingIdeaDto],
  })
  trainingIdeas?: TrainingIdeaDto[];

  @ApiPropertyOptional({
    description: 'Progress updates',
    type: [ProgressUpdateDto],
  })
  progressUpdates?: ProgressUpdateDto[];

  @ApiProperty({ description: 'Main highlight of the week' })
  weeklyHighlight: string;

  @ApiPropertyOptional({ description: 'Challenges faced' })
  challenges?: string;

  @ApiPropertyOptional({ description: 'Next week focus' })
  nextWeekFocus?: string;

  @ApiPropertyOptional({
    description: 'File attachments',
    type: [AttachmentDto],
  })
  attachments?: AttachmentDto[];

  @ApiProperty({ description: 'Submission status', enum: SubmissionStatus })
  status: SubmissionStatus;

  @ApiPropertyOptional({ description: 'Submission date' })
  submissionDate?: Date;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'User information' })
  user?: {
    id: string;
    name: string;
    email: string;
    department?: {
      id: string;
      name: string;
    };
    role?: {
      id: string;
      title: string;
      level: number;
    };
  };

  @ApiPropertyOptional({ description: 'Calculated metrics for the submission' })
  metrics?: {
    achievementCount: number;
    costInitiativeCount: number;
    trainingIdeaCount: number;
    progressUpdateCount: number;
    totalCostSavings: number;
    completedProgress: number;
    overallProgressPercent: number;
    impactScore: number;
  };
}
