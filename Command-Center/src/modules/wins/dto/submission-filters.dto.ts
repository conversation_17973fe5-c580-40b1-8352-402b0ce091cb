import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsNumber,
  IsDateString,
  IsUUID,
  <PERSON>,
  Max,
  IsIn,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { SubmissionStatus } from './submission.dto';

export class SubmissionFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by user ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Filter by department ID',
    example: 'dept-1234',
  })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Filter by submission status',
    enum: SubmissionStatus,
    example: SubmissionStatus.SUBMITTED,
  })
  @IsOptional()
  @IsEnum(SubmissionStatus)
  status?: SubmissionStatus;

  @ApiPropertyOptional({
    description: 'Filter submissions from this week start date',
    example: '2024-01-15',
  })
  @IsOptional()
  @IsDateString()
  weekStartDate?: string;

  @ApiPropertyOptional({
    description: 'Filter submissions to this week end date',
    example: '2024-01-21',
  })
  @IsOptional()
  @IsDateString()
  weekEndDate?: string;

  @ApiPropertyOptional({
    description: 'Search term for highlights, challenges, or user name',
    example: 'project completion',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by minimum impact score',
    minimum: 0,
    maximum: 100,
    example: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  minImpactScore?: number;

  @ApiPropertyOptional({
    description: 'Filter by minimum cost savings',
    minimum: 0,
    example: 1000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minCostSavings?: number;

  @ApiPropertyOptional({
    description: 'Filter by minimum number of achievements',
    minimum: 0,
    example: 2,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minAchievements?: number;

  @ApiPropertyOptional({
    description: 'Filter submissions with attachments',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  hasAttachments?: boolean;

  @ApiPropertyOptional({
    description: 'Filter submissions with cost initiatives',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  hasCostInitiatives?: boolean;

  @ApiPropertyOptional({
    description: 'Filter submissions with training ideas',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  hasTrainingIdeas?: boolean;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: ['weekStartDate', 'createdAt', 'updatedAt', 'status', 'impactScore'],
    default: 'weekStartDate',
    example: 'weekStartDate',
  })
  @IsOptional()
  @IsString()
  @IsIn(['weekStartDate', 'createdAt', 'updatedAt', 'status', 'impactScore'])
  sortBy?: string = 'weekStartDate';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
    example: 'desc',
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';

  @ApiPropertyOptional({
    description: 'Include only recent submissions (days)',
    minimum: 1,
    maximum: 365,
    example: 30,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(365)
  recentDays?: number;

  @ApiPropertyOptional({
    description: 'Filter by submission quarter (1-4)',
    minimum: 1,
    maximum: 4,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(4)
  quarter?: number;

  @ApiPropertyOptional({
    description: 'Filter by submission year',
    minimum: 2020,
    maximum: 2030,
    example: 2024,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(2020)
  @Max(2030)
  year?: number;

  @ApiPropertyOptional({
    description: 'Include only submissions by top performers',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  topPerformersOnly?: boolean;
}
