import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { WinsService } from '../services/wins.service';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { PermissionsGuard } from '../../auth/guards/permissions.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { RequirePermissions } from '../../auth/decorators/permissions.decorator';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { User } from '../../users/entities/user.entity';
import {
  CreateSubmissionDto,
  UpdateSubmissionDto,
  SubmissionResponseDto,
} from '../dto/submission.dto';
import { SubmissionFiltersDto } from '../dto/submission-filters.dto';

@ApiTags('Wins of Week')
@Controller('wins')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class WinsController {
  constructor(private readonly winsService: WinsService) {}

  // =============================================================================
  // WEEKLY SUBMISSION MANAGEMENT
  // =============================================================================

  @Post('submissions')
  @RequirePermissions('wins:create')
  @ApiOperation({ summary: 'Create a new weekly submission' })
  @ApiResponse({
    status: 201,
    description: 'Weekly submission created successfully',
    type: SubmissionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid submission data' })
  @ApiResponse({
    status: 409,
    description: 'Submission already exists for this week',
  })
  async createSubmission(
    @Body(ValidationPipe) createSubmissionDto: CreateSubmissionDto,
    @GetUser() user: User,
  ): Promise<any> {
    // If userId is not provided, use the authenticated user's ID
    if (!createSubmissionDto.userId) {
      createSubmissionDto.userId = user.id;
    }

    return await this.winsService.createSubmission(createSubmissionDto);
  }

  @Get('submissions')
  @RequirePermissions('wins:read')
  @ApiOperation({ summary: 'Get weekly submissions with filters' })
  @ApiResponse({
    status: 200,
    description: 'Submissions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        submissions: {
          type: 'array',
          items: { $ref: '#/components/schemas/SubmissionResponseDto' },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    description: 'Filter by user ID',
  })
  @ApiQuery({
    name: 'departmentId',
    required: false,
    description: 'Filter by department ID',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by submission status',
  })
  @ApiQuery({
    name: 'weekStartDate',
    required: false,
    description: 'Filter from week start date',
  })
  @ApiQuery({
    name: 'weekEndDate',
    required: false,
    description: 'Filter to week end date',
  })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async getSubmissions(
    @Query() filters: SubmissionFiltersDto,
    @GetUser() user: User,
  ): Promise<any> {
    // Non-admin users can only see their own submissions or their team's
    if (
      !user?.roles?.some((role) => ['admin', 'manager', 'hr'].includes(role))
    ) {
      filters.userId = user.id;
    }

    return await this.winsService.getSubmissions(filters);
  }

  @Get('submissions/:id')
  @RequirePermissions('wins:read')
  @ApiOperation({ summary: 'Get weekly submission by ID' })
  @ApiParam({ name: 'id', description: 'Submission ID' })
  @ApiResponse({
    status: 200,
    description: 'Submission retrieved successfully',
    type: SubmissionResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Submission not found' })
  async getSubmissionById(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<any> {
    const submission = await this.winsService.getSubmissionById(id);

    // Users can only view their own submissions unless they're admin/manager
    if (
      submission.userId !== user.id &&
      !user?.roles?.some((role) =>
        ['admin', 'manager', 'hr'].includes(role.name),
      )
    ) {
      throw new Error('Access denied');
    }

    return submission;
  }

  @Put('submissions/:id')
  @RequirePermissions('wins:update')
  @ApiOperation({ summary: 'Update weekly submission' })
  @ApiParam({ name: 'id', description: 'Submission ID' })
  @ApiResponse({
    status: 200,
    description: 'Submission updated successfully',
    type: SubmissionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid submission data' })
  @ApiResponse({ status: 404, description: 'Submission not found' })
  async updateSubmission(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateSubmissionDto: UpdateSubmissionDto,
    @GetUser() user: User,
  ): Promise<any> {
    const submission = await this.winsService.getSubmissionById(id);

    // Users can only update their own submissions
    if (
      submission.userId !== user.id &&
      !user?.roles?.some((role) => ['admin', 'manager'].includes(role.name))
    ) {
      throw new Error('Access denied');
    }

    return await this.winsService.updateSubmission(id, updateSubmissionDto);
  }

  @Delete('submissions/:id')
  @RequirePermissions('wins:delete')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete weekly submission' })
  @ApiParam({ name: 'id', description: 'Submission ID' })
  @ApiResponse({ status: 204, description: 'Submission deleted successfully' })
  @ApiResponse({
    status: 400,
    description: 'Cannot delete submitted or reviewed submissions',
  })
  @ApiResponse({ status: 404, description: 'Submission not found' })
  async deleteSubmission(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<void> {
    const submission = await this.winsService.getSubmissionById(id);

    // Users can only delete their own submissions
    if (
      submission.userId !== user.id &&
      !user?.roles?.some((role) => ['admin', 'manager'].includes(role.name))
    ) {
      throw new Error('Access denied');
    }

    await this.winsService.deleteSubmission(id);
  }

  // =============================================================================
  // SUBMISSION WORKFLOW
  // =============================================================================

  @Post('submissions/:id/submit')
  @RequirePermissions('wins:update')
  @ApiOperation({ summary: 'Submit weekly submission for review' })
  @ApiParam({ name: 'id', description: 'Submission ID' })
  @ApiResponse({
    status: 200,
    description: 'Submission submitted for review successfully',
    type: SubmissionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Submission cannot be submitted' })
  async submitForReview(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<any> {
    const submission = await this.winsService.getSubmissionById(id);

    // Users can only submit their own submissions
    if (submission.userId !== user.id) {
      throw new Error('Access denied');
    }

    return await this.winsService.submitForReview(id);
  }

  @Post('submissions/:id/approve')
  @UseGuards(RolesGuard)
  @Roles('admin', 'manager', 'hr')
  @RequirePermissions('wins:approve')
  @ApiOperation({ summary: 'Approve weekly submission' })
  @ApiParam({ name: 'id', description: 'Submission ID' })
  @ApiResponse({
    status: 200,
    description: 'Submission approved successfully',
    type: SubmissionResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Submission cannot be approved' })
  async approveSubmission(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.winsService.approveSubmission(id, user.id);
  }

  // =============================================================================
  // DASHBOARD & ANALYTICS
  // =============================================================================

  @Get('dashboard')
  @RequirePermissions('wins:read')
  @ApiOperation({ summary: 'Get wins dashboard statistics' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalSubmissions: { type: 'number' },
        thisWeekSubmissions: { type: 'number' },
        pendingReviews: { type: 'number' },
        totalAchievements: { type: 'number' },
        totalCostSavings: { type: 'number' },
        topPerformers: { type: 'array' },
        recentSubmissions: { type: 'array' },
      },
    },
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    description: 'Filter by user ID',
  })
  @ApiQuery({
    name: 'departmentId',
    required: false,
    description: 'Filter by department ID',
  })
  async getDashboard(
    @Query('userId') userId?: string,
    @Query('departmentId') departmentId?: string,
    @GetUser() user?: User,
  ): Promise<any> {
    // Non-admin users can only see their own stats
    if (
      !user?.roles?.some((role) => ['admin', 'manager', 'hr'].includes(role))
    ) {
      userId = user.id;
    }

    return await this.winsService.getDashboardStats(userId, departmentId);
  }

  @Get('analytics/summary')
  @UseGuards(RolesGuard)
  @Roles('admin', 'manager', 'hr', 'analytics')
  @RequirePermissions('wins:analytics')
  @ApiOperation({ summary: 'Get submission summary analytics' })
  @ApiResponse({
    status: 200,
    description: 'Submission summary retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        submissionTrends: { type: 'array' },
        achievementBreakdown: { type: 'array' },
        departmentComparison: { type: 'array' },
        costSavingsTrends: { type: 'array' },
      },
    },
  })
  @ApiQuery({
    name: 'timeRange',
    required: false,
    description: 'Time range (30d, 90d, 1y)',
  })
  async getSubmissionSummary(
    @Query('timeRange') timeRange?: string,
  ): Promise<any> {
    return await this.winsService.getSubmissionSummary(timeRange);
  }

  @Get('analytics/top-performers')
  @RequirePermissions('wins:analytics')
  @ApiOperation({ summary: 'Get top performing employees' })
  @ApiResponse({
    status: 200,
    description: 'Top performers retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          user: { type: 'object' },
          submissionCount: { type: 'number' },
          achievementCount: { type: 'number' },
          totalCostSavings: { type: 'number' },
          impactScore: { type: 'number' },
        },
      },
    },
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of performers to return',
  })
  @ApiQuery({
    name: 'timeRange',
    required: false,
    description: 'Time range (30d, 90d, 1y)',
  })
  async getTopPerformers(
    @Query('limit') limit?: number,
    @Query('timeRange') timeRange?: string,
  ): Promise<any[]> {
    return await this.winsService.getTopPerformers(limit || 10, timeRange);
  }

  @Get('analytics/team-progress/:departmentId')
  @UseGuards(RolesGuard)
  @Roles('admin', 'manager', 'hr')
  @RequirePermissions('wins:analytics')
  @ApiOperation({ summary: 'Get team progress analytics' })
  @ApiParam({ name: 'departmentId', description: 'Department ID' })
  @ApiResponse({
    status: 200,
    description: 'Team progress retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        department: { type: 'object' },
        submissionRate: { type: 'number' },
        averageImpactScore: { type: 'number' },
        totalCostSavings: { type: 'number' },
        memberProgress: { type: 'array' },
      },
    },
  })
  @ApiQuery({
    name: 'timeRange',
    required: false,
    description: 'Time range (30d, 90d, 1y)',
  })
  async getTeamProgress(
    @Param('departmentId', ParseUUIDPipe) departmentId: string,
    @Query('timeRange') timeRange?: string,
  ): Promise<any> {
    return await this.winsService.getTeamProgress(departmentId, timeRange);
  }

  @Get('analytics/cost-savings')
  @UseGuards(RolesGuard)
  @Roles('admin', 'manager', 'finance', 'analytics')
  @RequirePermissions('wins:analytics')
  @ApiOperation({ summary: 'Get cost savings trends' })
  @ApiResponse({
    status: 200,
    description: 'Cost savings trends retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalSavings: { type: 'number' },
        monthlySavings: { type: 'array' },
        departmentBreakdown: { type: 'array' },
        initiativeTypes: { type: 'array' },
      },
    },
  })
  @ApiQuery({
    name: 'timeRange',
    required: false,
    description: 'Time range (30d, 90d, 1y)',
  })
  async getCostSavingsTrends(
    @Query('timeRange') timeRange?: string,
  ): Promise<any> {
    return await this.winsService.getCostSavingsTrends(timeRange);
  }

  // =============================================================================
  // REPORTING
  // =============================================================================

  @Post('reports/generate')
  @UseGuards(RolesGuard)
  @Roles('admin', 'manager', 'hr')
  @RequirePermissions('wins:reports')
  @ApiOperation({ summary: 'Generate wins report' })
  @ApiResponse({
    status: 200,
    description: 'Report generated successfully',
    schema: {
      type: 'object',
      properties: {
        reportId: { type: 'string' },
        reportType: { type: 'string' },
        generatedAt: { type: 'string', format: 'date-time' },
        downloadUrl: { type: 'string' },
        summary: { type: 'object' },
      },
    },
  })
  async generateReport(
    @Body()
    reportRequest: {
      type: 'weekly' | 'monthly' | 'quarterly' | 'annual';
      departmentId?: string;
      startDate?: string;
      endDate?: string;
      includeMetrics?: boolean;
      format?: 'pdf' | 'excel' | 'csv';
    },
    @GetUser() user: User,
  ): Promise<any> {
    return await this.winsService.generateReport(reportRequest.type, {
      ...reportRequest,
      generatedBy: user.id,
    });
  }

  @Get('my-submissions')
  @RequirePermissions('wins:read')
  @ApiOperation({ summary: 'Get current user submissions' })
  @ApiResponse({
    status: 200,
    description: 'User submissions retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        submissions: { type: 'array' },
        stats: {
          type: 'object',
          properties: {
            totalSubmissions: { type: 'number' },
            achievementCount: { type: 'number' },
            totalCostSavings: { type: 'number' },
            averageImpactScore: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of submissions to return',
  })
  async getMySubmissions(
    @Query('limit') limit?: number,
    @GetUser() user?: User,
  ): Promise<any> {
    const filters: SubmissionFiltersDto = {
      userId: user.id,
      limit: limit || 10,
      sortBy: 'weekStartDate',
      sortOrder: 'desc',
    };

    const result = await this.winsService.getSubmissions(filters);
    const stats = await this.winsService.getDashboardStats(user.id);

    return {
      submissions: result.submissions,
      pagination: result.pagination,
      stats,
    };
  }
}
