import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { WinsService } from './services/wins.service';
import { WinsAnalyticsService } from './services/wins-analytics.service';
import { WinsController } from './controllers/wins.controller';
import { ReportGeneratorService } from './services/report-generator.service';
import { AchievementService } from './services/achievement.service';

@Module({
  imports: [PrismaModule],
  controllers: [WinsController],
  providers: [
    WinsService,
    WinsAnalyticsService,
    ReportGeneratorService,
    AchievementService,
  ],
  exports: [
    WinsService,
    WinsAnalyticsService,
    ReportGeneratorService,
    AchievementService,
  ],
})
export class WinsModule {}
