import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  LAndDNotification,
  TrainingNotification,
  VendorNotification,
  WinsNotification,
  NotificationChannel,
  NotificationPriority,
  NotificationDelivery,
  DeliveryStatus,
  NotificationPreferences,
  NotificationTemplate,
  BulkNotificationRequest,
  CrossAppNotification,
  NotificationAnalytics,
  NotificationSummary,
  RealTimeNotificationData,
} from './notification.types';
import { NotificationQueueService } from './notification.queue';
import { LAndDApp } from '../l-and-d/websocket/websocket-event.types';

/**
 * Notification Service
 * Core service for managing notifications across L&D applications
 */
@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  // Notification storage: userId -> NotificationData[]
  private readonly userNotifications = new Map<string, LAndDNotification[]>();

  // Delivery tracking: notificationId -> NotificationDelivery[]
  private readonly deliveries = new Map<string, NotificationDelivery[]>();

  // User preferences: userId -> NotificationPreferences
  private readonly userPreferences = new Map<string, NotificationPreferences>();

  // Templates: templateId -> NotificationTemplate
  private readonly templates = new Map<string, NotificationTemplate>();

  // Analytics tracking
  private readonly analytics: NotificationAnalytics[] = [];

  // WebSocket service reference (will be injected by gateway)
  private webSocketService: any;

  constructor(
    private readonly queueService: NotificationQueueService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeDefaultTemplates();
  }

  /**
   * Set WebSocket service reference
   */
  setWebSocketService(webSocketService: any): void {
    this.webSocketService = webSocketService;
  }

  /**
   * Send real-time notification (immediate WebSocket delivery)
   */
  async sendRealTimeNotification(
    userId: string,
    notification: Omit<
      LAndDNotification,
      'id' | 'userId' | 'createdAt' | 'isRead'
    >,
  ): Promise<void> {
    // Create full notification
    const fullNotification: LAndDNotification = {
      id: this.generateNotificationId(),
      userId,
      createdAt: new Date(),
      isRead: false,
      ...notification,
    } as LAndDNotification;

    // Store notification
    this.storeNotification(fullNotification);

    // Check user preferences
    const preferences = this.getUserPreferences(userId);
    if (!this.shouldSendNotification(fullNotification, preferences)) {
      this.logger.debug(
        `Notification filtered by user preferences: ${fullNotification.id}`,
      );
      return;
    }

    // Create delivery record
    const delivery: NotificationDelivery = {
      id: this.generateDeliveryId(),
      notificationId: fullNotification.id,
      userId,
      channel: 'websocket',
      status: 'pending',
      attempts: 0,
    };

    try {
      // Send via WebSocket if user is connected
      if (
        this.webSocketService &&
        this.webSocketService.isUserConnected(userId)
      ) {
        const notificationData: RealTimeNotificationData = {
          notification: fullNotification,
          delivery,
          isImmediate: true,
          requiresAck: fullNotification.priority === 'urgent',
        };

        await this.webSocketService.sendToUser(
          userId,
          'notification:received',
          notificationData,
        );

        delivery.status = 'delivered';
        delivery.deliveredAt = new Date();

        this.logger.log(
          `Real-time notification sent to user ${userId}: ${fullNotification.title}`,
        );
      } else {
        // Queue for later delivery if user not connected
        await this.queueService.enqueue(
          fullNotification,
          ['websocket'],
          undefined,
          fullNotification.priority,
        );
        delivery.status = 'pending';

        this.logger.debug(
          `User ${userId} not connected, queued notification: ${fullNotification.id}`,
        );
      }
    } catch (error) {
      delivery.status = 'failed';
      delivery.errorMessage = error.message;
      this.logger.error(
        `Failed to send real-time notification to user ${userId}:`,
        error,
      );
    }

    // Track delivery
    this.trackDelivery(delivery);

    // Track analytics
    this.trackAnalytics({
      id: this.generateAnalyticsId(),
      notificationId: fullNotification.id,
      userId,
      app: fullNotification.app,
      type: fullNotification.type,
      action: delivery.status === 'delivered' ? 'delivered' : 'failed',
      channel: 'websocket',
      timestamp: new Date(),
    });

    // Emit event for other services
    this.eventEmitter.emit('notification.sent', {
      notification: fullNotification,
      delivery,
      timestamp: new Date(),
    });
  }

  /**
   * Send cross-app notification
   */
  async sendCrossAppNotification(
    sourceApp: LAndDApp,
    targetUsers: string[],
    notification: Omit<
      LAndDNotification,
      'id' | 'userId' | 'createdAt' | 'isRead'
    >,
  ): Promise<void> {
    const crossAppNotification: CrossAppNotification = {
      id: this.generateCrossAppNotificationId(),
      sourceApp,
      targetApps: [notification.app], // Can be extended for multiple target apps
      notification: notification as LAndDNotification,
      createdAt: new Date(),
    };

    // Send to each target user
    const promises = targetUsers.map((userId) =>
      this.sendRealTimeNotification(userId, {
        ...notification,
        metadata: {
          ...notification.metadata,
          sourceApp,
          isCrossApp: true,
        },
      }),
    );

    await Promise.allSettled(promises);

    this.logger.log(
      `Cross-app notification sent from ${sourceApp} to ${targetUsers.length} users`,
    );

    // Emit cross-app event
    this.eventEmitter.emit('notification.cross-app', crossAppNotification);
  }

  /**
   * Send bulk notification
   */
  async sendBulkNotification(
    userIds: string[],
    notification: Omit<
      LAndDNotification,
      'id' | 'userId' | 'createdAt' | 'isRead'
    >,
    channels: NotificationChannel[] = ['websocket'],
  ): Promise<void> {
    const chunkSize = 100; // Process in chunks to avoid overwhelming the system

    for (let i = 0; i < userIds.length; i += chunkSize) {
      const chunk = userIds.slice(i, i + chunkSize);

      const promises = chunk.map((userId) =>
        this.sendRealTimeNotification(userId, notification),
      );

      await Promise.allSettled(promises);

      // Small delay between chunks to prevent system overload
      if (i + chunkSize < userIds.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    this.logger.log(
      `Bulk notification sent to ${userIds.length} users: ${notification.title}`,
    );
  }

  /**
   * Get notification history for user
   */
  async getNotificationHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0,
    app?: LAndDApp,
    type?: string,
  ): Promise<LAndDNotification[]> {
    const userNotifications = this.userNotifications.get(userId) || [];

    // Filter by app and type if specified
    let filtered = userNotifications;
    if (app) {
      filtered = filtered.filter((n) => n.app === app);
    }
    if (type) {
      filtered = filtered.filter((n) => n.type === type);
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // Apply pagination
    return filtered.slice(offset, offset + limit);
  }

  /**
   * Mark notifications as read
   */
  async markAsRead(userId: string, notificationIds: string[]): Promise<void> {
    const userNotifications = this.userNotifications.get(userId) || [];
    let markedCount = 0;

    userNotifications.forEach((notification) => {
      if (notificationIds.includes(notification.id) && !notification.isRead) {
        notification.isRead = true;
        notification.readAt = new Date();
        markedCount++;

        // Track analytics
        this.trackAnalytics({
          id: this.generateAnalyticsId(),
          notificationId: notification.id,
          userId,
          app: notification.app,
          type: notification.type,
          action: 'read',
          channel: 'websocket', // Assuming read via WebSocket
          timestamp: new Date(),
        });
      }
    });

    if (markedCount > 0) {
      this.logger.debug(
        `Marked ${markedCount} notifications as read for user ${userId}`,
      );

      // Emit event
      this.eventEmitter.emit('notification.read', {
        userId,
        notificationIds,
        count: markedCount,
        timestamp: new Date(),
      });

      // Send real-time update to user
      if (
        this.webSocketService &&
        this.webSocketService.isUserConnected(userId)
      ) {
        await this.webSocketService.sendToUser(
          userId,
          'notification:read_status_updated',
          {
            notificationIds,
            isRead: true,
            timestamp: new Date(),
          },
        );
      }
    }
  }

  /**
   * Mark all notifications as read for user
   */
  async markAllAsRead(userId: string, app?: LAndDApp): Promise<void> {
    const userNotifications = this.userNotifications.get(userId) || [];

    const notificationIds = userNotifications
      .filter((n) => !n.isRead && (!app || n.app === app))
      .map((n) => n.id);

    if (notificationIds.length > 0) {
      await this.markAsRead(userId, notificationIds);
    }
  }

  /**
   * Get unread notification count
   */
  getUnreadCount(userId: string, app?: LAndDApp): number {
    const userNotifications = this.userNotifications.get(userId) || [];

    return userNotifications.filter((n) => !n.isRead && (!app || n.app === app))
      .length;
  }

  /**
   * Get notification summary
   */
  getNotificationSummary(
    userId: string,
    period: 'day' | 'week' | 'month' = 'week',
    app?: LAndDApp,
  ): NotificationSummary {
    const userNotifications = this.userNotifications.get(userId) || [];

    // Calculate period start
    const now = new Date();
    const periodStart = new Date(now);

    switch (period) {
      case 'day':
        periodStart.setDate(now.getDate() - 1);
        break;
      case 'week':
        periodStart.setDate(now.getDate() - 7);
        break;
      case 'month':
        periodStart.setMonth(now.getMonth() - 1);
        break;
    }

    // Filter notifications by period and app
    const periodNotifications = userNotifications.filter(
      (n) => n.createdAt >= periodStart && (!app || n.app === app),
    );

    // Calculate counts
    const byType = {} as Record<string, number>;
    const byPriority = {} as Record<string, number>;
    const byApp = {} as Record<string, number>;

    periodNotifications.forEach((n) => {
      byType[n.type] = (byType[n.type] || 0) + 1;
      byPriority[n.priority] = (byPriority[n.priority] || 0) + 1;
      byApp[n.app] = (byApp[n.app] || 0) + 1;
    });

    const lastNotification = userNotifications
      .filter((n) => !app || n.app === app)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];

    return {
      userId,
      app,
      period,
      total: periodNotifications.length,
      unread: periodNotifications.filter((n) => !n.isRead).length,
      byType: byType as any,
      byPriority: byPriority as any,
      byApp: byApp as any,
      lastNotificationAt: lastNotification?.createdAt,
    };
  }

  /**
   * Delete notification
   */
  async deleteNotification(
    userId: string,
    notificationId: string,
  ): Promise<boolean> {
    const userNotifications = this.userNotifications.get(userId) || [];
    const index = userNotifications.findIndex((n) => n.id === notificationId);

    if (index !== -1) {
      userNotifications.splice(index, 1);
      this.userNotifications.set(userId, userNotifications);

      // Clean up deliveries
      this.deliveries.delete(notificationId);

      this.logger.debug(
        `Deleted notification ${notificationId} for user ${userId}`,
      );
      return true;
    }

    return false;
  }

  /**
   * Get service statistics
   */
  getStats() {
    const totalNotifications = Array.from(
      this.userNotifications.values(),
    ).reduce((sum, notifications) => sum + notifications.length, 0);

    const totalDeliveries = Array.from(this.deliveries.values()).reduce(
      (sum, deliveries) => sum + deliveries.length,
      0,
    );

    return {
      notifications: {
        total: totalNotifications,
        users: this.userNotifications.size,
      },
      deliveries: {
        total: totalDeliveries,
        success: Array.from(this.deliveries.values())
          .flat()
          .filter((d) => d.status === 'delivered').length,
      },
      analytics: {
        total: this.analytics.length,
      },
      queue: this.queueService.getStats(),
      templates: this.templates.size,
    };
  }

  /**
   * Store notification for user
   */
  private storeNotification(notification: LAndDNotification): void {
    if (!this.userNotifications.has(notification.userId)) {
      this.userNotifications.set(notification.userId, []);
    }

    const userNotifications = this.userNotifications.get(notification.userId)!;
    userNotifications.unshift(notification); // Add to beginning (newest first)

    // Keep only last 1000 notifications per user
    if (userNotifications.length > 1000) {
      userNotifications.splice(1000);
    }
  }

  /**
   * Track delivery
   */
  private trackDelivery(delivery: NotificationDelivery): void {
    if (!this.deliveries.has(delivery.notificationId)) {
      this.deliveries.set(delivery.notificationId, []);
    }

    this.deliveries.get(delivery.notificationId)!.push(delivery);
  }

  /**
   * Track analytics
   */
  private trackAnalytics(analytics: NotificationAnalytics): void {
    this.analytics.unshift(analytics);

    // Keep only last 10000 analytics entries
    if (this.analytics.length > 10000) {
      this.analytics.splice(10000);
    }
  }

  /**
   * Get user preferences with defaults
   */
  private getUserPreferences(userId: string): NotificationPreferences {
    const existing = this.userPreferences.get(userId);
    if (existing) return existing;

    // Return default preferences
    const defaultPreferences: NotificationPreferences = {
      userId,
      preferences: {
        training: {},
        vendors: {},
        wins: {},
      },
      globalSettings: {
        enabled: true,
        channels: ['websocket'],
        batchNotifications: false,
        frequency: 'instant',
      },
      updatedAt: new Date(),
    };

    this.userPreferences.set(userId, defaultPreferences);
    return defaultPreferences;
  }

  /**
   * Check if notification should be sent based on preferences
   */
  private shouldSendNotification(
    notification: LAndDNotification,
    preferences: NotificationPreferences,
  ): boolean {
    if (!preferences.globalSettings.enabled) return false;

    const appPrefs = preferences.preferences[notification.app];
    const typePrefs = appPrefs[notification.type];

    if (typePrefs && !typePrefs.enabled) return false;

    // Check quiet hours if configured
    if (preferences.globalSettings.quietHours) {
      const now = new Date();
      const quietStart = preferences.globalSettings.quietHours.start;
      const quietEnd = preferences.globalSettings.quietHours.end;

      // Simple time check (can be enhanced with timezone support)
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(quietStart.replace(':', ''));
      const endTime = parseInt(quietEnd.replace(':', ''));

      if (startTime <= endTime) {
        if (currentTime >= startTime && currentTime <= endTime) return false;
      } else {
        if (currentTime >= startTime || currentTime <= endTime) return false;
      }
    }

    return true;
  }

  /**
   * Initialize default notification templates
   */
  private initializeDefaultTemplates(): void {
    const templates: NotificationTemplate[] = [
      // Training templates
      {
        id: 'training_course_assigned',
        app: 'training',
        type: 'assignment',
        name: 'Course Assigned',
        title: 'New Course Assignment',
        message: 'You have been assigned to course: {{courseName}}',
        variables: ['courseName'],
        priority: 'medium',
        channels: ['websocket', 'email'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'training_assessment_due',
        app: 'training',
        type: 'deadline',
        name: 'Assessment Due',
        title: 'Assessment Due Soon',
        message: 'Assessment "{{assessmentName}}" is due on {{deadline}}',
        variables: ['assessmentName', 'deadline'],
        priority: 'high',
        channels: ['websocket', 'email'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      // Vendor templates
      {
        id: 'vendor_proposal_received',
        app: 'vendors',
        type: 'info',
        name: 'Proposal Received',
        title: 'New Vendor Proposal',
        message: 'Received proposal from {{vendorName}} for {{amount}}',
        variables: ['vendorName', 'amount'],
        priority: 'medium',
        channels: ['websocket'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      // Wins templates
      {
        id: 'wins_featured',
        app: 'wins',
        type: 'achievement',
        name: 'Win Featured',
        title: 'Your Win Was Featured!',
        message:
          'Your submission "{{submissionTitle}}" has been featured in {{category}}',
        variables: ['submissionTitle', 'category'],
        priority: 'high',
        channels: ['websocket'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    templates.forEach((template) => {
      this.templates.set(template.id, template);
    });

    this.logger.log(
      `Initialized ${templates.length} default notification templates`,
    );
  }

  /**
   * Generate unique IDs
   */
  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateDeliveryId(): string {
    return `deliv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCrossAppNotificationId(): string {
    return `cross_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAnalyticsId(): string {
    return `analytics_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
