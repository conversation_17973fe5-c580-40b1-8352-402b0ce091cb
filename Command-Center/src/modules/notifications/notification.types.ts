import { LAndDApp } from '../l-and-d/websocket/websocket-event.types';

/**
 * Notification Types for L&D Platform
 * Defines all notification types and data structures
 */

// Base notification interface
export interface BaseNotification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  app: LAndDApp;
  createdAt: Date;
  readAt?: Date;
  isRead: boolean;
  priority: NotificationPriority;
  metadata?: Record<string, any>;
}

// Notification types
export type NotificationType =
  | 'info'
  | 'success'
  | 'warning'
  | 'error'
  | 'achievement'
  | 'reminder'
  | 'system'
  | 'assignment'
  | 'approval'
  | 'deadline'
  | 'milestone'
  | 'collaboration';

// Notification priority levels
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

// Training-specific notifications
export interface TrainingNotification extends BaseNotification {
  app: 'training';
  data: {
    courseId?: string;
    courseName?: string;
    assessmentId?: string;
    assessmentName?: string;
    sessionId?: string;
    sessionName?: string;
    progress?: number;
    deadline?: Date;
    instructor?: string;
    discussionId?: string;
    quizId?: string;
    certificateId?: string;
    achievementType?:
      | 'course_completion'
      | 'assessment_passed'
      | 'milestone_reached'
      | 'skill_earned';
  };
}

// Vendor-specific notifications
export interface VendorNotification extends BaseNotification {
  app: 'vendors';
  data: {
    vendorId?: string;
    vendorName?: string;
    proposalId?: string;
    proposalTitle?: string;
    contractId?: string;
    contractName?: string;
    rfpId?: string;
    rfpTitle?: string;
    amount?: number;
    currency?: string;
    deadline?: Date;
    status?: 'pending' | 'approved' | 'rejected' | 'under_review';
    reviewers?: string[];
    approvers?: string[];
  };
}

// Wins-specific notifications
export interface WinsNotification extends BaseNotification {
  app: 'wins';
  data: {
    submissionId?: string;
    submissionTitle?: string;
    teamId?: string;
    teamName?: string;
    weekNumber?: number;
    category?: string;
    milestoneId?: string;
    milestoneName?: string;
    recognitionId?: string;
    recognitionType?: string;
    points?: number;
    rank?: number;
    featuredOn?: string;
    collaborators?: string[];
  };
}

// Union type for all L&D notifications
export type LAndDNotification =
  | TrainingNotification
  | VendorNotification
  | WinsNotification;

// Notification delivery status
export interface NotificationDelivery {
  id: string;
  notificationId: string;
  userId: string;
  channel: NotificationChannel;
  status: DeliveryStatus;
  attempts: number;
  deliveredAt?: Date;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

// Notification channels
export type NotificationChannel =
  | 'websocket'
  | 'email'
  | 'sms'
  | 'push'
  | 'in_app'
  | 'slack'
  | 'teams';

// Delivery status
export type DeliveryStatus =
  | 'pending'
  | 'delivered'
  | 'failed'
  | 'retrying'
  | 'cancelled';

// Notification preferences
export interface NotificationPreferences {
  userId: string;
  preferences: {
    [K in LAndDApp]: {
      [T in NotificationType]?: {
        enabled: boolean;
        channels: NotificationChannel[];
        priority?: NotificationPriority;
        quietHours?: {
          start: string; // HH:mm format
          end: string; // HH:mm format
          timezone: string;
        };
      };
    };
  };
  globalSettings: {
    enabled: boolean;
    channels: NotificationChannel[];
    quietHours?: {
      start: string;
      end: string;
      timezone: string;
    };
    batchNotifications: boolean;
    frequency: 'instant' | 'hourly' | 'daily';
  };
  updatedAt: Date;
}

// Notification template
export interface NotificationTemplate {
  id: string;
  app: LAndDApp;
  type: NotificationType;
  name: string;
  title: string;
  message: string;
  variables: string[]; // Template variables like {{courseName}}, {{deadline}}
  priority: NotificationPriority;
  channels: NotificationChannel[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Notification queue item
export interface NotificationQueueItem {
  id: string;
  notification: LAndDNotification;
  scheduledFor: Date;
  attempts: number;
  maxAttempts: number;
  priority: NotificationPriority;
  channels: NotificationChannel[];
  userId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  processedAt?: Date;
  errorMessage?: string;
}

// Bulk notification request
export interface BulkNotificationRequest {
  userIds: string[];
  template: NotificationTemplate;
  data: Record<string, any>;
  scheduledFor?: Date;
  channels?: NotificationChannel[];
  priority?: NotificationPriority;
}

// Cross-app notification
export interface CrossAppNotification {
  id: string;
  sourceApp: LAndDApp;
  targetApps: LAndDApp[];
  notification: LAndDNotification;
  propagationRules?: {
    targetApp: LAndDApp;
    transform?: (notification: LAndDNotification) => Partial<LAndDNotification>;
    condition?: (notification: LAndDNotification, userId: string) => boolean;
  }[];
  createdAt: Date;
}

// Notification analytics
export interface NotificationAnalytics {
  id: string;
  notificationId: string;
  userId: string;
  app: LAndDApp;
  type: NotificationType;
  action: 'sent' | 'delivered' | 'read' | 'clicked' | 'dismissed' | 'failed';
  channel: NotificationChannel;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Notification summary
export interface NotificationSummary {
  userId: string;
  app?: LAndDApp;
  period: 'day' | 'week' | 'month';
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
  byPriority: Record<NotificationPriority, number>;
  byApp: Record<LAndDApp, number>;
  lastNotificationAt?: Date;
}

// Real-time notification data for WebSocket
export interface RealTimeNotificationData {
  notification: LAndDNotification;
  delivery: NotificationDelivery;
  isImmediate: boolean;
  requiresAck: boolean;
  expiresAt?: Date;
}
