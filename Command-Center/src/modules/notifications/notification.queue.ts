import { Injectable, Logger } from '@nestjs/common';
import {
  NotificationQueueItem,
  LAndDNotification,
  NotificationChannel,
  NotificationPriority,
  DeliveryStatus,
  BulkNotificationRequest,
} from './notification.types';

/**
 * Notification Queue Service
 * Manages queuing, prioritization, and processing of notifications
 */
@Injectable()
export class NotificationQueueService {
  private readonly logger = new Logger(NotificationQueueService.name);

  // Priority queues: priority -> NotificationQueueItem[]
  private readonly queues = new Map<
    NotificationPriority,
    NotificationQueueItem[]
  >([
    ['urgent', []],
    ['high', []],
    ['medium', []],
    ['low', []],
  ]);

  // Queue processing status
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;

  // Queue statistics
  private stats = {
    queued: 0,
    processed: 0,
    failed: 0,
    retries: 0,
  };

  constructor() {
    this.startQueueProcessor();
  }

  /**
   * Add notification to queue
   */
  async enqueue(
    notification: LAndDNotification,
    channels: NotificationChannel[] = ['websocket'],
    scheduledFor?: Date,
    priority?: NotificationPriority,
    maxAttempts: number = 3,
  ): Promise<string> {
    const queueItem: NotificationQueueItem = {
      id: this.generateQueueId(),
      notification,
      scheduledFor: scheduledFor || new Date(),
      attempts: 0,
      maxAttempts,
      priority: priority || notification.priority,
      channels,
      userId: notification.userId,
      status: 'queued',
      createdAt: new Date(),
    };

    // Add to appropriate priority queue
    const queue = this.queues.get(queueItem.priority) || [];
    queue.push(queueItem);
    this.queues.set(queueItem.priority, queue);

    this.stats.queued++;

    this.logger.debug(
      `Queued notification ${queueItem.id} for user ${queueItem.userId} with priority ${queueItem.priority}`,
    );

    return queueItem.id;
  }

  /**
   * Add bulk notifications to queue
   */
  async enqueueBulk(request: BulkNotificationRequest): Promise<string[]> {
    const queueIds: string[] = [];

    for (const userId of request.userIds) {
      // Create notification from template
      const notification: LAndDNotification = {
        id: this.generateNotificationId(),
        userId,
        title: this.replaceTemplateVariables(
          request.template.title,
          request.data,
        ),
        message: this.replaceTemplateVariables(
          request.template.message,
          request.data,
        ),
        type: request.template.type,
        app: request.template.app,
        createdAt: new Date(),
        isRead: false,
        priority: request.priority || request.template.priority,
        data: request.data,
      } as LAndDNotification;

      const queueId = await this.enqueue(
        notification,
        request.channels || request.template.channels,
        request.scheduledFor,
        request.priority || request.template.priority,
      );

      queueIds.push(queueId);
    }

    this.logger.log(`Bulk queued ${queueIds.length} notifications`);
    return queueIds;
  }

  /**
   * Get next item to process (highest priority, earliest scheduled)
   */
  getNextItem(): NotificationQueueItem | null {
    const priorities: NotificationPriority[] = [
      'urgent',
      'high',
      'medium',
      'low',
    ];
    const now = new Date();

    for (const priority of priorities) {
      const queue = this.queues.get(priority) || [];

      // Find items ready to be processed
      const readyItems = queue.filter(
        (item) =>
          item.status === 'queued' &&
          item.scheduledFor <= now &&
          item.attempts < item.maxAttempts,
      );

      if (readyItems.length > 0) {
        // Sort by scheduled time (earliest first)
        readyItems.sort(
          (a, b) => a.scheduledFor.getTime() - b.scheduledFor.getTime(),
        );

        const item = readyItems[0];
        item.status = 'processing';

        return item;
      }
    }

    return null;
  }

  /**
   * Mark item as completed
   */
  markCompleted(queueId: string): void {
    this.updateItemStatus(queueId, 'completed');
    this.stats.processed++;
  }

  /**
   * Mark item as failed and potentially retry
   */
  markFailed(queueId: string, errorMessage: string): void {
    const item = this.findItem(queueId);
    if (!item) return;

    item.attempts++;
    item.errorMessage = errorMessage;

    if (item.attempts >= item.maxAttempts) {
      item.status = 'failed';
      this.stats.failed++;
      this.logger.error(
        `Notification ${queueId} failed after ${item.attempts} attempts: ${errorMessage}`,
      );
    } else {
      item.status = 'queued';
      // Exponential backoff: retry after 2^attempts minutes
      const delayMinutes = Math.pow(2, item.attempts);
      item.scheduledFor = new Date(Date.now() + delayMinutes * 60 * 1000);
      this.stats.retries++;
      this.logger.warn(
        `Notification ${queueId} failed, retry ${item.attempts}/${item.maxAttempts} in ${delayMinutes} minutes`,
      );
    }
  }

  /**
   * Get queue statistics
   */
  getStats() {
    const queueCounts = {
      urgent: this.queues.get('urgent')?.length || 0,
      high: this.queues.get('high')?.length || 0,
      medium: this.queues.get('medium')?.length || 0,
      low: this.queues.get('low')?.length || 0,
    };

    return {
      ...this.stats,
      queueCounts,
      totalInQueue: Object.values(queueCounts).reduce(
        (sum, count) => sum + count,
        0,
      ),
      isProcessing: this.isProcessing,
    };
  }

  /**
   * Get queued items for a user
   */
  getUserQueuedItems(userId: string): NotificationQueueItem[] {
    const items: NotificationQueueItem[] = [];

    this.queues.forEach((queue) => {
      const userItems = queue.filter((item) => item.userId === userId);
      items.push(...userItems);
    });

    return items.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  }

  /**
   * Remove expired items from queue
   */
  cleanup(): void {
    const now = new Date();
    const expiredThreshold = 24 * 60 * 60 * 1000; // 24 hours
    let removedCount = 0;

    this.queues.forEach((queue, priority) => {
      const validItems = queue.filter((item) => {
        const isExpired =
          now.getTime() - item.createdAt.getTime() > expiredThreshold;
        const shouldRemove =
          isExpired ||
          (item.status === 'failed' && item.attempts >= item.maxAttempts);

        if (shouldRemove) removedCount++;
        return !shouldRemove;
      });

      this.queues.set(priority, validItems);
    });

    if (removedCount > 0) {
      this.logger.debug(
        `Cleaned up ${removedCount} expired/failed notifications from queue`,
      );
    }
  }

  /**
   * Start queue processor
   */
  private startQueueProcessor(): void {
    this.processingInterval = setInterval(async () => {
      if (this.isProcessing) return;

      this.isProcessing = true;

      try {
        const item = this.getNextItem();
        if (item) {
          await this.processQueueItem(item);
        }
      } catch (error) {
        this.logger.error('Error processing queue item:', error);
      } finally {
        this.isProcessing = false;
      }
    }, 1000); // Process every second

    // Cleanup every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Process individual queue item
   */
  private async processQueueItem(item: NotificationQueueItem): Promise<void> {
    // This will be called by the notification service
    // For now, just mark as completed (placeholder)
    this.markCompleted(item.id);
  }

  /**
   * Find item by ID
   */
  private findItem(queueId: string): NotificationQueueItem | undefined {
    for (const queue of this.queues.values()) {
      const item = queue.find((item) => item.id === queueId);
      if (item) return item;
    }
    return undefined;
  }

  /**
   * Update item status
   */
  private updateItemStatus(
    queueId: string,
    status: 'queued' | 'processing' | 'completed' | 'failed',
  ): void {
    const item = this.findItem(queueId);
    if (item) {
      item.status = status;
      item.processedAt = new Date();
    }
  }

  /**
   * Replace template variables in string
   */
  private replaceTemplateVariables(
    template: string,
    data: Record<string, any>,
  ): string {
    let result = template;

    Object.entries(data).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    });

    return result;
  }

  /**
   * Generate unique queue ID
   */
  private generateQueueId(): string {
    return `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique notification ID
   */
  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Stop queue processor (cleanup)
   */
  onModuleDestroy(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
  }
}
