import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { WsAuthGuard } from '../../common/guards/ws-auth.guard';
import { NotificationService } from './notification.service';
import { AuthenticatedSocket } from '../../common/gateways/app.gateway';
import {
  LAndDNotification,
  NotificationChannel,
  RealTimeNotificationData,
} from './notification.types';
import { LAndDApp } from '../l-and-d/websocket/websocket-event.types';

/**
 * Notification Gateway
 * Handles WebSocket-specific notification events and real-time delivery
 */
@WebSocketGateway({
  namespace: '/notifications',
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
})
export class NotificationGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(NotificationGateway.name);
  private readonly connectedUsers = new Map<string, Set<string>>(); // userId -> Set<socketId>

  constructor(
    private readonly notificationService: NotificationService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  afterInit(server: Server) {
    this.logger.log('Notification Gateway initialized');

    // Set this gateway's server in the notification service
    this.notificationService.setWebSocketService(this);
  }

  async handleConnection(client: AuthenticatedSocket) {
    const userId = client.userId;
    if (!userId) {
      client.disconnect();
      return;
    }

    this.logger.log(
      `Notification client connected: ${client.id} (User: ${userId})`,
    );

    // Track user connection
    if (!this.connectedUsers.has(userId)) {
      this.connectedUsers.set(userId, new Set());
    }
    this.connectedUsers.get(userId)!.add(client.id);

    // Join user-specific room
    client.join(`user:${userId}`);

    // Send connection acknowledgment with unread counts
    const unreadCounts = {
      total: this.notificationService.getUnreadCount(userId),
      training: this.notificationService.getUnreadCount(userId, 'training'),
      vendors: this.notificationService.getUnreadCount(userId, 'vendors'),
      wins: this.notificationService.getUnreadCount(userId, 'wins'),
    };

    client.emit('notification:connected', {
      socketId: client.id,
      userId,
      unreadCounts,
      timestamp: new Date(),
    });

    // Send any pending notifications for this user
    await this.sendPendingNotifications(userId);
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    const userId = client.userId;
    if (!userId) return;

    this.logger.log(
      `Notification client disconnected: ${client.id} (User: ${userId})`,
    );

    // Remove from user connections
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      userSockets.delete(client.id);
      if (userSockets.size === 0) {
        this.connectedUsers.delete(userId);
      }
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:get_history')
  async handleGetHistory(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: {
      limit?: number;
      offset?: number;
      app?: LAndDApp;
      type?: string;
    },
  ): Promise<void> {
    const { limit = 50, offset = 0, app, type } = data || {};

    try {
      const notifications =
        await this.notificationService.getNotificationHistory(
          client.userId!,
          limit,
          offset,
          app,
          type,
        );

      client.emit('notification:history', {
        notifications,
        count: notifications.length,
        hasMore: notifications.length === limit,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Error getting notification history:', error);
      client.emit('notification:error', {
        action: 'get_history',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:mark_read')
  async handleMarkRead(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { notificationIds: string[] },
  ): Promise<void> {
    const { notificationIds } = data;

    if (!notificationIds || !Array.isArray(notificationIds)) {
      throw new WsException('Invalid notification IDs');
    }

    try {
      await this.notificationService.markAsRead(
        client.userId!,
        notificationIds,
      );

      client.emit('notification:read_success', {
        notificationIds,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Error marking notifications as read:', error);
      client.emit('notification:error', {
        action: 'mark_read',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:mark_all_read')
  async handleMarkAllRead(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { app?: LAndDApp },
  ): Promise<void> {
    const { app } = data || {};

    try {
      await this.notificationService.markAllAsRead(client.userId!, app);

      client.emit('notification:all_read_success', {
        app,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Error marking all notifications as read:', error);
      client.emit('notification:error', {
        action: 'mark_all_read',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:get_unread_count')
  async handleGetUnreadCount(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { app?: LAndDApp },
  ): Promise<void> {
    const { app } = data || {};

    try {
      const count = this.notificationService.getUnreadCount(
        client.userId!,
        app,
      );

      client.emit('notification:unread_count', {
        app,
        count,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Error getting unread count:', error);
      client.emit('notification:error', {
        action: 'get_unread_count',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:get_summary')
  async handleGetSummary(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody()
    data: {
      period?: 'day' | 'week' | 'month';
      app?: LAndDApp;
    },
  ): Promise<void> {
    const { period = 'week', app } = data || {};

    try {
      const summary = this.notificationService.getNotificationSummary(
        client.userId!,
        period,
        app,
      );

      client.emit('notification:summary', {
        summary,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error('Error getting notification summary:', error);
      client.emit('notification:error', {
        action: 'get_summary',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:delete')
  async handleDeleteNotification(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { notificationId: string },
  ): Promise<void> {
    const { notificationId } = data;

    if (!notificationId) {
      throw new WsException('Notification ID is required');
    }

    try {
      const deleted = await this.notificationService.deleteNotification(
        client.userId!,
        notificationId,
      );

      if (deleted) {
        client.emit('notification:deleted', {
          notificationId,
          timestamp: new Date(),
        });
      } else {
        client.emit('notification:error', {
          action: 'delete',
          error: 'Notification not found',
          timestamp: new Date(),
        });
      }
    } catch (error) {
      this.logger.error('Error deleting notification:', error);
      client.emit('notification:error', {
        action: 'delete',
        error: error.message,
        timestamp: new Date(),
      });
    }
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:subscribe_app')
  async handleSubscribeApp(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { app: LAndDApp },
  ): Promise<void> {
    const { app } = data;

    if (!app || !['training', 'vendors', 'wins'].includes(app)) {
      throw new WsException('Invalid app specified');
    }

    // Join app-specific room
    const appRoom = `app:${app}`;
    client.join(appRoom);

    client.emit('notification:subscribed', {
      app,
      room: appRoom,
      timestamp: new Date(),
    });

    this.logger.debug(
      `User ${client.userId} subscribed to ${app} notifications`,
    );
  }

  @UseGuards(WsAuthGuard)
  @SubscribeMessage('notification:unsubscribe_app')
  async handleUnsubscribeApp(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { app: LAndDApp },
  ): Promise<void> {
    const { app } = data;

    if (!app || !['training', 'vendors', 'wins'].includes(app)) {
      throw new WsException('Invalid app specified');
    }

    // Leave app-specific room
    const appRoom = `app:${app}`;
    client.leave(appRoom);

    client.emit('notification:unsubscribed', {
      app,
      room: appRoom,
      timestamp: new Date(),
    });

    this.logger.debug(
      `User ${client.userId} unsubscribed from ${app} notifications`,
    );
  }

  // ========================================
  // SERVER-SIDE METHODS (called by NotificationService)
  // ========================================

  /**
   * Send notification to specific user
   */
  async sendToUser(userId: string, event: string, data: any): Promise<void> {
    this.server.to(`user:${userId}`).emit(event, data);
  }

  /**
   * Check if user is connected
   */
  isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  /**
   * Broadcast to app subscribers
   */
  async broadcastToApp(app: LAndDApp, event: string, data: any): Promise<void> {
    this.server.to(`app:${app}`).emit(event, data);
  }

  /**
   * Send pending notifications to recently connected user
   */
  private async sendPendingNotifications(userId: string): Promise<void> {
    try {
      // Get recent unread notifications (last 24 hours)
      const recentNotifications =
        await this.notificationService.getNotificationHistory(
          userId,
          10, // Limit to 10 most recent
          0,
          undefined, // All apps
          undefined, // All types
        );

      const unreadNotifications = recentNotifications.filter((n) => !n.isRead);

      if (unreadNotifications.length > 0) {
        // Send each unread notification
        for (const notification of unreadNotifications) {
          const notificationData: RealTimeNotificationData = {
            notification,
            delivery: {
              id: `pending_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              notificationId: notification.id,
              userId,
              channel: 'websocket',
              status: 'delivered',
              attempts: 1,
              deliveredAt: new Date(),
            },
            isImmediate: false,
            requiresAck: false,
          };

          await this.sendToUser(
            userId,
            'notification:received',
            notificationData,
          );
        }

        this.logger.debug(
          `Sent ${unreadNotifications.length} pending notifications to user ${userId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error sending pending notifications to user ${userId}:`,
        error,
      );
    }
  }

  // ========================================
  // EVENT LISTENERS
  // ========================================

  @OnEvent('notification.sent')
  async handleNotificationSent(payload: any): Promise<void> {
    const { notification, delivery } = payload;

    // Broadcast unread count update to user
    const unreadCount = this.notificationService.getUnreadCount(
      notification.userId,
    );

    await this.sendToUser(
      notification.userId,
      'notification:unread_count_updated',
      {
        total: unreadCount,
        app: notification.app,
        timestamp: new Date(),
      },
    );
  }

  @OnEvent('notification.read')
  async handleNotificationRead(payload: any): Promise<void> {
    const { userId, notificationIds, count } = payload;

    // Broadcast unread count update to user
    const unreadCount = this.notificationService.getUnreadCount(userId);

    await this.sendToUser(userId, 'notification:unread_count_updated', {
      total: unreadCount,
      readCount: count,
      timestamp: new Date(),
    });
  }

  @OnEvent('notification.cross-app')
  async handleCrossAppNotification(payload: any): Promise<void> {
    const { sourceApp, targetApps, notification } = payload;

    this.logger.debug(
      `Cross-app notification from ${sourceApp} to ${targetApps.join(', ')}`,
    );

    // Can add additional cross-app handling logic here
  }
}
