import { Module, forwardRef } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { NotificationService } from './notification.service';
import { NotificationGateway } from './notification.gateway';
import { NotificationQueueService } from './notification.queue';

/**
 * Notification Module
 * Handles real-time notifications across L&D applications
 */
@Module({
  imports: [EventEmitterModule.forRoot()],
  providers: [
    NotificationService,
    NotificationGateway,
    NotificationQueueService,
  ],
  exports: [NotificationService, NotificationGateway, NotificationQueueService],
})
export class NotificationModule {}
