import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { EmailProcessor } from './processors/email.processor';
import { FileProcessor } from './processors/file.processor';
import { EnhancedFileProcessor } from './processors/enhanced-file.processor';
import { EmbeddingProcessor } from './processors/embedding.processor';
import { DocumentParsingProcessor } from './processors/document-parsing.processor';
import { MaintenanceProcessor } from './processors/maintenance.processor';
import { DocumentProcessingProcessor } from './processors/document-processing.processor';
import { QueueService } from './queue.service';
import { QueueManagerService } from './services/queue-manager.service';
import { ScheduledTasksService } from './services/scheduled-tasks.service';
import { JobMonitoringService } from './services/job-monitoring.service';
import { EnhancedCacheModule } from '../cache/cache.module';
import { FilesModule } from '../files/files.module';
import { DocumentsModule } from '../documents/documents.module';
import { AiModule } from '../ai/ai.module';
import { VectorModule } from '../vector/vector.module';
import { DocumentParsingModule } from '../document-parsing/document-parsing.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get('redis.host'),
          port: configService.get('redis.port'),
        },
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: false,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      { name: 'email' },
      { name: 'file-processing' },
      { name: 'embeddings' },
      { name: 'document-parsing' },
      { name: 'maintenance' },
      { name: 'document-processing' },
    ),
    ScheduleModule.forRoot(),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    EnhancedCacheModule,
    FilesModule,
    DocumentsModule,
    AiModule,
    VectorModule,
    DocumentParsingModule,
    NotificationModule,
  ],
  providers: [
    QueueService,
    QueueManagerService,
    ScheduledTasksService,
    JobMonitoringService,
    EmailProcessor,
    FileProcessor,
    EnhancedFileProcessor,
    EmbeddingProcessor,
    DocumentParsingProcessor,
    MaintenanceProcessor,
    DocumentProcessingProcessor,
  ],
  exports: [QueueService, QueueManagerService, JobMonitoringService],
})
export class QueueModule {}
