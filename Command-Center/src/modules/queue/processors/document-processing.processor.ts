import { Processor, WorkerHost, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { HttpService } from '@nestjs/axios';
import { DocumentsService } from '../../documents/documents.service';
import { firstValueFrom } from 'rxjs';

export interface DocumentProcessingJobData {
  documentId: string;
  jobId: string;
  pythonServiceUrl: string;
  maxAttempts?: number;
  currentAttempt?: number;
}

@Processor('document-processing')
export class DocumentProcessingProcessor extends WorkerHost {
  private readonly logger = new Logger(DocumentProcessingProcessor.name);

  constructor(
    private readonly documentsService: DocumentsService,
    private readonly httpService: HttpService,
  ) {
    super();
  }

  @Processor('check-processing-status')
  async handleDocumentProcessing(job: Job<DocumentProcessingJobData>) {
    const { documentId, jobId, pythonServiceUrl, maxAttempts = 30, currentAttempt = 1 } = job.data;

    this.logger.log(`Checking processing status for document ${documentId}, job ${jobId} (attempt ${currentAttempt}/${maxAttempts})`);

    try {
      // Check processing status from Python service
      const response = await firstValueFrom(
        this.httpService.get(`${pythonServiceUrl}/status/${jobId}`, {
          timeout: 10000,
        }),
      );

      const status = response.data;

      if (status.status === 'completed' && status.result) {
        // Document processing completed successfully
        this.logger.log(`Document processing completed for ${documentId}`);
        
        await this.documentsService.updateDocumentWithProcessingResult(
          documentId,
          status.result,
        );

        return { success: true, status: 'completed' };
      } else if (status.status === 'failed') {
        // Document processing failed
        this.logger.error(`Document processing failed for ${documentId}: ${status.error}`);
        
        // Update document with error status
        await this.documentsService.updateDocumentProcessingError(
          documentId,
          status.error || 'Processing failed',
        );

        throw new Error(status.error || 'Document processing failed');
      } else if (status.status === 'processing' || status.status === 'queued') {
        // Still processing, check if we should retry
        if (currentAttempt < maxAttempts) {
          // Schedule next check in 5 seconds
          const delay = 5000;
          
          await job.queue.add(
            'check-processing-status',
            {
              ...job.data,
              currentAttempt: currentAttempt + 1,
            },
            {
              delay,
              attempts: 1,
              removeOnComplete: true,
              removeOnFail: false,
            },
          );

          this.logger.log(`Document ${documentId} still processing (${status.progress}%), will check again in ${delay}ms`);
          return { success: true, status: 'scheduled_retry', nextAttempt: currentAttempt + 1 };
        } else {
          // Max attempts reached
          this.logger.error(`Max attempts reached for document ${documentId}, marking as failed`);
          
          await this.documentsService.updateDocumentProcessingError(
            documentId,
            'Processing timeout - maximum attempts reached',
          );

          throw new Error('Processing timeout - maximum attempts reached');
        }
      } else {
        // Unknown status
        this.logger.warn(`Unknown processing status for document ${documentId}: ${status.status}`);
        
        if (currentAttempt < maxAttempts) {
          // Retry
          const delay = 10000; // 10 seconds for unknown status
          
          await job.queue.add(
            'check-processing-status',
            {
              ...job.data,
              currentAttempt: currentAttempt + 1,
            },
            {
              delay,
              attempts: 1,
              removeOnComplete: true,
              removeOnFail: false,
            },
          );

          return { success: true, status: 'scheduled_retry_unknown', nextAttempt: currentAttempt + 1 };
        } else {
          throw new Error(`Unknown processing status: ${status.status}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error checking processing status for document ${documentId}:`, error.message);

      if (currentAttempt < maxAttempts) {
        // Retry with exponential backoff
        const delay = Math.min(30000, 2000 * Math.pow(2, currentAttempt - 1)); // Max 30 seconds
        
        await job.queue.add(
          'check-processing-status',
          {
            ...job.data,
            currentAttempt: currentAttempt + 1,
          },
          {
            delay,
            attempts: 1,
            removeOnComplete: true,
            removeOnFail: false,
          },
        );

        this.logger.log(`Will retry checking document ${documentId} in ${delay}ms (attempt ${currentAttempt + 1}/${maxAttempts})`);
        return { success: true, status: 'scheduled_retry_error', nextAttempt: currentAttempt + 1 };
      } else {
        // Final failure
        await this.documentsService.updateDocumentProcessingError(
          documentId,
          `Failed to check processing status: ${error.message}`,
        );

        throw error;
      }
    }
  }

  @OnQueueActive()
  onActive(job: Job<DocumentProcessingJobData>) {
    this.logger.log(`Processing document processing job ${job.id} for document ${job.data.documentId}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job<DocumentProcessingJobData>, result: any) {
    this.logger.log(`Document processing job ${job.id} completed for document ${job.data.documentId}:`, result);
  }

  @OnQueueFailed()
  onFailed(job: Job<DocumentProcessingJobData>, error: Error) {
    this.logger.error(`Document processing job ${job.id} failed for document ${job.data.documentId}:`, error.message);
  }
}