import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { DocumentProcessingJobData } from './processors/document-processing.processor';

@Injectable()
export class QueueService {
  constructor(
    @InjectQueue('email') private emailQueue: Queue,
    @InjectQueue('file-processing') private fileQueue: Queue,
    @InjectQueue('document-processing') private documentProcessingQueue: Queue,
  ) {}

  async addEmailJob(data: any) {
    return await this.emailQueue.add('send-email', data, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    });
  }

  async addFileProcessingJob(data: any) {
    return await this.fileQueue.add('process-file', data, {
      attempts: 2,
      removeOnComplete: true,
      removeOnFail: false,
    });
  }

  async addDocumentProcessingJob(data: DocumentProcessingJobData) {
    return await this.documentProcessingQueue.add('check-processing-status', data, {
      delay: 2000, // Initial delay of 2 seconds
      attempts: 1,
      removeOnComplete: true,
      removeOnFail: false,
    });
  }

  async getJobStatus(queueName: string, jobId: string) {
    let queue: Queue;
    switch (queueName) {
      case 'email':
        queue = this.emailQueue;
        break;
      case 'file-processing':
        queue = this.fileQueue;
        break;
      case 'document-processing':
        queue = this.documentProcessingQueue;
        break;
      default:
        throw new Error(`Unknown queue: ${queueName}`);
    }

    const job = await queue.getJob(jobId);
    if (!job) return null;

    const state = await job.getState();
    const progress = job.progress();

    return {
      id: job.id,
      state,
      progress,
      data: job.data,
      failedReason: job.failedReason,
      finishedOn: job.finishedOn,
      processedOn: job.processedOn,
    };
  }
}
