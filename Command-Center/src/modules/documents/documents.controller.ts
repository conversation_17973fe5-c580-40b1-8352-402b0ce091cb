import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { VectorSearchService } from '../vector/services/vector-search.service';

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DocumentsService } from './documents.service';
import { CreateDocumentDto } from './dto/create-document.dto';
import { UpdateDocumentDto } from './dto/update-document.dto';
import { ProcessDocumentDto } from './dto/process-document.dto';
import { ProcessUrlDto } from './dto/process-url.dto';
import { VectorSearchService } from '../vector/services/vector-search.service';

// Multer configuration for file uploads
const multerConfig = {
  storage: diskStorage({
    destination: './uploads/documents',
    filename: (req, file, cb) => {
      const randomName = Array(32).fill(null).map(() => (Math.round(Math.random() * 16)).toString(16)).join('');
      cb(null, `${randomName}${extname(file.originalname)}`);
    },
  }),
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'text/markdown',
      'text/html',
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new BadRequestException('File type not supported'), false);
    }
  },
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
};

@ApiTags('documents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('documents')
export class DocumentsController {
  constructor(
    private readonly documentsService: DocumentsService,
    private readonly vectorSearchService: VectorSearchService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new document' })
  create(@Body() createDocumentDto: CreateDocumentDto, @Request() req) {
    return this.documentsService.create(createDocumentDto, req.user.userId);
  }

  @Post('upload')
  @ApiOperation({ summary: 'Upload and process a document file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        extractMetadata: {
          type: 'boolean',
          default: true,
        },
        chunkSize: {
          type: 'number',
          default: 1000,
        },
        chunkOverlap: {
          type: 'number',
          default: 200,
        },
        folderId: {
          type: 'string',
          nullable: true,
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file', multerConfig))
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() processDocumentDto: ProcessDocumentDto,
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.documentsService.uploadAndProcessDocument(
      file,
      processDocumentDto,
      req.user.userId,
    );
  }

  @Post('upload/batch')
  @ApiOperation({ summary: 'Upload and process multiple document files' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        extractMetadata: {
          type: 'boolean',
          default: true,
        },
        chunkSize: {
          type: 'number',
          default: 1000,
        },
        chunkOverlap: {
          type: 'number',
          default: 200,
        },
        folderId: {
          type: 'string',
          nullable: true,
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
        },
      },
    },
  })
  @UseInterceptors(FilesInterceptor('files', 10, multerConfig))
  async uploadDocumentsBatch(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() processDocumentDto: ProcessDocumentDto,
    @Request() req,
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    return this.documentsService.uploadAndProcessDocumentsBatch(
      files,
      processDocumentDto,
      req.user.userId,
    );
  }

  @Post('process/url')
  @ApiOperation({ summary: 'Process a URL and extract content' })
  async processUrl(
    @Body() processUrlDto: ProcessUrlDto,
    @Request() req,
  ) {
    return this.documentsService.processUrl(processUrlDto, req.user.userId);
  }

  @Get('processing/:jobId')
  @ApiOperation({ summary: 'Get processing status for a job' })
  async getProcessingStatus(@Param('jobId') jobId: string) {
    return this.documentsService.getProcessingStatus(jobId);
  }

  @Get('folders')
  @ApiOperation({ summary: 'Get document folders for the user' })
  async getFolders(@Request() req) {
    return this.documentsService.getFolders(req.user.userId);
  }

  @Post('folders')
  @ApiOperation({ summary: 'Create a new folder' })
  async createFolder(
    @Body() createFolderDto: { name: string; parentId?: string },
    @Request() req,
  ) {
    return this.documentsService.createFolder(createFolderDto, req.user.userId);
  }

  @Get('tags')
  @ApiOperation({ summary: 'Get document tags for the user' })
  async getTags(@Request() req) {
    return this.documentsService.getTags(req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all documents for the authenticated user' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'folderId', required: false, type: String })
  @ApiQuery({ name: 'tags', required: false, type: String })
  @ApiQuery({ name: 'documentType', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  findAll(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('folderId') folderId?: string,
    @Query('tags') tags?: string,
    @Query('documentType') documentType?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Request() req,
  ) {
    const filters = {
      folderId,
      tags: tags ? tags.split(',') : undefined,
      documentType: documentType ? documentType.split(',') : undefined,
      startDate,
      endDate,
    };

    return this.documentsService.findAllWithFilters(
      req.user.userId,
      page,
      limit,
      filters,
    );
  }

  @Get('search')
  @ApiOperation({ summary: 'Search documents by text' })
  @ApiQuery({ name: 'q', required: true, type: String })
  searchDocuments(@Query('q') query: string, @Request() req) {
    return this.documentsService.searchDocuments(query, req.user.userId);
  }

  @Get('search/semantic')
  @ApiOperation({ summary: 'Semantic search using vector similarity' })
  @ApiQuery({ name: 'q', required: true, type: String })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'threshold', required: false, type: Number })
  async semanticSearch(
    @Query('q') query: string,
    @Query('limit') limit: number = 10,
    @Query('threshold') threshold: number = 0.7,
    @Request() req,
  ) {
    return this.vectorSearchService.searchDocuments(query, {
      limit,
      scoreThreshold: threshold,
      ownerId: req.user.userId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific document' })
  findOne(@Param('id') id: string) {
    return this.documentsService.findOne(id);
  }

  @Get(':id/chunks')
  @ApiOperation({ summary: 'Get document chunks for a specific document' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getDocumentChunks(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return this.documentsService.getDocumentChunks(id, page, limit);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a document' })
  update(
    @Param('id') id: string,
    @Body() updateDocumentDto: UpdateDocumentDto,
  ) {
    return this.documentsService.update(id, updateDocumentDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a document' })
  remove(@Param('id') id: string) {
    return this.documentsService.remove(id);
  }

  @Post(':id/reprocess')
  @ApiOperation({ summary: 'Reprocess a document' })
  async reprocessDocument(
    @Param('id') id: string,
    @Body() processDocumentDto: ProcessDocumentDto,
  ) {
    return this.documentsService.reprocessDocument(id, processDocumentDto);
  }
}
