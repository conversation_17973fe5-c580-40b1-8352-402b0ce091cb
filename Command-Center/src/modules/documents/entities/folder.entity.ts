import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Document } from './document.entity';

@Entity('folders')
@Index('idx_folder_owner', ['ownerId'])
@Index('idx_folder_parent', ['parentId'])
@Index('idx_folder_name', ['name'])
export class Folder {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 7, default: '#3B82F6' })
  color: string;

  @Column({ nullable: true })
  parentId: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  path: string;

  @Column({ type: 'int', default: 0 })
  documentCount: number;

  @Column({ type: 'boolean', default: false })
  isSystem: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Foreign Keys
  @Column({ type: 'uuid' })
  ownerId: string;

  // Relations
  @ManyToOne(() => User, (user) => user.folders)
  owner: User;

  @ManyToOne(() => Folder, { nullable: true })
  parent: Folder;

  @OneToMany(() => Folder, (folder) => folder.parent)
  children: Folder[];

  @OneToMany(() => Document, (document) => document.folder)
  documents: Document[];
}