import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryColumn,
  CreateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { Document } from './document.entity';

@Entity('document_chunks')
@Index('idx_chunk_document', ['documentId'])
@Index('idx_chunk_index', ['documentId', 'chunkIndex'])
export class DocumentChunk {
  @PrimaryColumn('varchar', { length: 64 })
  id: string;

  @Column({ type: 'uuid' })
  documentId: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'int' })
  chunkIndex: number;

  @Column({ type: 'int', default: 0 })
  startChar: number;

  @Column({ type: 'int', default: 0 })
  endChar: number;

  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  // Relations
  @ManyToOne(() => Document, (document) => document.chunks, {
    onDelete: 'CASCADE',
  })
  document: Document;
}