import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinTable,
  Index,
  BeforeInsert,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { File } from '../../files/entities/file.entity';
import { DocumentVersion } from './document-version.entity';
import { DocumentShare } from './document-share.entity';
import { DocumentChunk } from './document-chunk.entity';
import { Folder } from './folder.entity';

export enum DocumentType {
  TEXT = 'text',
  SPREADSHEET = 'spreadsheet',
  PRESENTATION = 'presentation',
  PDF = 'pdf',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  OTHER = 'other',
}

export enum DocumentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

@Entity('documents')
@Index('idx_document_owner', ['ownerId'])
@Index('idx_document_status', ['status'])
@Index('idx_document_type', ['type'])
@Index('idx_document_created', ['createdAt'])
@Index('idx_document_updated', ['updatedAt'])
@Index('idx_document_title_fulltext', ['title'], { fulltext: true })
@Index('idx_document_composite', ['ownerId', 'status', 'createdAt'])
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index('idx_document_title')
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  content: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    default: DocumentType.TEXT,
  })
  type: DocumentType;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.DRAFT,
  })
  status: DocumentStatus;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index('idx_document_mimetype')
  mimeType: string;

  @Column({ type: 'int', default: 0 })
  size: number;

  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @Column({ type: 'varchar', array: true, default: [] })
  @Index('idx_document_tags')
  tags: string[];

  @Column({ type: 'int', default: 1 })
  version: number;

  @Column({ default: false })
  isPublic: boolean;

  @Column({ default: false })
  isTemplate: boolean;

  @Column({ nullable: true })
  @Index('idx_document_parent')
  parentId: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  path: string;

  @Column({ type: 'int', default: 0 })
  viewCount: number;

  @Column({ type: 'int', default: 0 })
  downloadCount: number;

  @Column({ nullable: true })
  lastAccessedAt: Date;

  @Column({ type: 'varchar', length: 64, nullable: true })
  @Index('idx_document_checksum')
  checksum: string;

  @Column({ type: 'tsvector', nullable: true, select: false })
  searchVector: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  deletedAt: Date;

  // Foreign Keys
  @Column({ type: 'uuid' })
  ownerId: string;

  @Column({ type: 'uuid', nullable: true })
  lastModifiedById: string;

  @Column({ type: 'uuid', nullable: true })
  folderId: string;

  // Relations
  @ManyToOne(() => User, (user) => user.documents)
  owner: User;

  @ManyToOne(() => User)
  lastModifiedBy: User;

  @ManyToOne(() => Document, { nullable: true })
  parent: Document;

  @OneToMany(() => Document, (document) => document.parent)
  children: Document[];

  @OneToMany(() => DocumentVersion, (version) => version.document)
  versions: DocumentVersion[];

  @OneToMany(() => DocumentShare, (share) => share.document)
  shares: DocumentShare[];

  @OneToMany(() => DocumentChunk, (chunk) => chunk.document)
  chunks: DocumentChunk[];

  @ManyToOne(() => Folder, (folder) => folder.documents, { nullable: true })
  folder: Folder;

  @ManyToMany(() => File)
  @JoinTable({
    name: 'document_files',
    joinColumn: { name: 'document_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'file_id', referencedColumnName: 'id' },
  })
  attachments: File[];

  @ManyToMany(() => User)
  @JoinTable({
    name: 'document_collaborators',
    joinColumn: { name: 'document_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  collaborators: User[];

  @BeforeInsert()
  generatePath() {
    if (this.parentId) {
      // Path will be updated after insert to include parent path
      this.path = '';
    } else {
      this.path = `/${this.id}`;
    }
  }
}
