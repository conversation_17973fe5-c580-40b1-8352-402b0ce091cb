import { IsUrl, IsBoolean, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, <PERSON>String, <PERSON>A<PERSON>y, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ProcessUrlDto {
  @ApiProperty({
    description: 'URL to process and extract content from',
    example: 'https://example.com/article',
  })
  @IsUrl({}, { message: 'Please provide a valid URL' })
  url: string;

  @ApiProperty({
    description: 'Whether to extract metadata from the URL',
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  extractMetadata?: boolean = true;

  @ApiProperty({
    description: 'Size of each text chunk in characters',
    default: 1000,
    minimum: 100,
    maximum: 5000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(5000)
  chunkSize?: number = 1000;

  @ApiProperty({
    description: 'Number of characters to overlap between chunks',
    default: 200,
    minimum: 0,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  chunkOverlap?: number = 200;

  @ApiProperty({
    description: 'ID of the folder to place the document in',
    required: false,
  })
  @IsOptional()
  @IsString()
  folderId?: string;

  @ApiProperty({
    description: 'Tags to apply to the document',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[] = [];

  @ApiProperty({
    description: 'Title override for the document (if not provided, page title will be used)',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Description for the document',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}