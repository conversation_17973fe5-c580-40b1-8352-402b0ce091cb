import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class ProcessDocumentDto {
  @ApiProperty({
    description: 'Whether to extract metadata from the document',
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  extractMetadata?: boolean = true;

  @ApiProperty({
    description: 'Size of each text chunk in characters',
    default: 1000,
    minimum: 100,
    maximum: 5000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(5000)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return parseInt(value, 10);
    }
    return value;
  })
  chunkSize?: number = 1000;

  @ApiProperty({
    description: 'Number of characters to overlap between chunks',
    default: 200,
    minimum: 0,
    maximum: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return parseInt(value, 10);
    }
    return value;
  })
  chunkOverlap?: number = 200;

  @ApiProperty({
    description: 'ID of the folder to place the document in',
    required: false,
  })
  @IsOptional()
  @IsString()
  folderId?: string;

  @ApiProperty({
    description: 'Tags to apply to the document',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(tag => tag.trim());
    }
    return value;
  })
  tags?: string[] = [];

  @ApiProperty({
    description: 'Title override for the document (if not provided, filename will be used)',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Description for the document',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}