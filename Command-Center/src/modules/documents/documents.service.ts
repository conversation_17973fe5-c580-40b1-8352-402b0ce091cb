import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '../redis/redis.service';
import { QueueService } from '../queue/queue.service';
import { VectorIntegrationService } from '../vector/services/vector-integration.service';
import { Document, DocumentType } from './entities/document.entity';

import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../redis/redis.service';
import { QueueService } from '../queue/queue.service';
import { VectorIntegrationService } from '../vector/services/vector-integration.service';
import { Document, DocumentType } from './entities/document.entity';
import { DocumentChunk } from './entities/document-chunk.entity';
import { Folder } from './entities/folder.entity';
import { Tag } from './entities/tag.entity';
import { ProcessDocumentDto } from './dto/process-document.dto';
import { ProcessUrlDto } from './dto/process-url.dto';
import { firstValueFrom } from 'rxjs';
import { unlinkSync } from 'fs';
import { join } from 'path';

@Injectable()
export class DocumentsService {
  private readonly logger = new Logger(DocumentsService.name);
  private readonly pythonServiceUrl: string;

  constructor(
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(DocumentChunk)
    private readonly chunkRepository: Repository<DocumentChunk>,
    @InjectRepository(Folder)
    private readonly folderRepository: Repository<Folder>,
    @InjectRepository(Tag)
    private readonly tagRepository: Repository<Tag>,
    private readonly redisService: RedisService,
    private readonly queueService: QueueService,
    private readonly vectorIntegrationService: VectorIntegrationService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.pythonServiceUrl = this.configService.get<string>(
      'PYTHON_SERVICE_URL',
      'http://localhost:8001'
    );
  }

  async findAllWithFilters(
    ownerId: string,
    page = 1,
    limit = 10,
    filters: {
      folderId?: string;
      tags?: string[];
      documentType?: string[];
      startDate?: string;
      endDate?: string;
    } = {},
  ) {
    const cacheKey = `documents:${ownerId}:${page}:${limit}:${JSON.stringify(filters)}`;

    // Try to get from cache first
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const queryBuilder = this.documentRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.owner', 'owner')
      .leftJoinAndSelect('document.folder', 'folder')
      .where('document.ownerId = :ownerId', { ownerId })
      .andWhere('document.deletedAt IS NULL');

    // Apply filters
    if (filters.folderId) {
      queryBuilder.andWhere('document.folderId = :folderId', {
        folderId: filters.folderId,
      });
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('document.tags && :tags', { tags: filters.tags });
    }

    if (filters.documentType && filters.documentType.length > 0) {
      queryBuilder.andWhere('document.type IN (:...types)', {
        types: filters.documentType,
      });
    }

    if (filters.startDate) {
      queryBuilder.andWhere('document.createdAt >= :startDate', {
        startDate: filters.startDate,
      });
    }

    if (filters.endDate) {
      queryBuilder.andWhere('document.createdAt <= :endDate', {
        endDate: filters.endDate,
      });
    }

    const [documents, total] = await queryBuilder
      .orderBy('document.updatedAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    const result = {
      documents,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };

    // Cache for 5 minutes
    await this.redisService.set(cacheKey, result, 300);
    return result;
  }

  async findAll(ownerId: string, page = 1, limit = 10) {
    return this.findAllWithFilters(ownerId, page, limit);
  }

  async findOne(id: string) {
    const cacheKey = `document:${id}`;

    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      // Update view count asynchronously
      this.incrementViewCount(id);
      return cached;
    }

    const document = await this.documentRepository.findOne({
      where: { id, deletedAt: null },
      relations: ['owner', 'versions', 'attachments', 'folder'],
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Cache for 15 minutes
    await this.redisService.set(cacheKey, document, 900);

    // Update view count asynchronously
    this.incrementViewCount(id);

    return document;
  }

  async uploadAndProcessDocument(
    file: Express.Multer.File,
    processDto: ProcessDocumentDto,
    ownerId: string,
  ): Promise<{ document: Document; jobId: string }> {
    try {
      // Create document record
      const document = await this.createDocumentFromFile(file, processDto, ownerId);

      // Send file to Python service for processing
      const formData = new FormData();
      const fileBuffer = require('fs').readFileSync(file.path);
      const blob = new Blob([fileBuffer], { type: file.mimetype });
      formData.append('file', blob, file.originalname);
      formData.append('extractMetadata', processDto.extractMetadata?.toString() || 'true');
      formData.append('chunkSize', processDto.chunkSize?.toString() || '1000');
      formData.append('chunkOverlap', processDto.chunkOverlap?.toString() || '200');

      const response = await firstValueFrom(
        this.httpService.post(`${this.pythonServiceUrl}/process/file`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 30000, // 30 seconds
        }),
      );

      const { job_id: jobId } = response.data;

      // Store job reference in document metadata
      document.metadata = {
        ...document.metadata,
        processingJobId: jobId,
        processingStatus: 'queued',
      };
      await this.documentRepository.save(document);

      // Queue job to check processing status
      await this.queueService.addDocumentProcessingJob({
        documentId: document.id,
        jobId,
        pythonServiceUrl: this.pythonServiceUrl,
      });

      // Clean up uploaded file
      try {
        unlinkSync(file.path);
      } catch (error) {
        console.warn('Failed to delete uploaded file:', error);
      }

      return { document, jobId };
    } catch (error) {
      // Clean up uploaded file on error
      try {
        unlinkSync(file.path);
      } catch (cleanupError) {
        console.warn('Failed to delete uploaded file after error:', cleanupError);
      }
      throw new BadRequestException(`Failed to process document: ${error.message}`);
    }
  }

  async uploadAndProcessDocumentsBatch(
    files: Express.Multer.File[],
    processDto: ProcessDocumentDto,
    ownerId: string,
  ): Promise<{ documents: Document[]; jobIds: string[] }> {
    const results = await Promise.allSettled(
      files.map(file => this.uploadAndProcessDocument(file, processDto, ownerId))
    );

    const documents: Document[] = [];
    const jobIds: string[] = [];
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        documents.push(result.value.document);
        jobIds.push(result.value.jobId);
      } else {
        errors.push(`File ${files[index].originalname}: ${result.reason.message}`);
      }
    });

    if (errors.length > 0 && documents.length === 0) {
      throw new BadRequestException(`All files failed to process: ${errors.join(', ')}`);
    }

    return { documents, jobIds };
  }

  async processUrl(processDto: ProcessUrlDto, ownerId: string): Promise<{ document: Document; jobId: string }> {
    try {
      // Send URL to Python service for processing
      const response = await firstValueFrom(
        this.httpService.post(`${this.pythonServiceUrl}/process/url`, processDto, {
          timeout: 30000,
        }),
      );

      const { job_id: jobId } = response.data;

      // Create document record
      const document = await this.createDocumentFromUrl(processDto, ownerId, jobId);

      // Queue job to check processing status
      await this.queueService.addDocumentProcessingJob({
        documentId: document.id,
        jobId,
        pythonServiceUrl: this.pythonServiceUrl,
      });

      return { document, jobId };
    } catch (error) {
      throw new BadRequestException(`Failed to process URL: ${error.message}`);
    }
  }

  async getProcessingStatus(jobId: string) {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.pythonServiceUrl}/status/${jobId}`, {
          timeout: 10000,
        }),
      );

      return response.data;
    } catch (error) {
      throw new BadRequestException(`Failed to get processing status: ${error.message}`);
    }
  }

  async updateDocumentWithProcessingResult(documentId: string, processingResult: any) {
    const document = await this.documentRepository.findOne({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Update document with processed content
    document.content = processingResult.content;
    document.metadata = {
      ...document.metadata,
      ...processingResult.metadata,
      processingStatus: 'completed',
      processedAt: new Date().toISOString(),
      wordCount: processingResult.word_count,
      charCount: processingResult.char_count,
      processingTime: processingResult.processing_time,
    };

    await this.documentRepository.save(document);

    // Save chunks
    if (processingResult.chunks && processingResult.chunks.length > 0) {
      await this.saveDocumentChunks(documentId, processingResult.chunks);
    }

    // Index document in vector database
    await this.vectorIntegrationService.indexDocument(document);

    // Invalidate caches
    await this.redisService.del(`document:${documentId}`);
    await this.invalidateUserDocumentsCache(document.ownerId);

    return document;
  }

  async getDocumentChunks(documentId: string, page = 1, limit = 10) {
    const [chunks, total] = await this.chunkRepository.findAndCount({
      where: { documentId },
      order: { chunkIndex: 'ASC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      chunks,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getFolders(ownerId: string): Promise<Folder[]> {
    const cacheKey = `folders:${ownerId}`;
    
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const folders = await this.folderRepository.find({
      where: { ownerId },
      order: { name: 'ASC' },
    });

    await this.redisService.set(cacheKey, folders, 300);
    return folders;
  }

  async createFolder(
    createFolderDto: { name: string; parentId?: string },
    ownerId: string,
  ): Promise<Folder> {
    const folder = this.folderRepository.create({
      ...createFolderDto,
      ownerId,
    });

    const saved = await this.folderRepository.save(folder);
    
    // Invalidate cache
    await this.redisService.del(`folders:${ownerId}`);
    
    return saved;
  }

  async getTags(ownerId: string): Promise<Tag[]> {
    const cacheKey = `tags:${ownerId}`;
    
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const tags = await this.tagRepository.find({
      where: { ownerId },
      order: { name: 'ASC' },
    });

    await this.redisService.set(cacheKey, tags, 300);
    return tags;
  }

  async reprocessDocument(documentId: string, processDto: ProcessDocumentDto): Promise<{ jobId: string }> {
    const document = await this.documentRepository.findOne({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // If document has a file path, reprocess the file
    if (document.metadata?.filePath) {
      const formData = new FormData();
      const fileBuffer = require('fs').readFileSync(document.metadata.filePath);
      const blob = new Blob([fileBuffer], { type: document.mimeType });
      formData.append('file', blob, document.title);
      formData.append('extractMetadata', processDto.extractMetadata?.toString() || 'true');
      formData.append('chunkSize', processDto.chunkSize?.toString() || '1000');
      formData.append('chunkOverlap', processDto.chunkOverlap?.toString() || '200');

      const response = await firstValueFrom(
        this.httpService.post(`${this.pythonServiceUrl}/process/file`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 30000,
        }),
      );

      const { job_id: jobId } = response.data;

      // Update document metadata
      document.metadata = {
        ...document.metadata,
        processingJobId: jobId,
        processingStatus: 'queued',
      };
      await this.documentRepository.save(document);

      return { jobId };
    }

    // If document was from URL, reprocess the URL
    if (document.metadata?.url) {
      const response = await firstValueFrom(
        this.httpService.post(`${this.pythonServiceUrl}/process/url`, {
          url: document.metadata.url,
          extractMetadata: processDto.extractMetadata,
          chunkSize: processDto.chunkSize,
          chunkOverlap: processDto.chunkOverlap,
        }, {
          timeout: 30000,
        }),
      );

      const { job_id: jobId } = response.data;

      // Update document metadata
      document.metadata = {
        ...document.metadata,
        processingJobId: jobId,
        processingStatus: 'queued',
      };
      await this.documentRepository.save(document);

      return { jobId };
    }

    throw new BadRequestException('Document cannot be reprocessed (no source file or URL)');
  }

  private async createDocumentFromFile(
    file: Express.Multer.File,
    processDto: ProcessDocumentDto,
    ownerId: string,
  ): Promise<Document> {
    const documentType = this.getDocumentTypeFromMimeType(file.mimetype);
    
    const document = this.documentRepository.create({
      title: processDto.title || file.originalname,
      description: processDto.description,
      type: documentType,
      mimeType: file.mimetype,
      size: file.size,
      ownerId,
      folderId: processDto.folderId || null,
      tags: processDto.tags || [],
      metadata: {
        originalFilename: file.originalname,
        filePath: file.path,
        uploadedAt: new Date().toISOString(),
      },
    });

    return this.documentRepository.save(document);
  }

  private async createDocumentFromUrl(
    processDto: ProcessUrlDto,
    ownerId: string,
    jobId: string,
  ): Promise<Document> {
    const url = new URL(processDto.url);
    const title = processDto.title || `Content from ${url.hostname}`;
    
    const document = this.documentRepository.create({
      title,
      description: processDto.description,
      type: DocumentType.OTHER,
      mimeType: 'text/html',
      ownerId,
      folderId: processDto.folderId || null,
      tags: processDto.tags || [],
      metadata: {
        url: processDto.url,
        domain: url.hostname,
        processingJobId: jobId,
        processingStatus: 'queued',
        crawledAt: new Date().toISOString(),
      },
    });

    return this.documentRepository.save(document);
  }

  private async saveDocumentChunks(documentId: string, chunks: any[]) {
    // Remove existing chunks
    await this.chunkRepository.delete({ documentId });

    // Save new chunks
    const chunkEntities = chunks.map((chunk, index) =>
      this.chunkRepository.create({
        id: chunk.id,
        documentId,
        content: chunk.content,
        chunkIndex: index,
        startChar: chunk.start_char,
        endChar: chunk.end_char,
        metadata: chunk.metadata || {},
      }),
    );

    await this.chunkRepository.save(chunkEntities);
  }

  private getDocumentTypeFromMimeType(mimeType: string): DocumentType {
    const typeMapping = {
      'application/pdf': DocumentType.PDF,
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': DocumentType.TEXT,
      'application/msword': DocumentType.TEXT,
      'text/plain': DocumentType.TEXT,
      'text/markdown': DocumentType.TEXT,
      'text/html': DocumentType.TEXT,
      'image/jpeg': DocumentType.IMAGE,
      'image/png': DocumentType.IMAGE,
      'image/gif': DocumentType.IMAGE,
      'video/mp4': DocumentType.VIDEO,
      'audio/mpeg': DocumentType.AUDIO,
    };

    return typeMapping[mimeType] || DocumentType.OTHER;
  }

  async create(createDocumentDto: any, ownerId: string): Promise<Document> {
    const document = this.documentRepository.create({
      ...createDocumentDto,
      ownerId,
    });

    const saved = await this.documentRepository.save(document);

    // Ensure we have a single document (TypeORM save can return array or single)
    const result = Array.isArray(saved) ? saved[0] : saved;

    // Invalidate cache
    await this.invalidateUserDocumentsCache(ownerId);

    // Queue document processing if needed
    if (
      result.type === DocumentType.PDF ||
      result.type === DocumentType.IMAGE
    ) {
      await this.queueService.addFileProcessingJob({
        documentId: result.id,
        operation: 'extract-text',
      });
    }

    // Index document in vector database
    await this.vectorIntegrationService.indexDocument(result);

    return result;
  }

  async update(id: string, updateDocumentDto: any) {
    const document = await this.documentRepository.findOne({
      where: { id, deletedAt: null },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    Object.assign(document, updateDocumentDto);
    const updated = await this.documentRepository.save(document);

    // Invalidate specific document cache
    await this.redisService.del(`document:${id}`);

    // Invalidate user documents cache
    await this.invalidateUserDocumentsCache(document.ownerId);

    // Update document in vector database
    await this.vectorIntegrationService.updateDocumentVector(updated);

    return updated;
  }

  async remove(id: string) {
    const document = await this.documentRepository.findOne({
      where: { id, deletedAt: null },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    document.deletedAt = new Date();
    await this.documentRepository.save(document);

    // Remove chunks
    await this.chunkRepository.delete({ documentId: id });

    // Invalidate caches
    await this.redisService.del(`document:${id}`);
    await this.invalidateUserDocumentsCache(document.ownerId);

    // Remove from vector database
    await this.vectorIntegrationService.removeDocumentVector(id);

    return { message: 'Document deleted successfully' };
  }

  private async incrementViewCount(documentId: string) {
    try {
      await this.documentRepository.increment(
        { id: documentId },
        'viewCount',
        1,
      );

      await this.documentRepository.update(
        { id: documentId },
        { lastAccessedAt: new Date() },
      );
    } catch (error) {
      // Log error but don't fail the request
      console.error('Failed to increment view count:', error);
    }
  }

  private async invalidateUserDocumentsCache(ownerId: string) {
    // This is a simplified approach - in production you might use pattern matching
    const cacheKeys = [
      `documents:${ownerId}:1:10`,
      `documents:${ownerId}:1:20`,
      `documents:${ownerId}:2:10`,
      // Add more patterns as needed
    ];

    await Promise.all(cacheKeys.map((key) => this.redisService.del(key)));
  }

  async searchDocuments(query: string, ownerId: string) {
    const cacheKey = `search:${ownerId}:${query}`;

    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const documents = await this.documentRepository
      .createQueryBuilder('document')
      .where('document.ownerId = :ownerId', { ownerId })
      .andWhere('document.deletedAt IS NULL')
      .andWhere(
        'document.title ILIKE :query OR document.content ILIKE :query',
        { query: `%${query}%` },
      )
      .orderBy('document.updatedAt', 'DESC')
      .limit(50)
      .getMany();

    // Cache search results for 2 minutes
    await this.redisService.set(cacheKey, documents, 120);

    return documents;
  }

  // Additional method for maintenance processor
  async findPopular(limit: number = 10): Promise<Document[]> {
    return this.documentRepository.find({
      order: { viewCount: 'DESC' },
      take: limit,
      where: { deletedAt: null },
    });
  }

  async updateDocumentProcessingError(documentId: string, error: string): Promise<void> {
    try {
      const document = await this.documentRepository.findOne({
        where: { id: documentId },
      });

      if (document) {
        document.metadata = {
          ...document.metadata,
          processingStatus: 'failed',
          processingError: error,
          failedAt: new Date().toISOString(),
        };

        await this.documentRepository.save(document);

        // Invalidate caches
        await this.redisService.del(`document:${documentId}`);
        await this.invalidateUserDocumentsCache(document.ownerId);
      }
    } catch (err) {
      this.logger?.error(`Failed to update document processing error: ${err.message}`);
    }
  }
}
