import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { VendorService } from '../services/vendor.service';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { PermissionsGuard } from '../../auth/guards/permissions.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { RequirePermissions } from '../../auth/decorators/permissions.decorator';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { User } from '../../users/entities/user.entity';
import {
  CreateVendorDto,
  UpdateVendorDto,
  VendorResponseDto,
} from '../dto/vendor.dto';
import { VendorFiltersDto } from '../dto/vendor-filters.dto';

@ApiTags('Vendors')
@Controller('vendors')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class VendorController {
  constructor(private readonly vendorService: VendorService) {}

  // =============================================================================
  // VENDOR MANAGEMENT
  // =============================================================================

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager', 'vendor_manager')
  @RequirePermissions('vendor:create')
  @ApiOperation({ summary: 'Create a new vendor' })
  @ApiResponse({
    status: 201,
    description: 'Vendor created successfully',
    type: VendorResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid vendor data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createVendor(
    @Body(ValidationPipe) createVendorDto: CreateVendorDto,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.vendorService.createVendor(createVendorDto);
  }

  @Get()
  @RequirePermissions('vendor:read')
  @ApiOperation({ summary: 'Get all vendors with filters' })
  @ApiResponse({
    status: 200,
    description: 'Vendors retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        vendors: {
          type: 'array',
          items: { $ref: '#/components/schemas/VendorResponseDto' },
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term for vendor name/contact',
  })
  @ApiQuery({
    name: 'category',
    required: false,
    description: 'Vendor category filter',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Vendor status filter',
  })
  @ApiQuery({
    name: 'minRating',
    required: false,
    description: 'Minimum rating filter',
  })
  @ApiQuery({
    name: 'maxRating',
    required: false,
    description: 'Maximum rating filter',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async getVendors(
    @Query() filters: VendorFiltersDto,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.vendorService.getVendors(filters);
  }

  @Get(':id')
  @RequirePermissions('vendor:read')
  @ApiOperation({ summary: 'Get vendor by ID' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({
    status: 200,
    description: 'Vendor retrieved successfully',
    type: VendorResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Vendor not found' })
  async getVendorById(@Param('id', ParseUUIDPipe) id: string): Promise<any> {
    return await this.vendorService.getVendorById(id);
  }

  @Put(':id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager', 'vendor_manager')
  @RequirePermissions('vendor:update')
  @ApiOperation({ summary: 'Update vendor' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({
    status: 200,
    description: 'Vendor updated successfully',
    type: VendorResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid vendor data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Vendor not found' })
  async updateVendor(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateVendorDto: UpdateVendorDto,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.vendorService.updateVendor(id, updateVendorDto);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager')
  @RequirePermissions('vendor:delete')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete vendor' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({ status: 204, description: 'Vendor deleted successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Vendor not found' })
  @ApiResponse({
    status: 400,
    description: 'Cannot delete vendor with active proposals',
  })
  async deleteVendor(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<void> {
    await this.vendorService.deleteVendor(id);
  }

  // =============================================================================
  // VENDOR PERFORMANCE & ANALYTICS
  // =============================================================================

  @Get(':id/performance')
  @RequirePermissions('vendor:read')
  @ApiOperation({ summary: 'Get vendor performance metrics' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({
    status: 200,
    description: 'Vendor performance retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        vendor: { $ref: '#/components/schemas/VendorResponseDto' },
        proposalStats: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              count: { type: 'number' },
              averageCost: { type: 'number' },
            },
          },
        },
        reviewStats: {
          type: 'object',
          properties: {
            count: { type: 'number' },
            averageRating: { type: 'number' },
            minRating: { type: 'number' },
            maxRating: { type: 'number' },
          },
        },
        performanceScore: { type: 'number' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Vendor not found' })
  async getVendorPerformance(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<any> {
    return await this.vendorService.getVendorPerformance(id);
  }

  @Put(':id/rating')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager', 'vendor_manager')
  @RequirePermissions('vendor:update')
  @ApiOperation({ summary: 'Update vendor rating based on reviews' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({
    status: 200,
    description: 'Vendor rating updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Vendor not found' })
  async updateVendorRating(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<{ message: string }> {
    await this.vendorService.updateVendorRating(id);
    return { message: 'Vendor rating updated successfully' };
  }

  @Get(':id/trends')
  @RequirePermissions('vendor:analytics')
  @ApiOperation({ summary: 'Get vendor performance trends' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiQuery({
    name: 'timeRange',
    required: false,
    description: 'Time range (30d, 90d, 1y)',
  })
  @ApiResponse({
    status: 200,
    description: 'Vendor trends retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        proposalTrends: { type: 'array' },
        ratingTrends: { type: 'array' },
        costTrends: { type: 'array' },
      },
    },
  })
  async getVendorTrends(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('timeRange') timeRange?: string,
  ): Promise<any> {
    return await this.vendorService.getVendorTrends(id, timeRange);
  }

  // =============================================================================
  // VENDOR WORKFLOW MANAGEMENT
  // =============================================================================

  @Post(':id/onboard')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager', 'vendor_manager')
  @RequirePermissions('vendor:manage')
  @ApiOperation({ summary: 'Initiate vendor onboarding process' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({
    status: 200,
    description: 'Vendor onboarding initiated successfully',
    schema: {
      type: 'object',
      properties: {
        workflowId: { type: 'string' },
        status: { type: 'string' },
        nextSteps: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async initiateOnboarding(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.vendorService.initiateVendorOnboarding(id);
  }

  @Post(':id/approve')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager')
  @RequirePermissions('vendor:approve')
  @ApiOperation({ summary: 'Approve vendor' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({
    status: 200,
    description: 'Vendor approved successfully',
    schema: {
      type: 'object',
      properties: {
        vendorId: { type: 'string' },
        status: { type: 'string' },
        approvedBy: { type: 'string' },
        approvedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  async approveVendor(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
  ): Promise<any> {
    return await this.vendorService.approveVendor(id, user.id);
  }

  @Post(':id/suspend')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager')
  @RequirePermissions('vendor:suspend')
  @ApiOperation({ summary: 'Suspend vendor' })
  @ApiParam({ name: 'id', description: 'Vendor ID' })
  @ApiResponse({
    status: 200,
    description: 'Vendor suspended successfully',
    schema: {
      type: 'object',
      properties: {
        vendorId: { type: 'string' },
        status: { type: 'string' },
        suspendedBy: { type: 'string' },
        suspendedAt: { type: 'string', format: 'date-time' },
        reason: { type: 'string' },
      },
    },
  })
  async suspendVendor(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { reason: string },
    @GetUser() user: User,
  ): Promise<any> {
    return await this.vendorService.suspendVendor(id, body.reason, user.id);
  }

  // =============================================================================
  // VENDOR CATEGORIES & STATISTICS
  // =============================================================================

  @Get('analytics/categories')
  @RequirePermissions('vendor:analytics')
  @ApiOperation({ summary: 'Get vendor categories with statistics' })
  @ApiResponse({
    status: 200,
    description: 'Vendor categories retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          category: { type: 'string' },
          count: { type: 'number' },
          averageRating: { type: 'number' },
        },
      },
    },
  })
  async getVendorCategories(): Promise<any[]> {
    return await this.vendorService.getVendorCategories();
  }

  @Get('analytics/statistics')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager', 'finance', 'analytics')
  @RequirePermissions('vendor:analytics')
  @ApiOperation({ summary: 'Get comprehensive vendor statistics' })
  @ApiResponse({
    status: 200,
    description: 'Vendor statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalVendors: { type: 'number' },
        activeVendors: { type: 'number' },
        averageRating: { type: 'number' },
        categoryBreakdown: { type: 'array' },
        statusBreakdown: { type: 'array' },
      },
    },
  })
  async getVendorStatistics(): Promise<any> {
    return await this.vendorService.getVendorStatistics();
  }

  @Get('analytics/top-performers')
  @RequirePermissions('vendor:analytics')
  @ApiOperation({ summary: 'Get top performing vendors' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of vendors to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Top performing vendors retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          vendor: { $ref: '#/components/schemas/VendorResponseDto' },
          performanceScore: { type: 'number' },
          totalProposals: { type: 'number' },
          approvalRate: { type: 'number' },
          averageRating: { type: 'number' },
        },
      },
    },
  })
  async getTopPerformingVendors(
    @Query('limit') limit?: number,
  ): Promise<any[]> {
    return await this.vendorService.getTopPerformingVendors(limit || 10);
  }

  @Get('analytics/overview')
  @UseGuards(RolesGuard)
  @Roles('admin', 'procurement_manager', 'finance', 'analytics')
  @RequirePermissions('vendor:analytics')
  @ApiOperation({ summary: 'Get vendor analytics overview' })
  @ApiQuery({
    name: 'timeRange',
    required: false,
    description: 'Time range for analytics (30d, 90d, 1y)',
  })
  @ApiResponse({
    status: 200,
    description: 'Vendor analytics overview retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalCost: { type: 'number' },
        totalProposals: { type: 'number' },
        averageProposalValue: { type: 'number' },
        approvalRate: { type: 'number' },
        categoryDistribution: { type: 'array' },
        monthlyTrends: { type: 'array' },
      },
    },
  })
  async getVendorAnalytics(
    @Query('timeRange') timeRange?: string,
  ): Promise<any> {
    return await this.vendorService.getVendorAnalytics(timeRange);
  }
}
