import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';

@Injectable()
export class VendorAnalyticsService {
  constructor(private prisma: PrismaService) {}

  async getVendorAnalytics(timeRange?: string) {
    // Simplified analytics implementation for MVP
    const [totalCost, totalProposals, categoryStats] = await Promise.all([
      this.prisma.proposal.aggregate({
        _sum: { totalCost: true },
      }),
      this.prisma.proposal.count(),
      this.prisma.vendor.groupBy({
        by: ['category'],
        _count: { category: true },
      }),
    ]);

    return {
      totalCost: totalCost._sum.totalCost || 0,
      totalProposals,
      averageProposalValue:
        totalProposals > 0
          ? (totalCost._sum.totalCost || 0) / totalProposals
          : 0,
      categoryDistribution: categoryStats,
      monthlyTrends: [], // Placeholder for MVP
    };
  }

  async getTopPerformingVendors(limit: number = 10) {
    const vendors = await this.prisma.vendor.findMany({
      include: {
        _count: {
          select: {
            proposals: true,
            reviews: true,
          },
        },
      },
      orderBy: {
        rating: 'desc',
      },
      take: limit,
    });

    return vendors.map((vendor) => ({
      vendor,
      performanceScore: vendor.rating * 20, // Simple calculation for MVP
      totalProposals: vendor._count.proposals,
      approvalRate: 0.8, // Placeholder
      averageRating: vendor.rating,
    }));
  }

  async getVendorTrends(vendorId: string, timeRange?: string) {
    // Simplified trends for MVP
    return {
      proposalTrends: [],
      ratingTrends: [],
      costTrends: [],
    };
  }
}
