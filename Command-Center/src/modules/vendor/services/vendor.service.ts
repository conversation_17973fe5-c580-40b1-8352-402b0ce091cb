import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { Vendor, Proposal, Review } from '@prisma/client';
import { CreateVendorDto, UpdateVendorDto } from '../dto/vendor.dto';
import { VendorFiltersDto } from '../dto/vendor-filters.dto';
import { VendorAnalyticsService } from './vendor-analytics.service';
import { VendorWorkflowService } from './vendor-workflow.service';

@Injectable()
export class VendorService {
  constructor(
    private prisma: PrismaService,
    private vendorAnalytics: VendorAnalyticsService,
    private vendorWorkflow: VendorWorkflowService,
  ) {}

  // =============================================================================
  // VENDOR MANAGEMENT
  // =============================================================================

  async createVendor(data: CreateVendorDto): Promise<Vendor> {
    // Validate unique company name
    const existingVendor = await this.prisma.vendor.findFirst({
      where: {
        companyName: {
          equals: data.companyName,
          mode: 'insensitive',
        },
      },
    });

    if (existingVendor) {
      throw new BadRequestException(
        'A vendor with this company name already exists',
      );
    }

    // Validate email format and uniqueness
    const existingEmail = await this.prisma.vendor.findFirst({
      where: {
        email: {
          equals: data.email,
          mode: 'insensitive',
        },
      },
    });

    if (existingEmail) {
      throw new BadRequestException('A vendor with this email already exists');
    }

    const vendor = await this.prisma.vendor.create({
      data: {
        ...data,
        phoneNumbers: data.phoneNumbers || [],
        certifications: data.certifications || [],
      },
      include: {
        proposals: {
          select: {
            id: true,
            title: true,
            status: true,
            totalCost: true,
            createdAt: true,
          },
        },
        reviews: {
          select: {
            id: true,
            rating: true,
            reviewerName: true,
            comment: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            proposals: true,
            reviews: true,
          },
        },
      },
    });

    // Log vendor creation activity
    await this.prisma.analytics.create({
      data: {
        event: 'vendor_created',
        properties: {
          vendorId: vendor.id,
          companyName: vendor.companyName,
          category: vendor.category,
          status: vendor.status,
        },
      },
    });

    return vendor;
  }

  async getVendors(filters: VendorFiltersDto) {
    const {
      search,
      category,
      status,
      minRating,
      maxRating,
      certifications,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const where: any = {};

    if (search) {
      where.OR = [
        { companyName: { contains: search, mode: 'insensitive' } },
        { contactPerson: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category && category !== 'all') {
      where.category = category;
    }

    if (status && status !== 'all') {
      where.status = status;
    }

    if (minRating !== undefined) {
      where.rating = { ...where.rating, gte: minRating };
    }

    if (maxRating !== undefined) {
      where.rating = { ...where.rating, lte: maxRating };
    }

    if (certifications && certifications.length > 0) {
      where.certifications = {
        hasSome: certifications,
      };
    }

    const [vendors, total] = await Promise.all([
      this.prisma.vendor.findMany({
        where,
        include: {
          proposals: {
            select: {
              id: true,
              title: true,
              status: true,
              totalCost: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 3,
          },
          reviews: {
            select: {
              id: true,
              rating: true,
              reviewerName: true,
              comment: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 3,
          },
          _count: {
            select: {
              proposals: true,
              reviews: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.vendor.count({ where }),
    ]);

    return {
      vendors,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getVendorById(id: string): Promise<Vendor> {
    const vendor = await this.prisma.vendor.findUnique({
      where: { id },
      include: {
        proposals: {
          include: {
            reviews: {
              select: {
                id: true,
                rating: true,
                reviewerName: true,
                comment: true,
                createdAt: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        reviews: {
          orderBy: {
            createdAt: 'desc',
          },
        },
        _count: {
          select: {
            proposals: true,
            reviews: true,
          },
        },
      },
    });

    if (!vendor) {
      throw new NotFoundException(`Vendor with ID ${id} not found`);
    }

    return vendor;
  }

  async updateVendor(id: string, data: UpdateVendorDto): Promise<Vendor> {
    const vendor = await this.getVendorById(id);

    // Check for duplicate company name if updating
    if (data.companyName && data.companyName !== vendor.companyName) {
      const existingVendor = await this.prisma.vendor.findFirst({
        where: {
          companyName: {
            equals: data.companyName,
            mode: 'insensitive',
          },
          id: { not: id },
        },
      });

      if (existingVendor) {
        throw new BadRequestException(
          'A vendor with this company name already exists',
        );
      }
    }

    // Check for duplicate email if updating
    if (data.email && data.email !== vendor.email) {
      const existingEmail = await this.prisma.vendor.findFirst({
        where: {
          email: {
            equals: data.email,
            mode: 'insensitive',
          },
          id: { not: id },
        },
      });

      if (existingEmail) {
        throw new BadRequestException(
          'A vendor with this email already exists',
        );
      }
    }

    const updated = await this.prisma.vendor.update({
      where: { id },
      data,
      include: {
        proposals: {
          select: {
            id: true,
            title: true,
            status: true,
            totalCost: true,
            createdAt: true,
          },
        },
        reviews: {
          select: {
            id: true,
            rating: true,
            reviewerName: true,
            comment: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            proposals: true,
            reviews: true,
          },
        },
      },
    });

    // Log vendor update activity
    await this.prisma.analytics.create({
      data: {
        event: 'vendor_updated',
        properties: {
          vendorId: id,
          companyName: updated.companyName,
          changes: data,
          previousStatus: vendor.status,
          newStatus: updated.status,
        },
      },
    });

    return updated;
  }

  async deleteVendor(id: string): Promise<void> {
    const vendor = await this.getVendorById(id);

    // Check if vendor has active proposals
    const activeProposals = await this.prisma.proposal.count({
      where: {
        vendorId: id,
        status: { in: ['submitted', 'negotiation'] },
      },
    });

    if (activeProposals > 0) {
      throw new BadRequestException(
        'Cannot delete vendor with active proposals',
      );
    }

    await this.prisma.vendor.delete({
      where: { id },
    });

    // Log vendor deletion activity
    await this.prisma.analytics.create({
      data: {
        event: 'vendor_deleted',
        properties: {
          vendorId: id,
          companyName: vendor.companyName,
          category: vendor.category,
          proposalCount: vendor._count.proposals,
          reviewCount: vendor._count.reviews,
        },
      },
    });
  }

  // =============================================================================
  // VENDOR RATING & PERFORMANCE
  // =============================================================================

  async updateVendorRating(vendorId: string): Promise<void> {
    const reviews = await this.prisma.review.findMany({
      where: { vendorId },
      select: { rating: true },
    });

    if (reviews.length === 0) {
      return;
    }

    const averageRating =
      reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
    const roundedRating = Math.round(averageRating * 10) / 10; // Round to 1 decimal place

    await this.prisma.vendor.update({
      where: { id: vendorId },
      data: { rating: roundedRating },
    });
  }

  async getVendorPerformance(vendorId: string) {
    const vendor = await this.getVendorById(vendorId);

    // Get performance metrics
    const [proposalStats, reviewStats, recentActivity] = await Promise.all([
      this.prisma.proposal.groupBy({
        by: ['status'],
        where: { vendorId },
        _count: { status: true },
        _avg: { totalCost: true },
      }),
      this.prisma.review.aggregate({
        where: { vendorId },
        _count: { rating: true },
        _avg: { rating: true },
        _min: { rating: true },
        _max: { rating: true },
      }),
      this.prisma.proposal.findMany({
        where: { vendorId },
        select: {
          id: true,
          title: true,
          status: true,
          totalCost: true,
          createdAt: true,
          submittedAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 5,
      }),
    ]);

    return {
      vendor,
      proposalStats,
      reviewStats,
      recentActivity,
      performanceScore: this.calculatePerformanceScore(
        proposalStats,
        reviewStats,
      ),
    };
  }

  private calculatePerformanceScore(
    proposalStats: any[],
    reviewStats: any,
  ): number {
    let score = 0;
    let factors = 0;

    // Rating factor (40% weight)
    if (reviewStats._avg.rating) {
      score += (reviewStats._avg.rating / 5) * 40;
      factors += 40;
    }

    // Proposal approval rate (35% weight)
    const totalProposals = proposalStats.reduce(
      (sum, stat) => sum + stat._count.status,
      0,
    );
    const approvedProposals =
      proposalStats.find((stat) => stat.status === 'approved')?._count.status ||
      0;

    if (totalProposals > 0) {
      const approvalRate = approvedProposals / totalProposals;
      score += approvalRate * 35;
      factors += 35;
    }

    // Review count factor (25% weight)
    const reviewCount = reviewStats._count.rating || 0;
    if (reviewCount > 0) {
      // Normalize review count (more reviews = better, cap at 20 reviews for full score)
      const normalizedReviewCount = Math.min(reviewCount / 20, 1);
      score += normalizedReviewCount * 25;
      factors += 25;
    }

    return factors > 0 ? Math.round((score / factors) * 100) : 0;
  }

  // =============================================================================
  // VENDOR CATEGORIES & STATISTICS
  // =============================================================================

  async getVendorCategories() {
    const categories = await this.prisma.vendor.groupBy({
      by: ['category'],
      _count: { category: true },
      _avg: { rating: true },
    });

    return categories.map((cat) => ({
      category: cat.category,
      count: cat._count.category,
      averageRating: cat._avg.rating || 0,
    }));
  }

  async getVendorStatistics() {
    const [
      totalVendors,
      activeVendors,
      averageRating,
      categoryStats,
      statusStats,
    ] = await Promise.all([
      this.prisma.vendor.count(),
      this.prisma.vendor.count({ where: { status: 'active' } }),
      this.prisma.vendor.aggregate({
        _avg: { rating: true },
      }),
      this.prisma.vendor.groupBy({
        by: ['category'],
        _count: { category: true },
      }),
      this.prisma.vendor.groupBy({
        by: ['status'],
        _count: { status: true },
      }),
    ]);

    return {
      totalVendors,
      activeVendors,
      averageRating: averageRating._avg.rating || 0,
      categoryBreakdown: categoryStats,
      statusBreakdown: statusStats,
    };
  }

  // =============================================================================
  // VENDOR WORKFLOW INTEGRATION
  // =============================================================================

  async initiateVendorOnboarding(vendorId: string) {
    return await this.vendorWorkflow.initiateOnboarding(vendorId);
  }

  async approveVendor(vendorId: string, approverId: string) {
    return await this.vendorWorkflow.approveVendor(vendorId, approverId);
  }

  async suspendVendor(vendorId: string, reason: string, suspendedBy: string) {
    return await this.vendorWorkflow.suspendVendor(
      vendorId,
      reason,
      suspendedBy,
    );
  }

  // =============================================================================
  // ANALYTICS & REPORTING
  // =============================================================================

  async getVendorAnalytics(timeRange?: string) {
    return await this.vendorAnalytics.getVendorAnalytics(timeRange);
  }

  async getTopPerformingVendors(limit: number = 10) {
    return await this.vendorAnalytics.getTopPerformingVendors(limit);
  }

  async getVendorTrends(vendorId: string, timeRange?: string) {
    return await this.vendorAnalytics.getVendorTrends(vendorId, timeRange);
  }
}
