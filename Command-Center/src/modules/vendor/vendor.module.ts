import { Module } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { VendorService } from './services/vendor.service';
import { VendorController } from './controllers/vendor.controller';
import { VendorAnalyticsService } from './services/vendor-analytics.service';
import { VendorWorkflowService } from './services/vendor-workflow.service';

@Module({
  imports: [PrismaModule],
  controllers: [VendorController],
  providers: [VendorService, VendorAnalyticsService, VendorWorkflowService],
  exports: [VendorService, VendorAnalyticsService, VendorWorkflowService],
})
export class VendorModule {}
