import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsArray,
  IsEnum,
  IsNumber,
  IsUrl,
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsJSO<PERSON>,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export enum VendorStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BLACKLISTED = 'blacklisted',
}

export enum VendorCategory {
  TECHNOLOGY = 'technology',
  TRAINING = 'training',
  CONSULTING = 'consulting',
  STAFFING = 'staffing',
  MARKETING = 'marketing',
  FINANCE = 'finance',
  LEGAL = 'legal',
  HR = 'hr',
  FACILITIES = 'facilities',
  EQUIPMENT = 'equipment',
  SOFTWARE = 'software',
  OTHER = 'other',
}

export class CreateVendorDto {
  @ApiProperty({ description: 'Company name', example: 'TechCorp Solutions' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  companyName: string;

  @ApiProperty({ description: 'Contact person name', example: 'John <PERSON>' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  contactPerson: string;

  @ApiProperty({
    description: 'Primary email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiPropertyOptional({
    description: 'Phone numbers array',
    example: ['******-0123', '******-0124'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  phoneNumbers?: string[];

  @ApiPropertyOptional({
    description: 'Company website URL',
    example: 'https://techcorp.com',
  })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiProperty({
    description: 'Vendor category',
    enum: VendorCategory,
    example: VendorCategory.TECHNOLOGY,
  })
  @IsEnum(VendorCategory)
  category: VendorCategory;

  @ApiPropertyOptional({ description: 'Company description', maxLength: 1000 })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({ description: 'Company address' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  address?: string;

  @ApiPropertyOptional({ description: 'City' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  city?: string;

  @ApiPropertyOptional({ description: 'State/Province' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  state?: string;

  @ApiPropertyOptional({ description: 'Country' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  country?: string;

  @ApiPropertyOptional({ description: 'Postal code' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  postalCode?: string;

  @ApiPropertyOptional({
    description: 'Vendor certifications',
    example: ['ISO 9001', 'SOC 2', 'GDPR Compliant'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  certifications?: string[];

  @ApiPropertyOptional({
    description: 'Vendor status',
    enum: VendorStatus,
    default: VendorStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(VendorStatus)
  status?: VendorStatus = VendorStatus.PENDING;

  @ApiPropertyOptional({
    description: 'Initial vendor rating',
    minimum: 0,
    maximum: 5,
    default: 0,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(5)
  rating?: number = 0;

  @ApiPropertyOptional({ description: 'Tax identification number' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  taxId?: string;

  @ApiPropertyOptional({ description: 'Business registration number' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  businessRegistrationNumber?: string;

  @ApiPropertyOptional({ description: 'Additional vendor metadata as JSON' })
  @IsOptional()
  metadata?: any;
}

export class UpdateVendorDto extends PartialType(CreateVendorDto) {
  @ApiPropertyOptional({
    description: 'Updated timestamp will be automatically set',
  })
  updatedAt?: Date;
}

export class VendorResponseDto {
  @ApiProperty({ description: 'Vendor unique identifier' })
  id: string;

  @ApiProperty({ description: 'Company name' })
  companyName: string;

  @ApiProperty({ description: 'Contact person name' })
  contactPerson: string;

  @ApiProperty({ description: 'Primary email address' })
  email: string;

  @ApiPropertyOptional({ description: 'Phone numbers array' })
  phoneNumbers?: string[];

  @ApiPropertyOptional({ description: 'Company website URL' })
  website?: string;

  @ApiProperty({ description: 'Vendor category', enum: VendorCategory })
  category: VendorCategory;

  @ApiPropertyOptional({ description: 'Company description' })
  description?: string;

  @ApiPropertyOptional({ description: 'Complete address information' })
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;

  @ApiPropertyOptional({ description: 'Vendor certifications' })
  certifications?: string[];

  @ApiProperty({ description: 'Current vendor status', enum: VendorStatus })
  status: VendorStatus;

  @ApiProperty({ description: 'Vendor rating (0-5)', minimum: 0, maximum: 5 })
  rating: number;

  @ApiPropertyOptional({ description: 'Tax identification number' })
  taxId?: string;

  @ApiPropertyOptional({ description: 'Business registration number' })
  businessRegistrationNumber?: string;

  @ApiPropertyOptional({ description: 'Additional vendor metadata' })
  metadata?: any;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiPropertyOptional({ description: 'Recent proposals summary' })
  proposals?: {
    id: string;
    title: string;
    status: string;
    totalCost: number;
    createdAt: Date;
  }[];

  @ApiPropertyOptional({ description: 'Recent reviews summary' })
  reviews?: {
    id: string;
    rating: number;
    reviewerName: string;
    comment: string;
    createdAt: Date;
  }[];

  @ApiPropertyOptional({ description: 'Counts of related records' })
  _count?: {
    proposals: number;
    reviews: number;
  };
}

export class VendorSummaryDto {
  @ApiProperty({ description: 'Vendor unique identifier' })
  id: string;

  @ApiProperty({ description: 'Company name' })
  companyName: string;

  @ApiProperty({ description: 'Contact person name' })
  contactPerson: string;

  @ApiProperty({ description: 'Primary email address' })
  email: string;

  @ApiProperty({ description: 'Vendor category', enum: VendorCategory })
  category: VendorCategory;

  @ApiProperty({ description: 'Current vendor status', enum: VendorStatus })
  status: VendorStatus;

  @ApiProperty({ description: 'Vendor rating (0-5)' })
  rating: number;

  @ApiProperty({ description: 'Total number of proposals' })
  proposalCount: number;

  @ApiProperty({ description: 'Total number of reviews' })
  reviewCount: number;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;
}

export class VendorPerformanceDto {
  @ApiProperty({ description: 'Vendor information' })
  vendor: VendorResponseDto;

  @ApiProperty({ description: 'Proposal statistics by status' })
  proposalStats: {
    status: string;
    count: number;
    averageCost: number;
  }[];

  @ApiProperty({ description: 'Review statistics' })
  reviewStats: {
    count: number;
    averageRating: number;
    minRating: number;
    maxRating: number;
  };

  @ApiProperty({ description: 'Recent vendor activity' })
  recentActivity: {
    id: string;
    title: string;
    status: string;
    totalCost: number;
    createdAt: Date;
    submittedAt?: Date;
  }[];

  @ApiProperty({ description: 'Overall performance score (0-100)' })
  performanceScore: number;
}
