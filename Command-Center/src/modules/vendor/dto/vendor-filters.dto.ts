import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsNumber,
  IsArray,
  Min,
  <PERSON>,
  IsIn,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { VendorStatus, VendorCategory } from './vendor.dto';

export class VendorFiltersDto {
  @ApiPropertyOptional({
    description: 'Search term for vendor name, contact person, or email',
    example: 'TechCorp',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by vendor category',
    enum: VendorCategory,
    example: VendorCategory.TECHNOLOGY,
  })
  @IsOptional()
  @IsEnum(VendorCategory)
  category?: VendorCategory;

  @ApiPropertyOptional({
    description: 'Filter by vendor status',
    enum: VendorStatus,
    example: VendorStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(VendorStatus)
  status?: VendorStatus;

  @ApiPropertyOptional({
    description: 'Minimum vendor rating',
    minimum: 0,
    maximum: 5,
    example: 3.0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(5)
  minRating?: number;

  @ApiPropertyOptional({
    description: 'Maximum vendor rating',
    minimum: 0,
    maximum: 5,
    example: 5.0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(5)
  maxRating?: number;

  @ApiPropertyOptional({
    description: 'Filter by certifications',
    example: ['ISO 9001', 'SOC 2'],
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return [value];
    }
    return value;
  })
  @IsArray()
  @IsString({ each: true })
  certifications?: string[];

  @ApiPropertyOptional({
    description: 'Filter by city',
    example: 'New York',
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: 'Filter by state/province',
    example: 'NY',
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({
    description: 'Filter by country',
    example: 'USA',
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: [
      'companyName',
      'contactPerson',
      'rating',
      'createdAt',
      'updatedAt',
      'category',
      'status',
    ],
    default: 'createdAt',
    example: 'rating',
  })
  @IsOptional()
  @IsString()
  @IsIn([
    'companyName',
    'contactPerson',
    'rating',
    'createdAt',
    'updatedAt',
    'category',
    'status',
  ])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
    example: 'desc',
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';

  @ApiPropertyOptional({
    description: 'Include only vendors with recent activity (days)',
    minimum: 1,
    maximum: 365,
    example: 30,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(365)
  recentActivityDays?: number;

  @ApiPropertyOptional({
    description: 'Filter by minimum number of proposals',
    minimum: 0,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minProposals?: number;

  @ApiPropertyOptional({
    description: 'Filter by minimum number of reviews',
    minimum: 0,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minReviews?: number;

  @ApiPropertyOptional({
    description: 'Include vendors without any proposals',
    default: true,
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  includeWithoutProposals?: boolean = true;

  @ApiPropertyOptional({
    description: 'Include vendors without any reviews',
    default: true,
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  includeWithoutReviews?: boolean = true;
}
