import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { TrainingService } from './services/training.service';
import { AssessmentService } from './services/assessment.service';
import { SkillService } from './services/skill.service';
import { LearningPathService } from './services/learning-path.service';
import { TrainingController } from './controllers/training.controller';
import { AssessmentController } from './controllers/assessment.controller';
import { SkillController } from './controllers/skill.controller';
import { LearningPathController } from './controllers/learning-path.controller';
import { TrainingGateway } from './gateways/training.gateway';
import { RecommendationEngine } from './engines/recommendation.engine';
import { SkillGapAnalyzer } from './engines/skill-gap-analyzer.engine';
import { TrainingAnalytics } from './services/training-analytics.service';

@Module({
  imports: [PrismaModule],
  controllers: [
    TrainingController,
    AssessmentController,
    SkillController,
    LearningPathController,
  ],
  providers: [
    TrainingService,
    AssessmentService,
    SkillService,
    LearningPathService,
    TrainingGateway,
    RecommendationEngine,
    SkillGapAnalyzer,
    TrainingAnalytics,
  ],
  exports: [
    TrainingService,
    AssessmentService,
    SkillService,
    LearningPathService,
    RecommendationEngine,
    SkillGapAnalyzer,
    TrainingAnalytics,
  ],
})
export class TrainingModule {}
