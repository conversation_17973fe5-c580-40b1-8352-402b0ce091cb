import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsNumber,
  IsEnum,
  IsU<PERSON>D,
  IsDateString,
  Min,
  Max,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum EnrollmentStatus {
  ENROLLED = 'enrolled',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  DROPPED = 'dropped',
  FAILED = 'failed',
}

export class CreateEnrollmentDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user-uuid-123',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Course ID',
    example: 'course-uuid-456',
  })
  @IsUUID()
  @IsNotEmpty()
  courseId: string;

  @ApiPropertyOptional({
    description: 'Start date of the course',
    example: '2024-01-15T09:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;
}

export class UpdateEnrollmentDto {
  @ApiPropertyOptional({
    description: 'Start date of the course',
    example: '2024-01-15T09:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Completion date of the course',
    example: '2024-03-15T17:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  completedDate?: string;

  @ApiPropertyOptional({
    description: 'Enrollment status',
    enum: EnrollmentStatus,
    example: EnrollmentStatus.IN_PROGRESS,
  })
  @IsOptional()
  @IsEnum(EnrollmentStatus)
  status?: EnrollmentStatus;

  @ApiPropertyOptional({
    description: 'Course progress (0-100)',
    example: 75,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Type(() => Number)
  progress?: number;

  @ApiPropertyOptional({
    description: 'Final score (0-100)',
    example: 85,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Type(() => Number)
  score?: number;
}

export class EnrollmentResponseDto {
  @ApiProperty({
    description: 'Enrollment ID',
    example: 'enrollment-uuid-789',
  })
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: 'user-uuid-123',
  })
  userId: string;

  @ApiProperty({
    description: 'Course ID',
    example: 'course-uuid-456',
  })
  courseId: string;

  @ApiProperty({
    description: 'Enrollment date',
    example: '2024-01-01T09:00:00.000Z',
  })
  enrolledDate: Date;

  @ApiProperty({
    description: 'Start date',
    example: '2024-01-15T09:00:00.000Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Completion date',
    example: '2024-03-15T17:00:00.000Z',
  })
  completedDate: Date;

  @ApiProperty({
    description: 'Enrollment status',
    enum: EnrollmentStatus,
    example: EnrollmentStatus.COMPLETED,
  })
  status: EnrollmentStatus;

  @ApiProperty({
    description: 'Course progress (0-100)',
    example: 100,
  })
  progress: number;

  @ApiProperty({
    description: 'Final score (0-100)',
    example: 92,
  })
  score: number;

  @ApiProperty({
    description: 'User information',
  })
  user: {
    id: string;
    name: string;
    email: string;
    department?: {
      id: string;
      name: string;
    };
  };

  @ApiProperty({
    description: 'Course information',
  })
  course: {
    id: string;
    title: string;
    duration: number;
    difficulty: string;
    type: string;
  };

  @ApiProperty({
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
