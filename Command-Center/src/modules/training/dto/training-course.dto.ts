import {
  IsString,
  <PERSON>NotEmpty,
  IsOptional,
  IsNumber,
  IsEnum,
  IsArray,
  IsPositive,
  Min,
  Max,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum TrainingType {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
  ONLINE = 'online',
  WORKSHOP = 'workshop',
  CERTIFICATION = 'certification',
}

export enum TrainingDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
}

export enum TrainingStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  COMING_SOON = 'coming-soon',
}

export class CreateTrainingCourseDto {
  @ApiProperty({
    description: 'Course title',
    example: 'Advanced JavaScript Development',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Course description',
    example: 'Master modern JavaScript concepts and best practices',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Course provider',
    example: 'TechEd Academy',
  })
  @IsString()
  @IsNotEmpty()
  provider: string;

  @ApiProperty({
    description: 'Course type',
    enum: TrainingType,
    example: TrainingType.ONLINE,
  })
  @IsEnum(TrainingType)
  type: TrainingType;

  @ApiProperty({
    description: 'Course duration in hours',
    example: 40,
  })
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  duration: number;

  @ApiProperty({
    description: 'Course cost',
    example: 299.99,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost: number;

  @ApiPropertyOptional({
    description: 'Course capacity (maximum enrollments)',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  capacity?: number;

  @ApiPropertyOptional({
    description: 'Course prerequisites',
    example: ['Basic JavaScript', 'HTML/CSS fundamentals'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prerequisites?: string[];

  @ApiProperty({
    description: 'Course difficulty level',
    enum: TrainingDifficulty,
    example: TrainingDifficulty.ADVANCED,
  })
  @IsEnum(TrainingDifficulty)
  difficulty: TrainingDifficulty;

  @ApiPropertyOptional({
    description: 'Course status',
    enum: TrainingStatus,
    example: TrainingStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(TrainingStatus)
  status?: TrainingStatus;

  @ApiPropertyOptional({
    description: 'Skills addressed by this course',
    example: ['uuid1', 'uuid2', 'uuid3'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skillIds?: string[];
}

export class UpdateTrainingCourseDto {
  @ApiPropertyOptional({
    description: 'Course title',
    example: 'Advanced JavaScript Development',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  title?: string;

  @ApiPropertyOptional({
    description: 'Course description',
    example: 'Master modern JavaScript concepts and best practices',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;

  @ApiPropertyOptional({
    description: 'Course provider',
    example: 'TechEd Academy',
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  provider?: string;

  @ApiPropertyOptional({
    description: 'Course type',
    enum: TrainingType,
    example: TrainingType.ONLINE,
  })
  @IsOptional()
  @IsEnum(TrainingType)
  type?: TrainingType;

  @ApiPropertyOptional({
    description: 'Course duration in hours',
    example: 40,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  duration?: number;

  @ApiPropertyOptional({
    description: 'Course cost',
    example: 299.99,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost?: number;

  @ApiPropertyOptional({
    description: 'Course capacity (maximum enrollments)',
    example: 50,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  capacity?: number;

  @ApiPropertyOptional({
    description: 'Course prerequisites',
    example: ['Basic JavaScript', 'HTML/CSS fundamentals'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prerequisites?: string[];

  @ApiPropertyOptional({
    description: 'Course difficulty level',
    enum: TrainingDifficulty,
    example: TrainingDifficulty.ADVANCED,
  })
  @IsOptional()
  @IsEnum(TrainingDifficulty)
  difficulty?: TrainingDifficulty;

  @ApiPropertyOptional({
    description: 'Course rating',
    example: 4.5,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  @Type(() => Number)
  rating?: number;

  @ApiPropertyOptional({
    description: 'Course status',
    enum: TrainingStatus,
    example: TrainingStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(TrainingStatus)
  status?: TrainingStatus;

  @ApiPropertyOptional({
    description: 'Skills addressed by this course',
    example: ['uuid1', 'uuid2', 'uuid3'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skillIds?: string[];
}

export class TrainingCourseResponseDto {
  @ApiProperty({
    description: 'Course ID',
    example: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Course title',
    example: 'Advanced JavaScript Development',
  })
  title: string;

  @ApiProperty({
    description: 'Course description',
    example: 'Master modern JavaScript concepts and best practices',
  })
  description: string;

  @ApiProperty({
    description: 'Course provider',
    example: 'TechEd Academy',
  })
  provider: string;

  @ApiProperty({
    description: 'Course type',
    enum: TrainingType,
    example: TrainingType.ONLINE,
  })
  type: TrainingType;

  @ApiProperty({
    description: 'Course duration in hours',
    example: 40,
  })
  duration: number;

  @ApiProperty({
    description: 'Course cost',
    example: 299.99,
  })
  cost: number;

  @ApiProperty({
    description: 'Course capacity (maximum enrollments)',
    example: 50,
  })
  capacity: number;

  @ApiProperty({
    description: 'Course prerequisites',
    example: ['Basic JavaScript', 'HTML/CSS fundamentals'],
  })
  prerequisites: string[];

  @ApiProperty({
    description: 'Course difficulty level',
    enum: TrainingDifficulty,
    example: TrainingDifficulty.ADVANCED,
  })
  difficulty: TrainingDifficulty;

  @ApiProperty({
    description: 'Course rating',
    example: 4.5,
  })
  rating: number;

  @ApiProperty({
    description: 'Number of enrollments',
    example: 25,
  })
  enrollments: number;

  @ApiProperty({
    description: 'Course status',
    enum: TrainingStatus,
    example: TrainingStatus.ACTIVE,
  })
  status: TrainingStatus;

  @ApiProperty({
    description: 'Skills addressed by this course',
  })
  skills: any[];

  @ApiProperty({
    description: 'Course creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Course last update timestamp',
  })
  updatedAt: Date;
}
