import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TrainingService } from './training.service';
import { Course } from './entities/course.entity';
import { SkillGap } from './entities/skill-gap.entity';
import { UserProgress } from './entities/user-progress.entity';
import { TrainingRecommendation } from './entities/training-recommendation.entity';
import { NotFoundException } from '@nestjs/common';
import { CreateCourseDto } from './dto/create-course.dto';

describe('TrainingService', () => {
  let service: TrainingService;
  let courseRepository: Repository<Course>;
  let skillGapRepository: Repository<SkillGap>;
  let userProgressRepository: Repository<UserProgress>;
  let recommendationRepository: Repository<TrainingRecommendation>;

  const mockCourse = {
    id: '1',
    title: 'Test Course',
    description: 'Test Description',
    duration: 120,
    level: 'beginner',
    category: 'technical',
    skills: ['JavaScript', 'React'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockSkillGap = {
    id: '1',
    userId: 'user1',
    skill: 'JavaScript',
    currentLevel: 2,
    targetLevel: 4,
    priority: 'high',
    status: 'identified',
    createdAt: new Date(),
  };

  const mockUserProgress = {
    id: '1',
    userId: 'user1',
    courseId: '1',
    course: mockCourse,
    progress: 50,
    status: 'in_progress',
    startedAt: new Date(),
    lastAccessedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TrainingService,
        {
          provide: getRepositoryToken(Course),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(SkillGap),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserProgress),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(TrainingRecommendation),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<TrainingService>(TrainingService);
    courseRepository = module.get<Repository<Course>>(
      getRepositoryToken(Course),
    );
    skillGapRepository = module.get<Repository<SkillGap>>(
      getRepositoryToken(SkillGap),
    );
    userProgressRepository = module.get<Repository<UserProgress>>(
      getRepositoryToken(UserProgress),
    );
    recommendationRepository = module.get<Repository<TrainingRecommendation>>(
      getRepositoryToken(TrainingRecommendation),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createCourse', () => {
    it('should create a new course', async () => {
      const createCourseDto: CreateCourseDto = {
        title: 'New Course',
        description: 'Course Description',
        duration: 90,
        level: 'intermediate',
        category: 'technical',
        skills: ['Node.js', 'Express'],
        modules: [],
      };

      const savedCourse = { ...createCourseDto, id: '2' };
      jest
        .spyOn(courseRepository, 'create')
        .mockReturnValue(savedCourse as any);
      jest
        .spyOn(courseRepository, 'save')
        .mockResolvedValue(savedCourse as any);

      const result = await service.createCourse(createCourseDto);

      expect(result).toEqual(savedCourse);
      expect(courseRepository.create).toHaveBeenCalledWith(createCourseDto);
      expect(courseRepository.save).toHaveBeenCalledWith(savedCourse);
    });
  });

  describe('findAllCourses', () => {
    it('should return filtered courses', async () => {
      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockCourse], 1]),
      };

      jest
        .spyOn(courseRepository, 'createQueryBuilder')
        .mockReturnValue(queryBuilder as any);

      const result = await service.findAllCourses({
        category: 'technical',
        level: 'beginner',
        isActive: true,
        page: 1,
        limit: 10,
      });

      expect(result).toEqual({
        data: [mockCourse],
        total: 1,
        page: 1,
        limit: 10,
      });
      expect(queryBuilder.where).toHaveBeenCalled();
      expect(queryBuilder.skip).toHaveBeenCalledWith(0);
      expect(queryBuilder.take).toHaveBeenCalledWith(10);
    });
  });

  describe('findCourseById', () => {
    it('should return a course by id', async () => {
      jest
        .spyOn(courseRepository, 'findOne')
        .mockResolvedValue(mockCourse as any);

      const result = await service.findCourseById('1');

      expect(result).toEqual(mockCourse);
      expect(courseRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['modules', 'enrollments'],
      });
    });

    it('should throw NotFoundException if course not found', async () => {
      jest.spyOn(courseRepository, 'findOne').mockResolvedValue(null);

      await expect(service.findCourseById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('analyzeSkillGaps', () => {
    it('should analyze and save skill gaps', async () => {
      const analysisDto = {
        userId: 'user1',
        requiredSkills: [
          { skill: 'JavaScript', level: 4 },
          { skill: 'React', level: 3 },
        ],
        currentSkills: [{ skill: 'JavaScript', level: 2 }],
      };

      jest.spyOn(skillGapRepository, 'find').mockResolvedValue([]);
      jest
        .spyOn(skillGapRepository, 'create')
        .mockImplementation((data) => data as any);
      jest
        .spyOn(skillGapRepository, 'save')
        .mockImplementation((data) => Promise.resolve(data));

      const result = await service.analyzeSkillGaps(analysisDto);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        userId: 'user1',
        skill: 'JavaScript',
        currentLevel: 2,
        targetLevel: 4,
        priority: 'high',
      });
      expect(result[1]).toMatchObject({
        userId: 'user1',
        skill: 'React',
        currentLevel: 0,
        targetLevel: 3,
        priority: 'high',
      });
    });
  });

  describe('getUserProgress', () => {
    it('should return user progress for all courses', async () => {
      jest
        .spyOn(userProgressRepository, 'find')
        .mockResolvedValue([mockUserProgress] as any);

      const result = await service.getUserProgress('user1');

      expect(result).toEqual([mockUserProgress]);
      expect(userProgressRepository.find).toHaveBeenCalledWith({
        where: { userId: 'user1' },
        relations: ['course'],
        order: { lastAccessedAt: 'DESC' },
      });
    });

    it('should return user progress for a specific course', async () => {
      jest
        .spyOn(userProgressRepository, 'findOne')
        .mockResolvedValue(mockUserProgress as any);

      const result = await service.getUserProgress('user1', '1');

      expect(result).toEqual(mockUserProgress);
      expect(userProgressRepository.findOne).toHaveBeenCalledWith({
        where: { userId: 'user1', courseId: '1' },
        relations: ['course'],
      });
    });
  });

  describe('updateCourseProgress', () => {
    it('should update existing progress', async () => {
      const updateDto = {
        userId: 'user1',
        courseId: '1',
        progress: 75,
        status: 'in_progress' as const,
      };

      jest
        .spyOn(userProgressRepository, 'findOne')
        .mockResolvedValue(mockUserProgress as any);
      jest.spyOn(userProgressRepository, 'save').mockResolvedValue({
        ...mockUserProgress,
        ...updateDto,
        lastAccessedAt: new Date(),
      } as any);

      const result = await service.updateCourseProgress(updateDto);

      expect(result.progress).toBe(75);
      expect(userProgressRepository.save).toHaveBeenCalled();
    });

    it('should create new progress if not exists', async () => {
      const updateDto = {
        userId: 'user2',
        courseId: '1',
        progress: 10,
        status: 'in_progress' as const,
      };

      jest.spyOn(userProgressRepository, 'findOne').mockResolvedValue(null);
      jest
        .spyOn(userProgressRepository, 'create')
        .mockReturnValue(updateDto as any);
      jest.spyOn(userProgressRepository, 'save').mockResolvedValue({
        ...updateDto,
        id: '2',
        startedAt: new Date(),
        lastAccessedAt: new Date(),
      } as any);

      const result = await service.updateCourseProgress(updateDto);

      expect(result.userId).toBe('user2');
      expect(userProgressRepository.create).toHaveBeenCalledWith(
        expect.objectContaining(updateDto),
      );
    });

    it('should mark as completed when progress is 100', async () => {
      const updateDto = {
        userId: 'user1',
        courseId: '1',
        progress: 100,
        status: 'in_progress' as const,
      };

      jest
        .spyOn(userProgressRepository, 'findOne')
        .mockResolvedValue(mockUserProgress as any);
      jest.spyOn(userProgressRepository, 'save').mockResolvedValue({
        ...mockUserProgress,
        progress: 100,
        status: 'completed',
        completedAt: new Date(),
      } as any);

      const result = await service.updateCourseProgress(updateDto);

      expect(result.status).toBe('completed');
      expect(result.completedAt).toBeDefined();
    });
  });

  describe('getTrainingMetrics', () => {
    it('should return training metrics for a department', async () => {
      const queryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          totalCourses: '10',
          completedCourses: '5',
          inProgressCourses: '3',
          averageProgress: '65.5',
          totalHours: '450',
        }),
      };

      jest
        .spyOn(userProgressRepository, 'createQueryBuilder')
        .mockReturnValue(queryBuilder as any);

      const result = await service.getTrainingMetrics({
        departmentId: 'dept1',
      });

      expect(result).toEqual({
        totalCourses: 10,
        completedCourses: 5,
        inProgressCourses: 3,
        averageProgress: 65.5,
        totalHours: 450,
        completionRate: 50,
      });
    });
  });
});
