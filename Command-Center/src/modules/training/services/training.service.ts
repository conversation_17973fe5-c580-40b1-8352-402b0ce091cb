import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import {
  TrainingCourse,
  TrainingEnrollment,
  TrainingRecommendation,
  User,
} from '@prisma/client';
import {
  CreateTrainingCourseDto,
  UpdateTrainingCourseDto,
} from '../dto/training-course.dto';
import {
  CreateEnrollmentDto,
  UpdateEnrollmentDto,
} from '../dto/enrollment.dto';
import { TrainingFiltersDto } from '../dto/training-filters.dto';
import { RecommendationEngine } from '../engines/recommendation.engine';
import { TrainingAnalytics } from './training-analytics.service';

@Injectable()
export class TrainingService {
  constructor(
    private prisma: PrismaService,
    private recommendationEngine: RecommendationEngine,
    private trainingAnalytics: TrainingAnalytics,
  ) {}

  // =============================================================================
  // TRAINING COURSE MANAGEMENT
  // =============================================================================

  async createCourse(data: CreateTrainingCourseDto): Promise<TrainingCourse> {
    const { skillIds, ...courseData } = data;

    return await this.prisma.trainingCourse.create({
      data: {
        ...courseData,
        skills: {
          connect: skillIds?.map((id) => ({ id })) || [],
        },
      },
      include: {
        skills: true,
        _count: {
          select: {
            enrollments_rel: true,
          },
        },
      },
    });
  }

  async getCourses(filters: TrainingFiltersDto) {
    const {
      search,
      type,
      difficulty,
      provider,
      status,
      minCost,
      maxCost,
      skills,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { provider: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (type) where.type = type;
    if (difficulty) where.difficulty = difficulty;
    if (provider) where.provider = provider;
    if (status) where.status = status;
    if (minCost !== undefined) where.cost = { ...where.cost, gte: minCost };
    if (maxCost !== undefined) where.cost = { ...where.cost, lte: maxCost };

    if (skills && skills.length > 0) {
      where.skills = {
        some: {
          id: { in: skills },
        },
      };
    }

    const [courses, total] = await Promise.all([
      this.prisma.trainingCourse.findMany({
        where,
        include: {
          skills: true,
          _count: {
            select: {
              enrollments_rel: true,
              recommendations: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.trainingCourse.count({ where }),
    ]);

    return {
      courses,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getCourseById(id: string): Promise<TrainingCourse> {
    const course = await this.prisma.trainingCourse.findUnique({
      where: { id },
      include: {
        skills: true,
        enrollments_rel: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        recommendations: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            skillGap: {
              include: {
                skill: true,
              },
            },
          },
        },
        _count: {
          select: {
            enrollments_rel: true,
            recommendations: true,
          },
        },
      },
    });

    if (!course) {
      throw new NotFoundException(`Training course with ID ${id} not found`);
    }

    return course;
  }

  async updateCourse(
    id: string,
    data: UpdateTrainingCourseDto,
  ): Promise<TrainingCourse> {
    const { skillIds, ...courseData } = data;

    const course = await this.getCourseById(id);

    return await this.prisma.trainingCourse.update({
      where: { id },
      data: {
        ...courseData,
        ...(skillIds && {
          skills: {
            set: skillIds.map((id) => ({ id })),
          },
        }),
      },
      include: {
        skills: true,
        _count: {
          select: {
            enrollments_rel: true,
          },
        },
      },
    });
  }

  async deleteCourse(id: string): Promise<void> {
    const course = await this.getCourseById(id);

    // Check if there are active enrollments
    const activeEnrollments = await this.prisma.trainingEnrollment.count({
      where: {
        courseId: id,
        status: { in: ['enrolled', 'in-progress'] },
      },
    });

    if (activeEnrollments > 0) {
      throw new BadRequestException(
        'Cannot delete course with active enrollments',
      );
    }

    await this.prisma.trainingCourse.delete({
      where: { id },
    });
  }

  // =============================================================================
  // ENROLLMENT MANAGEMENT
  // =============================================================================

  async enrollUser(data: CreateEnrollmentDto): Promise<TrainingEnrollment> {
    const { userId, courseId, startDate } = data;

    // Check if user is already enrolled
    const existingEnrollment = await this.prisma.trainingEnrollment.findUnique({
      where: {
        userId_courseId: {
          userId,
          courseId,
        },
      },
    });

    if (existingEnrollment) {
      throw new BadRequestException('User is already enrolled in this course');
    }

    // Check course capacity
    const course = await this.getCourseById(courseId);
    if (course.capacity) {
      const currentEnrollments = await this.prisma.trainingEnrollment.count({
        where: {
          courseId,
          status: { in: ['enrolled', 'in-progress'] },
        },
      });

      if (currentEnrollments >= course.capacity) {
        throw new BadRequestException('Course is at full capacity');
      }
    }

    // Create enrollment
    const enrollment = await this.prisma.trainingEnrollment.create({
      data: {
        userId,
        courseId,
        startDate,
        status: startDate ? 'in-progress' : 'enrolled',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        course: {
          select: {
            id: true,
            title: true,
            duration: true,
            difficulty: true,
          },
        },
      },
    });

    // Update course enrollment count
    await this.prisma.trainingCourse.update({
      where: { id: courseId },
      data: {
        enrollments: {
          increment: 1,
        },
      },
    });

    // Log learning activity
    await this.prisma.learningActivity.create({
      data: {
        type: 'training-started',
        userId,
        description: `Enrolled in ${course.title}`,
        metadata: {
          courseId,
          courseName: course.title,
          enrollmentId: enrollment.id,
        },
      },
    });

    return enrollment;
  }

  async getEnrollments(userId?: string, courseId?: string) {
    const where: any = {};
    if (userId) where.userId = userId;
    if (courseId) where.courseId = courseId;

    return await this.prisma.trainingEnrollment.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            department: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        course: {
          select: {
            id: true,
            title: true,
            duration: true,
            difficulty: true,
            type: true,
          },
        },
      },
      orderBy: {
        enrolledDate: 'desc',
      },
    });
  }

  async updateEnrollment(
    id: string,
    data: UpdateEnrollmentDto,
  ): Promise<TrainingEnrollment> {
    const enrollment = await this.prisma.trainingEnrollment.findUnique({
      where: { id },
      include: {
        course: true,
        user: true,
      },
    });

    if (!enrollment) {
      throw new NotFoundException(`Enrollment with ID ${id} not found`);
    }

    const updated = await this.prisma.trainingEnrollment.update({
      where: { id },
      data,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        course: {
          select: {
            id: true,
            title: true,
            duration: true,
            difficulty: true,
          },
        },
      },
    });

    // Log completion activity
    if (data.status === 'completed' && enrollment.status !== 'completed') {
      await this.prisma.learningActivity.create({
        data: {
          type: 'training-completed',
          userId: enrollment.userId,
          description: `Completed ${enrollment.course.title}`,
          metadata: {
            courseId: enrollment.courseId,
            courseName: enrollment.course.title,
            enrollmentId: id,
            score: data.score,
            progress: data.progress,
          },
        },
      });
    }

    return updated;
  }

  // =============================================================================
  // RECOMMENDATIONS
  // =============================================================================

  async getRecommendations(userId: string): Promise<TrainingRecommendation[]> {
    return await this.prisma.trainingRecommendation.findMany({
      where: { userId },
      include: {
        course: {
          select: {
            id: true,
            title: true,
            description: true,
            duration: true,
            cost: true,
            difficulty: true,
            rating: true,
          },
        },
        skillGap: {
          include: {
            skill: {
              select: {
                id: true,
                name: true,
                category: true,
              },
            },
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  async generateRecommendations(
    userId: string,
  ): Promise<TrainingRecommendation[]> {
    return await this.recommendationEngine.generateRecommendations(userId);
  }

  async updateRecommendationStatus(
    id: string,
    status: 'accepted' | 'declined',
  ): Promise<TrainingRecommendation> {
    const recommendation = await this.prisma.trainingRecommendation.findUnique({
      where: { id },
      include: {
        course: true,
        user: true,
      },
    });

    if (!recommendation) {
      throw new NotFoundException(`Recommendation with ID ${id} not found`);
    }

    const updated = await this.prisma.trainingRecommendation.update({
      where: { id },
      data: { status },
    });

    // Auto-enroll if accepted
    if (status === 'accepted') {
      try {
        await this.enrollUser({
          userId: recommendation.userId,
          courseId: recommendation.courseId,
        });
      } catch (error) {
        // Log error but don't fail the recommendation update
        console.error('Auto-enrollment failed:', error);
      }
    }

    return updated;
  }

  // =============================================================================
  // ANALYTICS & REPORTING
  // =============================================================================

  async getTrainingMetrics(departmentId?: string) {
    return await this.trainingAnalytics.getTrainingMetrics(departmentId);
  }

  async getCourseAnalytics(courseId: string) {
    return await this.trainingAnalytics.getCourseAnalytics(courseId);
  }

  async getUserProgress(userId: string) {
    return await this.trainingAnalytics.getUserProgress(userId);
  }

  async getROIAnalysis(departmentId?: string) {
    return await this.trainingAnalytics.getROIAnalysis(departmentId);
  }
}
