import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Auth<PERSON>ontroller } from './auth.controller';
import { AdminController } from './admin.controller';
import { AuthService } from './services/auth.service';
import { RoleService } from './services/role.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtRefreshStrategy } from './strategies/jwt-refresh.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { User } from '../users/entities/user.entity';
import { RefreshToken } from './entities/refresh-token.entity';
import { Permission } from './entities/permission.entity';
import { Role } from './entities/role.entity';
import { UserSession } from './entities/user-session.entity';
import { AuthAttempt } from './entities/auth-attempt.entity';
import { PasswordResetToken } from './entities/password-reset-token.entity';
import { EmailVerificationToken } from './entities/email-verification-token.entity';
import { UsersModule } from '../users/users.module';
import { CryptoService } from './services/crypto.service';
import { TokenService } from './services/token.service';
import { SessionService } from './services/session.service';
import { SecurityService } from './services/security.service';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { CrossAppAuthService } from './services/cross-app-auth.service';
import { CrossAppAuthController } from './controllers/cross-app-auth.controller';
import { RedisSessionService } from './services/redis-session.service';
import { SessionController } from './controllers/session.controller';
import { RedisModule } from '../redis/redis.module';
import { CorsManagerService } from './services/cors-manager.service';
import { CorsController } from './controllers/cors.controller';

@Module({
  imports: [
    ConfigModule,
    RedisModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '15m'),
          issuer: configService.get<string>('JWT_ISSUER', 'command-center'),
          audience: configService.get<string>(
            'JWT_AUDIENCE',
            'command-center-api',
          ),
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      User,
      RefreshToken,
      Permission,
      Role,
      UserSession,
      AuthAttempt,
      PasswordResetToken,
      EmailVerificationToken,
    ]),
    UsersModule,
  ],
  controllers: [
    AuthController,
    AdminController,
    CrossAppAuthController,
    SessionController,
    CorsController,
  ],
  providers: [
    AuthService,
    RoleService,
    JwtStrategy,
    JwtRefreshStrategy,
    LocalStrategy,
    CryptoService,
    TokenService,
    SessionService,
    SecurityService,
    RolesGuard,
    PermissionsGuard,
    CrossAppAuthService,
    RedisSessionService,
    CorsManagerService,
  ],
  exports: [
    AuthService,
    RoleService,
    CryptoService,
    TokenService,
    SessionService,
    SecurityService,
    RolesGuard,
    PermissionsGuard,
    CrossAppAuthService,
    RedisSessionService,
    CorsManagerService,
  ],
})
export class AuthModule {}
