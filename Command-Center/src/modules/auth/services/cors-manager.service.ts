import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface AppCorsConfig {
  appId: string;
  allowedOrigins: string[];
  allowedMethods: string[];
  allowedHeaders: string[];
  exposedHeaders: string[];
  credentials: boolean;
  maxAge: number;
  preflightContinue: boolean;
  optionsSuccessStatus: number;
}

export interface CorsValidationResult {
  allowed: boolean;
  appId?: string;
  reason?: string;
  headers?: Record<string, string>;
}

@Injectable()
export class CorsManagerService {
  private readonly logger = new Logger(CorsManagerService.name);
  private readonly appConfigs: Map<string, AppCorsConfig> = new Map();

  constructor(private readonly configService: ConfigService) {
    this.initializeAppConfigs();
  }

  private initializeAppConfigs(): void {
    const environment = this.configService.get<string>(
      'NODE_ENV',
      'development',
    );

    // Command Center (Main API)
    this.appConfigs.set('command-center', {
      appId: 'command-center',
      allowedOrigins: [
        'http://localhost:3000',
        'https://command-center.luminar.app',
        ...(environment === 'development' ? ['http://127.0.0.1:3000'] : []),
      ],
      allowedMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Key',
        'X-Device-ID',
        'X-Session-ID',
        'X-Request-ID',
        'X-CSRF-Token',
        'X-App-ID',
      ],
      exposedHeaders: [
        'X-Total-Count',
        'X-Request-ID',
        'X-Rate-Limit-Limit',
        'X-Rate-Limit-Remaining',
      ],
      credentials: true,
      maxAge: 86400,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });

    // Learning Dashboard
    this.appConfigs.set('learning-dashboard', {
      appId: 'learning-dashboard',
      allowedOrigins: [
        'http://localhost:3001',
        'https://learn.luminar.app',
        ...(environment === 'development' ? ['http://127.0.0.1:3001'] : []),
      ],
      allowedMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Cross-App-Token',
        'X-Learning-Context',
        'X-User-Context',
        'X-Session-ID',
        'X-Device-ID',
        'X-App-ID',
      ],
      exposedHeaders: [
        'X-Learning-Progress',
        'X-Course-Status',
        'X-Request-ID',
        'X-Total-Count',
      ],
      credentials: true,
      maxAge: 86400,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });

    // Skills Assessment
    this.appConfigs.set('skills-assessment', {
      appId: 'skills-assessment',
      allowedOrigins: [
        'http://localhost:3002',
        'https://assess.luminar.app',
        ...(environment === 'development' ? ['http://127.0.0.1:3002'] : []),
      ],
      allowedMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Cross-App-Token',
        'X-Assessment-Context',
        'X-User-Context',
        'X-Session-ID',
        'X-Device-ID',
        'X-App-ID',
        'X-Assessment-Timer',
      ],
      exposedHeaders: [
        'X-Assessment-Status',
        'X-Time-Remaining',
        'X-Score',
        'X-Request-ID',
      ],
      credentials: true,
      maxAge: 86400,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });

    // Talent Analytics
    this.appConfigs.set('talent-analytics', {
      appId: 'talent-analytics',
      allowedOrigins: [
        'http://localhost:3003',
        'https://analytics.luminar.app',
        ...(environment === 'development' ? ['http://127.0.0.1:3003'] : []),
      ],
      allowedMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Cross-App-Token',
        'X-Analytics-Context',
        'X-User-Context',
        'X-Session-ID',
        'X-Device-ID',
        'X-App-ID',
        'X-Date-Range',
        'X-Filters',
      ],
      exposedHeaders: [
        'X-Analytics-Version',
        'X-Data-Freshness',
        'X-Request-ID',
        'X-Export-Status',
      ],
      credentials: true,
      maxAge: 86400,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });

    // Admin Portal
    this.appConfigs.set('admin-portal', {
      appId: 'admin-portal',
      allowedOrigins: [
        'http://localhost:3004',
        'https://admin.luminar.app',
        ...(environment === 'development' ? ['http://127.0.0.1:3004'] : []),
      ],
      allowedMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Cross-App-Token',
        'X-Admin-Context',
        'X-User-Context',
        'X-Session-ID',
        'X-Device-ID',
        'X-App-ID',
        'X-Tenant-ID',
        'X-Organization-ID',
      ],
      exposedHeaders: [
        'X-Admin-Version',
        'X-Permission-Level',
        'X-Request-ID',
        'X-Audit-Log-ID',
      ],
      credentials: true,
      maxAge: 86400,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    });

    this.logger.log(
      `Initialized CORS configs for ${this.appConfigs.size} applications`,
    );
  }

  /**
   * Validate CORS request
   */
  validateCorsRequest(
    origin: string,
    method: string,
    headers: string[],
    appId?: string,
  ): CorsValidationResult {
    // If no origin (mobile apps, server-to-server), allow in development
    if (!origin) {
      const environment = this.configService.get<string>(
        'NODE_ENV',
        'development',
      );
      if (environment === 'development') {
        return {
          allowed: true,
          reason: 'No origin - development mode',
        };
      } else {
        return {
          allowed: false,
          reason: 'No origin header in production',
        };
      }
    }

    // Try to identify app by origin if not provided
    const targetAppId = appId || this.identifyAppByOrigin(origin);

    if (!targetAppId) {
      return {
        allowed: false,
        reason: `Origin ${origin} not recognized as L&D application`,
      };
    }

    const appConfig = this.appConfigs.get(targetAppId);
    if (!appConfig) {
      return {
        allowed: false,
        reason: `App ${targetAppId} not configured`,
      };
    }

    // Check origin
    if (!this.isOriginAllowed(origin, appConfig)) {
      return {
        allowed: false,
        appId: targetAppId,
        reason: `Origin ${origin} not allowed for app ${targetAppId}`,
      };
    }

    // Check method
    if (!appConfig.allowedMethods.includes(method)) {
      return {
        allowed: false,
        appId: targetAppId,
        reason: `Method ${method} not allowed for app ${targetAppId}`,
      };
    }

    // Check headers (for preflight requests)
    const disallowedHeaders = headers.filter(
      (header) => !this.isHeaderAllowed(header, appConfig),
    );

    if (disallowedHeaders.length > 0) {
      return {
        allowed: false,
        appId: targetAppId,
        reason: `Headers not allowed: ${disallowedHeaders.join(', ')}`,
      };
    }

    return {
      allowed: true,
      appId: targetAppId,
      headers: this.getCorsHeaders(appConfig),
    };
  }

  /**
   * Get CORS configuration for specific app
   */
  getAppCorsConfig(appId: string): AppCorsConfig | undefined {
    return this.appConfigs.get(appId);
  }

  /**
   * Get all app CORS configurations
   */
  getAllAppConfigs(): AppCorsConfig[] {
    return Array.from(this.appConfigs.values());
  }

  /**
   * Update app CORS configuration
   */
  updateAppConfig(appId: string, config: Partial<AppCorsConfig>): boolean {
    const existingConfig = this.appConfigs.get(appId);
    if (!existingConfig) {
      return false;
    }

    const updatedConfig = { ...existingConfig, ...config };
    this.appConfigs.set(appId, updatedConfig);

    this.logger.log(`Updated CORS config for app ${appId}`);
    return true;
  }

  /**
   * Add new origin to app
   */
  addOriginToApp(appId: string, origin: string): boolean {
    const config = this.appConfigs.get(appId);
    if (!config) {
      return false;
    }

    if (!config.allowedOrigins.includes(origin)) {
      config.allowedOrigins.push(origin);
      this.logger.log(`Added origin ${origin} to app ${appId}`);
    }

    return true;
  }

  /**
   * Remove origin from app
   */
  removeOriginFromApp(appId: string, origin: string): boolean {
    const config = this.appConfigs.get(appId);
    if (!config) {
      return false;
    }

    const index = config.allowedOrigins.indexOf(origin);
    if (index > -1) {
      config.allowedOrigins.splice(index, 1);
      this.logger.log(`Removed origin ${origin} from app ${appId}`);
    }

    return true;
  }

  /**
   * Check if origin is allowed for any app
   */
  isOriginAllowedAnywhere(origin: string): boolean {
    for (const config of this.appConfigs.values()) {
      if (this.isOriginAllowed(origin, config)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Get CORS headers for response
   */
  getCorsResponseHeaders(
    origin: string,
    method: string,
    requestHeaders: string[],
    appId?: string,
  ): Record<string, string> {
    const validation = this.validateCorsRequest(
      origin,
      method,
      requestHeaders,
      appId,
    );

    if (!validation.allowed) {
      return {};
    }

    const config = this.appConfigs.get(validation.appId!);
    if (!config) {
      return {};
    }

    const headers: Record<string, string> = {
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': config.allowedMethods.join(', '),
      'Access-Control-Allow-Headers': config.allowedHeaders.join(', '),
      'Access-Control-Expose-Headers': config.exposedHeaders.join(', '),
      'Access-Control-Max-Age': config.maxAge.toString(),
    };

    if (config.credentials) {
      headers['Access-Control-Allow-Credentials'] = 'true';
    }

    return headers;
  }

  /**
   * Generate CORS middleware configuration
   */
  generateCorsMiddlewareConfig(): any {
    const self = this;

    return {
      origin: function (origin: string, callback: Function) {
        const validation = self.validateCorsRequest(origin, 'GET', []);

        if (validation.allowed) {
          callback(null, true);
        } else {
          callback(
            new Error(validation.reason || 'CORS policy violation'),
            false,
          );
        }
      },
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: this.getAllowedHeaders(),
      exposedHeaders: this.getExposedHeaders(),
      credentials: true,
      maxAge: 86400,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    };
  }

  private identifyAppByOrigin(origin: string): string | null {
    for (const [appId, config] of this.appConfigs) {
      if (this.isOriginAllowed(origin, config)) {
        return appId;
      }
    }
    return null;
  }

  private isOriginAllowed(origin: string, config: AppCorsConfig): boolean {
    // Exact match
    if (config.allowedOrigins.includes(origin)) {
      return true;
    }

    // Development localhost patterns
    const environment = this.configService.get<string>(
      'NODE_ENV',
      'development',
    );
    if (environment === 'development') {
      const localhostPattern = /^https?:\/\/(localhost|127\.0\.0\.1)(:\d+)?$/;
      if (localhostPattern.test(origin)) {
        // Check if any allowed origin is localhost on any port
        return config.allowedOrigins.some(
          (allowed) =>
            allowed.includes('localhost') || allowed.includes('127.0.0.1'),
        );
      }
    }

    return false;
  }

  private isHeaderAllowed(header: string, config: AppCorsConfig): boolean {
    // Case-insensitive header check
    const lowerHeader = header.toLowerCase();
    return config.allowedHeaders.some(
      (allowed) => allowed.toLowerCase() === lowerHeader,
    );
  }

  private getCorsHeaders(config: AppCorsConfig): Record<string, string> {
    return {
      'Access-Control-Allow-Methods': config.allowedMethods.join(', '),
      'Access-Control-Allow-Headers': config.allowedHeaders.join(', '),
      'Access-Control-Expose-Headers': config.exposedHeaders.join(', '),
      'Access-Control-Max-Age': config.maxAge.toString(),
      'Access-Control-Allow-Credentials': config.credentials.toString(),
    };
  }

  private getAllowedHeaders(): string[] {
    const headers = new Set<string>();

    for (const config of this.appConfigs.values()) {
      config.allowedHeaders.forEach((header) => headers.add(header));
    }

    return Array.from(headers);
  }

  private getExposedHeaders(): string[] {
    const headers = new Set<string>();

    for (const config of this.appConfigs.values()) {
      config.exposedHeaders.forEach((header) => headers.add(header));
    }

    return Array.from(headers);
  }

  /**
   * Security monitoring - track CORS violations
   */
  logCorsViolation(
    origin: string,
    method: string,
    headers: string[],
    reason: string,
    userAgent?: string,
    ip?: string,
  ): void {
    this.logger.warn(`CORS Violation: ${reason}`, {
      origin,
      method,
      headers,
      userAgent,
      ip,
      timestamp: new Date().toISOString(),
    });
  }
}
