import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import {
  CorsManagerService,
  AppCorsConfig,
} from '../services/cors-manager.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

interface CorsValidationRequestDto {
  origin: string;
  method: string;
  headers: string[];
  appId?: string;
}

interface AddOriginDto {
  origin: string;
}

interface UpdateCorsConfigDto {
  allowedOrigins?: string[];
  allowedMethods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
}

@ApiTags('CORS Management')
@Controller('auth/cors')
export class CorsController {
  private readonly logger = new Logger(CorsController.name);

  constructor(private readonly corsManagerService: CorsManagerService) {}

  @Get('config')
  @ApiOperation({ summary: 'Get all CORS configurations' })
  @ApiResponse({ status: 200, description: 'CORS configurations retrieved' })
  async getAllCorsConfigs() {
    try {
      const configs = this.corsManagerService.getAllAppConfigs();

      return {
        success: true,
        data: {
          totalApps: configs.length,
          configs: configs.map((config) => ({
            appId: config.appId,
            allowedOrigins: config.allowedOrigins,
            allowedMethods: config.allowedMethods,
            allowedHeaders: config.allowedHeaders,
            exposedHeaders: config.exposedHeaders,
            credentials: config.credentials,
            maxAge: config.maxAge,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get CORS configs: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve CORS configurations',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('config/:appId')
  @ApiOperation({ summary: 'Get CORS configuration for specific app' })
  @ApiResponse({ status: 200, description: 'App CORS configuration retrieved' })
  @ApiResponse({ status: 404, description: 'App not found' })
  async getAppCorsConfig(@Param('appId') appId: string) {
    try {
      const config = this.corsManagerService.getAppCorsConfig(appId);

      if (!config) {
        throw new HttpException(
          {
            success: false,
            message: `App ${appId} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        data: {
          appId: config.appId,
          allowedOrigins: config.allowedOrigins,
          allowedMethods: config.allowedMethods,
          allowedHeaders: config.allowedHeaders,
          exposedHeaders: config.exposedHeaders,
          credentials: config.credentials,
          maxAge: config.maxAge,
          preflightContinue: config.preflightContinue,
          optionsSuccessStatus: config.optionsSuccessStatus,
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to get CORS config for ${appId}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve CORS configuration',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('validate')
  @ApiOperation({ summary: 'Validate CORS request' })
  @ApiResponse({ status: 200, description: 'CORS validation result' })
  async validateCorsRequest(@Body() requestDto: CorsValidationRequestDto) {
    try {
      const validation = this.corsManagerService.validateCorsRequest(
        requestDto.origin,
        requestDto.method,
        requestDto.headers,
        requestDto.appId,
      );

      return {
        success: true,
        data: {
          allowed: validation.allowed,
          appId: validation.appId,
          reason: validation.reason,
          headers: validation.headers,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error(`CORS validation failed: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'CORS validation failed',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('headers')
  @ApiOperation({ summary: 'Get CORS headers for response' })
  @ApiResponse({ status: 200, description: 'CORS headers generated' })
  async getCorsHeaders(
    @Body() requestDto: CorsValidationRequestDto,
    @Headers() requestHeaders: Record<string, string>,
  ) {
    try {
      const headers = this.corsManagerService.getCorsResponseHeaders(
        requestDto.origin,
        requestDto.method,
        requestDto.headers,
        requestDto.appId,
      );

      return {
        success: true,
        data: {
          corsHeaders: headers,
          origin: requestDto.origin,
          method: requestDto.method,
          appId: requestDto.appId,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to generate CORS headers: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to generate CORS headers',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('config/:appId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update CORS configuration for app (admin only)' })
  @ApiResponse({ status: 200, description: 'CORS configuration updated' })
  @ApiResponse({ status: 403, description: 'Admin access required' })
  async updateAppCorsConfig(
    @Param('appId') appId: string,
    @Body() updateDto: UpdateCorsConfigDto,
    @Request() req: any,
  ) {
    try {
      // Check if user has admin role
      const user = req.user;
      if (!user.roles?.includes('admin')) {
        throw new HttpException(
          {
            success: false,
            message: 'Admin access required',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const updated = this.corsManagerService.updateAppConfig(appId, updateDto);

      if (!updated) {
        throw new HttpException(
          {
            success: false,
            message: `App ${appId} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      const updatedConfig = this.corsManagerService.getAppCorsConfig(appId);

      return {
        success: true,
        data: {
          appId,
          message: 'CORS configuration updated successfully',
          config: updatedConfig,
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update CORS config for ${appId}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: 'Failed to update CORS configuration',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('config/:appId/origins')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Add origin to app CORS configuration (admin only)',
  })
  @ApiResponse({ status: 201, description: 'Origin added successfully' })
  async addOriginToApp(
    @Param('appId') appId: string,
    @Body() addOriginDto: AddOriginDto,
    @Request() req: any,
  ) {
    try {
      // Check if user has admin role
      const user = req.user;
      if (!user.roles?.includes('admin')) {
        throw new HttpException(
          {
            success: false,
            message: 'Admin access required',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const added = this.corsManagerService.addOriginToApp(
        appId,
        addOriginDto.origin,
      );

      if (!added) {
        throw new HttpException(
          {
            success: false,
            message: `App ${appId} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        data: {
          appId,
          origin: addOriginDto.origin,
          message: 'Origin added successfully',
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to add origin to ${appId}: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to add origin',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('config/:appId/origins/:origin')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Remove origin from app CORS configuration (admin only)',
  })
  @ApiResponse({ status: 200, description: 'Origin removed successfully' })
  async removeOriginFromApp(
    @Param('appId') appId: string,
    @Param('origin') origin: string,
    @Request() req: any,
  ) {
    try {
      // Check if user has admin role
      const user = req.user;
      if (!user.roles?.includes('admin')) {
        throw new HttpException(
          {
            success: false,
            message: 'Admin access required',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      // Decode origin parameter
      const decodedOrigin = decodeURIComponent(origin);

      const removed = this.corsManagerService.removeOriginFromApp(
        appId,
        decodedOrigin,
      );

      if (!removed) {
        throw new HttpException(
          {
            success: false,
            message: `App ${appId} not found`,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        success: true,
        data: {
          appId,
          origin: decodedOrigin,
          message: 'Origin removed successfully',
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to remove origin from ${appId}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: 'Failed to remove origin',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('check/origin')
  @ApiOperation({ summary: 'Check if origin is allowed' })
  @ApiResponse({ status: 200, description: 'Origin check result' })
  async checkOrigin(@Query('origin') origin: string) {
    try {
      if (!origin) {
        throw new HttpException(
          {
            success: false,
            message: 'Origin parameter is required',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const allowed = this.corsManagerService.isOriginAllowedAnywhere(origin);

      return {
        success: true,
        data: {
          origin,
          allowed,
          message: allowed
            ? 'Origin is allowed'
            : 'Origin is not allowed by any L&D application',
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to check origin: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to check origin',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('middleware/config')
  @ApiOperation({ summary: 'Get CORS middleware configuration' })
  @ApiResponse({ status: 200, description: 'CORS middleware config generated' })
  async getCorsMiddlewareConfig() {
    try {
      const config = this.corsManagerService.generateCorsMiddlewareConfig();

      return {
        success: true,
        data: {
          middlewareConfig: {
            // Don't expose the actual function, just the configuration
            allowedMethods: config.methods,
            allowedHeaders: config.allowedHeaders,
            exposedHeaders: config.exposedHeaders,
            credentials: config.credentials,
            maxAge: config.maxAge,
            preflightContinue: config.preflightContinue,
            optionsSuccessStatus: config.optionsSuccessStatus,
          },
          message: 'CORS middleware configuration generated',
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate middleware config: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: 'Failed to generate CORS middleware configuration',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for CORS service' })
  @ApiResponse({ status: 200, description: 'CORS service health status' })
  async healthCheck() {
    try {
      const configs = this.corsManagerService.getAllAppConfigs();

      return {
        success: true,
        data: {
          service: 'cors-management',
          status: 'healthy',
          configuredApps: configs.length,
          appIds: configs.map((config) => config.appId),
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          service: 'cors-management',
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }
}
