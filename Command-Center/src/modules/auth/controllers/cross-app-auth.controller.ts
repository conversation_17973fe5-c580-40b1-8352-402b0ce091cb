import {
  Controller,
  Post,
  Get,
  Body,
  Headers,
  Query,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import {
  CrossAppAuthService,
  AppTokenRequest,
} from '../services/cross-app-auth.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

interface CrossAppTokenRequestDto {
  targetAppId: string;
  permissions?: string[];
  sessionData?: Record<string, any>;
}

interface AppCredentialsDto {
  appId: string;
  appSecret: string;
}

interface TokenExchangeDto {
  token: string;
  targetAppId: string;
  permissions?: string[];
}

interface TokenVerificationDto {
  token: string;
  appId: string;
}

@ApiTags('Cross-App Authentication')
@Controller('auth/cross-app')
export class CrossAppAuthController {
  private readonly logger = new Logger(CrossAppAuthController.name);

  constructor(private readonly crossAppAuthService: CrossAppAuthService) {}

  @Post('token/generate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Generate cross-app token for authenticated user' })
  @ApiResponse({
    status: 201,
    description: 'Cross-app token generated successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid request parameters' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async generateCrossAppToken(
    @Request() req: any,
    @Body() tokenRequest: CrossAppTokenRequestDto,
  ) {
    try {
      const user = req.user;

      // Get user-specific permissions for the target app
      const appPermissions = this.crossAppAuthService.getUserPermissionsForApp(
        user,
        tokenRequest.targetAppId,
      );

      // Merge requested permissions with user's app permissions
      const finalPermissions = tokenRequest.permissions
        ? tokenRequest.permissions.filter((p) => appPermissions.includes(p))
        : appPermissions;

      const request: AppTokenRequest = {
        targetAppId: tokenRequest.targetAppId,
        permissions: finalPermissions,
        sessionData: tokenRequest.sessionData,
      };

      const token = await this.crossAppAuthService.generateCrossAppToken(
        user,
        request,
        'command-center',
      );

      this.logger.log(
        `Generated cross-app token for user ${user.id} targeting ${tokenRequest.targetAppId}`,
      );

      return {
        success: true,
        data: {
          token,
          targetApp: tokenRequest.targetAppId,
          permissions: finalPermissions,
          expiresIn:
            this.crossAppAuthService.getAppConfig(tokenRequest.targetAppId)
              ?.tokenExpiryMinutes * 60,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to generate cross-app token: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to generate cross-app token',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('token/exchange')
  @ApiOperation({ summary: 'Exchange regular JWT for cross-app token' })
  @ApiResponse({ status: 201, description: 'Token exchanged successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token or request' })
  async exchangeToken(@Body() exchangeRequest: TokenExchangeDto) {
    try {
      const request: AppTokenRequest = {
        targetAppId: exchangeRequest.targetAppId,
        permissions: exchangeRequest.permissions || [],
      };

      const crossAppToken =
        await this.crossAppAuthService.exchangeTokenForCrossApp(
          exchangeRequest.token,
          request,
        );

      return {
        success: true,
        data: {
          crossAppToken,
          targetApp: exchangeRequest.targetAppId,
        },
      };
    } catch (error) {
      this.logger.error(`Token exchange failed: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Token exchange failed',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('token/verify')
  @ApiOperation({ summary: 'Verify cross-app token' })
  @ApiResponse({ status: 200, description: 'Token verified successfully' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  async verifyToken(@Body() verifyRequest: TokenVerificationDto) {
    try {
      const payload = await this.crossAppAuthService.verifyCrossAppToken(
        verifyRequest.token,
        verifyRequest.appId,
      );

      return {
        success: true,
        data: {
          valid: true,
          payload: {
            userId: payload.sub,
            email: payload.email,
            roles: payload.roles,
            permissions: payload.crossAppPermissions,
            appId: payload.appId,
            sessionId: payload.sessionId,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        data: {
          valid: false,
          error: error.message,
        },
      };
    }
  }

  @Post('token/refresh')
  @ApiOperation({ summary: 'Refresh cross-app token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  async refreshToken(@Body() refreshRequest: { token: string; appId: string }) {
    try {
      const newToken = await this.crossAppAuthService.refreshCrossAppToken(
        refreshRequest.token,
        refreshRequest.appId,
      );

      return {
        success: true,
        data: {
          token: newToken,
          appId: refreshRequest.appId,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Token refresh failed',
          error: error.message,
        },
        HttpStatus.UNAUTHORIZED,
      );
    }
  }

  @Post('app/validate')
  @ApiOperation({ summary: 'Validate app credentials' })
  @ApiResponse({ status: 200, description: 'App credentials validated' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async validateAppCredentials(@Body() credentials: AppCredentialsDto) {
    const isValid = this.crossAppAuthService.validateAppCredentials(
      credentials.appId,
      credentials.appSecret,
    );

    if (!isValid) {
      throw new HttpException(
        {
          success: false,
          message: 'Invalid app credentials',
        },
        HttpStatus.UNAUTHORIZED,
      );
    }

    const appConfig = this.crossAppAuthService.getAppConfig(credentials.appId);

    return {
      success: true,
      data: {
        appId: credentials.appId,
        allowedOrigins: appConfig?.allowedOrigins,
        tokenExpiryMinutes: appConfig?.tokenExpiryMinutes,
      },
    };
  }

  @Get('apps/config')
  @ApiOperation({ summary: 'Get available apps configuration' })
  @ApiResponse({ status: 200, description: 'Apps configuration retrieved' })
  async getAppsConfig(@Query('origin') origin?: string) {
    const apps = [
      'command-center',
      'learning-dashboard',
      'skills-assessment',
      'talent-analytics',
      'admin-portal',
    ];
    const appsConfig = [];

    for (const appId of apps) {
      const config = this.crossAppAuthService.getAppConfig(appId);
      if (
        config &&
        (!origin ||
          this.crossAppAuthService.isOriginAllowedForApp(appId, origin))
      ) {
        appsConfig.push({
          appId: config.appId,
          allowedOrigins: config.allowedOrigins,
          tokenExpiryMinutes: config.tokenExpiryMinutes,
        });
      }
    }

    return {
      success: true,
      data: appsConfig,
    };
  }

  @Post('logout/all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Generate logout tokens for all apps' })
  @ApiResponse({ status: 200, description: 'Logout tokens generated' })
  async generateLogoutTokens(@Request() req: any) {
    try {
      const user = req.user;
      const logoutTokens =
        await this.crossAppAuthService.generateLogoutTokens(user);

      return {
        success: true,
        data: {
          userId: user.id,
          logoutTokens,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to generate logout tokens',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('logout/verify')
  @ApiOperation({ summary: 'Verify logout token' })
  @ApiResponse({ status: 200, description: 'Logout token verified' })
  async verifyLogoutToken(
    @Body() verifyRequest: { token: string; appId: string },
  ) {
    try {
      const result = await this.crossAppAuthService.verifyLogoutToken(
        verifyRequest.token,
        verifyRequest.appId,
      );

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Get('permissions/:appId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user permissions for specific app' })
  @ApiResponse({ status: 200, description: 'User permissions retrieved' })
  async getUserAppPermissions(
    @Request() req: any,
    @Query('appId') appId: string,
  ) {
    const user = req.user;
    const permissions = this.crossAppAuthService.getUserPermissionsForApp(
      user,
      appId,
    );

    return {
      success: true,
      data: {
        userId: user.id,
        appId,
        permissions,
      },
    };
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check for cross-app auth service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck() {
    return {
      success: true,
      data: {
        service: 'cross-app-auth',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        availableApps: 5,
      },
    };
  }
}
