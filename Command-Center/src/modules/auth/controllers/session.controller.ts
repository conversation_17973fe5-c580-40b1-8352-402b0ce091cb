import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import {
  RedisSessionService,
  SessionData,
} from '../services/redis-session.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

interface SessionActivityUpdateDto {
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

interface SessionInvalidationDto {
  userId?: string;
  ipAddress?: string;
  deviceId?: string;
  beforeTimestamp?: number;
}

@ApiTags('Session Management')
@Controller('auth/sessions')
export class SessionController {
  private readonly logger = new Logger(SessionController.name);

  constructor(private readonly sessionService: RedisSessionService) {}

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user sessions' })
  @ApiResponse({
    status: 200,
    description: 'User sessions retrieved successfully',
  })
  async getCurrentUserSessions(@Request() req: any) {
    try {
      const user = req.user;
      const sessions = await this.sessionService.getUserSessions(user.id);

      return {
        success: true,
        data: {
          userId: user.id,
          totalSessions: sessions.length,
          sessions: sessions.map((session) => ({
            deviceId: session.deviceId,
            userAgent: session.userAgent,
            ipAddress: session.ipAddress,
            lastActivity: new Date(session.lastActivity).toISOString(),
            createdAt: new Date(session.createdAt).toISOString(),
            expiresAt: new Date(session.expiresAt).toISOString(),
            isActive: session.isActive,
            loginMethod: session.loginMethod,
            deviceInfo: session.deviceInfo,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get user sessions: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve sessions',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':sessionId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get specific session data' })
  @ApiResponse({ status: 200, description: 'Session data retrieved' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async getSession(@Param('sessionId') sessionId: string, @Request() req: any) {
    try {
      const sessionData = await this.sessionService.getSession(sessionId);

      if (!sessionData) {
        throw new HttpException(
          {
            success: false,
            message: 'Session not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Verify user owns this session
      const user = req.user;
      if (sessionData.userId !== user.id) {
        throw new HttpException(
          {
            success: false,
            message: 'Unauthorized to access this session',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      return {
        success: true,
        data: {
          sessionId,
          ...sessionData,
          lastActivity: new Date(sessionData.lastActivity).toISOString(),
          createdAt: new Date(sessionData.createdAt).toISOString(),
          expiresAt: new Date(sessionData.expiresAt).toISOString(),
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to get session ${sessionId}: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve session',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':sessionId/activity')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update session activity' })
  @ApiResponse({ status: 200, description: 'Session activity updated' })
  async updateSessionActivity(
    @Param('sessionId') sessionId: string,
    @Body() updateData: SessionActivityUpdateDto,
    @Request() req: any,
  ) {
    try {
      const sessionData = await this.sessionService.getSession(sessionId);

      if (!sessionData) {
        throw new HttpException(
          {
            success: false,
            message: 'Session not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Verify user owns this session
      const user = req.user;
      if (sessionData.userId !== user.id) {
        throw new HttpException(
          {
            success: false,
            message: 'Unauthorized to update this session',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const updated = await this.sessionService.updateSessionActivity(
        sessionId,
        updateData,
      );

      if (!updated) {
        throw new HttpException(
          {
            success: false,
            message: 'Failed to update session - session may have expired',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      return {
        success: true,
        data: {
          sessionId,
          message: 'Session activity updated successfully',
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update session activity ${sessionId}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: 'Failed to update session activity',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':sessionId/extend')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Extend session expiration' })
  @ApiResponse({ status: 200, description: 'Session extended successfully' })
  async extendSession(
    @Param('sessionId') sessionId: string,
    @Body() extendData: { additionalSeconds?: number },
    @Request() req: any,
  ) {
    try {
      const sessionData = await this.sessionService.getSession(sessionId);

      if (!sessionData) {
        throw new HttpException(
          {
            success: false,
            message: 'Session not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Verify user owns this session
      const user = req.user;
      if (sessionData.userId !== user.id) {
        throw new HttpException(
          {
            success: false,
            message: 'Unauthorized to extend this session',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const additionalSeconds = extendData.additionalSeconds || 3600; // Default 1 hour
      const extended = await this.sessionService.extendSession(
        sessionId,
        additionalSeconds,
      );

      if (!extended) {
        throw new HttpException(
          {
            success: false,
            message: 'Failed to extend session',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      return {
        success: true,
        data: {
          sessionId,
          additionalSeconds,
          message: 'Session extended successfully',
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to extend session ${sessionId}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: 'Failed to extend session',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':sessionId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Destroy specific session' })
  @ApiResponse({ status: 200, description: 'Session destroyed successfully' })
  async destroySession(
    @Param('sessionId') sessionId: string,
    @Request() req: any,
  ) {
    try {
      const sessionData = await this.sessionService.getSession(sessionId);

      if (!sessionData) {
        throw new HttpException(
          {
            success: false,
            message: 'Session not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Verify user owns this session
      const user = req.user;
      if (sessionData.userId !== user.id) {
        throw new HttpException(
          {
            success: false,
            message: 'Unauthorized to destroy this session',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      await this.sessionService.destroySession(sessionId);

      return {
        success: true,
        data: {
          sessionId,
          message: 'Session destroyed successfully',
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to destroy session ${sessionId}: ${error.message}`,
      );
      throw new HttpException(
        {
          success: false,
          message: 'Failed to destroy session',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('me/all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Destroy all user sessions except current' })
  @ApiResponse({ status: 200, description: 'Sessions destroyed successfully' })
  async destroyAllUserSessions(
    @Request() req: any,
    @Query('excludeCurrent') excludeCurrent: string = 'true',
  ) {
    try {
      const user = req.user;
      const currentSessionId =
        excludeCurrent === 'true' ? req.user.sessionId : undefined;

      const destroyedCount = await this.sessionService.destroyUserSessions(
        user.id,
        currentSessionId,
      );

      return {
        success: true,
        data: {
          userId: user.id,
          destroyedCount,
          message: `${destroyedCount} sessions destroyed successfully`,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to destroy user sessions: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to destroy sessions',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('metrics/overview')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get session metrics (admin only)' })
  @ApiResponse({ status: 200, description: 'Session metrics retrieved' })
  async getSessionMetrics(@Request() req: any) {
    try {
      // Check if user has admin role
      const user = req.user;
      if (!user.roles?.includes('admin')) {
        throw new HttpException(
          {
            success: false,
            message: 'Admin access required',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const metrics = await this.sessionService.getSessionMetrics();
      const activeCount = await this.sessionService.getActiveSessionsCount();

      return {
        success: true,
        data: {
          ...metrics,
          currentActiveSessions: activeCount,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to get session metrics: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve session metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('invalidate/criteria')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Invalidate sessions by criteria (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Sessions invalidated successfully',
  })
  async invalidateSessionsByCriteria(
    @Body() criteria: SessionInvalidationDto,
    @Request() req: any,
  ) {
    try {
      // Check if user has admin role
      const user = req.user;
      if (!user.roles?.includes('admin')) {
        throw new HttpException(
          {
            success: false,
            message: 'Admin access required',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const invalidatedCount =
        await this.sessionService.invalidateSessionsByCriteria(criteria);

      return {
        success: true,
        data: {
          criteria,
          invalidatedCount,
          message: `${invalidatedCount} sessions invalidated successfully`,
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to invalidate sessions: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to invalidate sessions',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('security/ip/:ipAddress')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get sessions by IP address (security monitoring)' })
  @ApiResponse({ status: 200, description: 'Sessions by IP retrieved' })
  async getSessionsByIP(
    @Param('ipAddress') ipAddress: string,
    @Request() req: any,
  ) {
    try {
      // Check if user has admin role
      const user = req.user;
      if (!user.roles?.includes('admin')) {
        throw new HttpException(
          {
            success: false,
            message: 'Admin access required',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const sessions = await this.sessionService.getSessionsByIP(ipAddress);

      return {
        success: true,
        data: {
          ipAddress,
          sessionCount: sessions.length,
          sessions: sessions.map((session) => ({
            userId: session.userId,
            email: session.email,
            deviceId: session.deviceId,
            userAgent: session.userAgent,
            lastActivity: new Date(session.lastActivity).toISOString(),
            createdAt: new Date(session.createdAt).toISOString(),
            loginMethod: session.loginMethod,
            deviceInfo: session.deviceInfo,
          })),
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to get sessions by IP: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve sessions',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('cleanup/expired')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Clean up expired sessions (admin only)' })
  @ApiResponse({ status: 200, description: 'Expired sessions cleaned up' })
  async cleanupExpiredSessions(@Request() req: any) {
    try {
      // Check if user has admin role
      const user = req.user;
      if (!user.roles?.includes('admin')) {
        throw new HttpException(
          {
            success: false,
            message: 'Admin access required',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      const cleanedCount = await this.sessionService.cleanupExpiredSessions();

      return {
        success: true,
        data: {
          cleanedCount,
          message: `${cleanedCount} expired sessions cleaned up`,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to cleanup expired sessions: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          message: 'Failed to cleanup sessions',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health/check')
  @ApiOperation({ summary: 'Health check for session service' })
  @ApiResponse({ status: 200, description: 'Session service health status' })
  async healthCheck() {
    try {
      const activeCount = await this.sessionService.getActiveSessionsCount();

      return {
        success: true,
        data: {
          service: 'session-management',
          status: 'healthy',
          activeSessionsCount: activeCount,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          service: 'session-management',
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }
}
