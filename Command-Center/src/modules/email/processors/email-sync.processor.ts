import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { EmailSyncService, SyncJobData } from '../services/email-sync.service';

@Processor('email-sync')
export class EmailSyncProcessor {
  private readonly logger = new Logger(EmailSyncProcessor.name);

  constructor(private readonly emailSyncService: EmailSyncService) {}

  @Process('sync-account')
  async handleSyncAccount(job: Job<SyncJobData>): Promise<void> {
    this.logger.log(`Processing email sync job ${job.id} for account ${job.data.accountId}`);
    
    try {
      await this.emailSyncService.processSyncJob(job.data);
      this.logger.log(`Email sync job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Email sync job ${job.id} failed`, error);
      throw error; // Re-throw to mark job as failed
    }
  }

  @Process('cleanup-old-logs')
  async handleCleanupOldLogs(job: Job): Promise<void> {
    this.logger.log('Processing cleanup old logs job');
    
    try {
      // TODO: Implement cleanup logic
      // This would remove old sync logs to prevent database bloat
      this.logger.log('Old logs cleanup completed');
    } catch (error) {
      this.logger.error('Cleanup old logs job failed', error);
      throw error;
    }
  }
}