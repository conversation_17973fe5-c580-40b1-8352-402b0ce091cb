import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { EmailSyncLog } from './email-sync-log.entity';

export enum EmailProvider {
  GMAIL = 'gmail',
  OUTLOOK = 'outlook',
  YAHOO = 'yahoo',
  IMAP = 'imap',
}

export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  REAUTH_REQUIRED = 'reauth_required',
}

@Entity('email_accounts')
@Index(['userId', 'provider'])
@Index(['userId', 'isActive'])
export class EmailAccount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  userId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  user: User;

  @Column({
    type: 'enum',
    enum: EmailProvider,
  })
  provider: EmailProvider;

  @Column()
  email: string;

  @Column({ nullable: true })
  displayName: string;

  @Column({
    type: 'enum',
    enum: AccountStatus,
    default: AccountStatus.ACTIVE,
  })
  status: AccountStatus;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: true })
  syncEnabled: boolean;

  @Column({ type: 'jsonb', nullable: true })
  credentials: {
    accessToken?: string;
    refreshToken?: string;
    tokenExpiry?: Date;
    scope?: string[];
    needsRefresh?: boolean;
  };

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    syncIntervalMinutes?: number;
    maxEmailsPerSync?: number;
    syncLabels?: string[];
    excludeLabels?: string[];
    enableRealTimeSync?: boolean;
    historyId?: string; // For Gmail incremental sync
  };

  @Column({ type: 'timestamp', nullable: true })
  lastSyncAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastSuccessfulSyncAt: Date;

  @Column({ type: 'text', nullable: true })
  lastSyncError: string;

  @Column({ type: 'int', default: 0 })
  consecutiveFailures: number;

  @Column({ type: 'text', nullable: true })
  disabledReason: string;

  @Column({ type: 'jsonb', nullable: true })
  syncStats: {
    totalEmailsSynced?: number;
    lastSyncEmailCount?: number;
    averageSyncTime?: number;
    successfulSyncs?: number;
    failedSyncs?: number;
  };

  @OneToMany(() => EmailSyncLog, (syncLog) => syncLog.emailAccount)
  syncLogs: EmailSyncLog[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  isTokenExpired(): boolean {
    if (!this.credentials?.tokenExpiry) return true;
    return new Date() > new Date(this.credentials.tokenExpiry);
  }

  needsReauth(): boolean {
    return this.status === AccountStatus.REAUTH_REQUIRED || this.isTokenExpired();
  }

  canSync(): boolean {
    return (
      this.isActive &&
      this.syncEnabled &&
      this.status === AccountStatus.ACTIVE &&
      !this.needsReauth()
    );
  }
}