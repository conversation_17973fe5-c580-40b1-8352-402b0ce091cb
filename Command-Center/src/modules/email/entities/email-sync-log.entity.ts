import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { EmailAccount } from './email-account.entity';

export enum SyncType {
  FULL = 'full',
  INCREMENTAL = 'incremental',
  MANUAL = 'manual',
  REAL_TIME = 'real_time',
}

export enum SyncStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  FAILED_RETRYABLE = 'failed_retryable',
  CANCELLED = 'cancelled',
}

@Entity('email_sync_logs')
@Index(['emailAccountId', 'createdAt'])
@Index(['status', 'createdAt'])
export class EmailSyncLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  emailAccountId: string;

  @ManyToOne(() => EmailAccount, (account) => account.syncLogs, {
    onDelete: 'CASCADE',
  })
  emailAccount: EmailAccount;

  @Column({
    type: 'enum',
    enum: SyncType,
  })
  syncType: SyncType;

  @Column({
    type: 'enum',
    enum: SyncStatus,
    default: SyncStatus.PENDING,
  })
  status: SyncStatus;

  @Column({ type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  completedAt: Date;

  @Column({ type: 'int', default: 0 })
  emailsProcessed: number;

  @Column({ type: 'int', default: 0 })
  emailsCreated: number;

  @Column({ type: 'int', default: 0 })
  emailsUpdated: number;

  @Column({ type: 'int', default: 0 })
  emailsSkipped: number;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'jsonb', nullable: true })
  errorDetails: {
    category?: 'authentication' | 'authorization' | 'rate_limit' | 'server_error' | 'timeout' | 'network' | 'unknown';
    message?: string;
    originalMessage?: string;
    stack?: string;
    statusCode?: number;
    timestamp?: Date;
    retryable?: boolean;
    retryDelay?: number;
  };

  @Column({ type: 'int', default: 0 })
  attemptCount: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    triggerReason?: string;
    userAgent?: string;
    historyId?: string;
    pageToken?: string;
    timeRange?: {
      from: Date;
      to: Date;
    };
    filters?: any;
  };

  @Column({ type: 'jsonb', nullable: true })
  performance: {
    totalDurationMs?: number;
    apiCallCount?: number;
    averageApiResponseTime?: number;
    memoryUsageMB?: number;
  };

  @CreateDateColumn()
  createdAt: Date;

  // Helper methods
  getDuration(): number | null {
    if (!this.startedAt || !this.completedAt) return null;
    return this.completedAt.getTime() - this.startedAt.getTime();
  }

  isCompleted(): boolean {
    return [SyncStatus.COMPLETED, SyncStatus.FAILED, SyncStatus.CANCELLED].includes(
      this.status,
    );
  }

  isSuccessful(): boolean {
    return this.status === SyncStatus.COMPLETED;
  }
}