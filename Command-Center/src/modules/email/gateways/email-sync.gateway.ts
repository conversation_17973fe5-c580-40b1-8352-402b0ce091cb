import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { OnEvent } from '@nestjs/event-emitter';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { EmailSyncService } from '../services/email-sync.service';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  accountIds?: string[];
}

@WebSocketGateway({
  namespace: 'email-sync',
  cors: {
    origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:5174'],
    credentials: true,
  },
})
export class EmailSyncGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(EmailSyncGateway.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();

  constructor(private readonly emailSyncService: EmailSyncService) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extract JWT token from handshake
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Client ${client.id} attempted connection without token`);
        client.disconnect();
        return;
      }

      // TODO: Validate JWT token and extract user info
      // For now, we'll use a mock user ID
      client.userId = 'mock-user-id';
      client.accountIds = ['mock-account-id'];

      this.connectedClients.set(client.id, client);
      this.logger.log(`Client ${client.id} connected for user ${client.userId}`);

      // Join user-specific room
      await client.join(`user:${client.userId}`);
      
      // Join account-specific rooms
      if (client.accountIds) {
        for (const accountId of client.accountIds) {
          await client.join(`account:${accountId}`);
        }
      }

      // Send initial connection confirmation
      client.emit('connected', {
        message: 'Connected to email sync service',
        userId: client.userId,
        accountIds: client.accountIds,
      });

    } catch (error) {
      this.logger.error(`Connection error for client ${client.id}:`, error);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    this.connectedClients.delete(client.id);
    this.logger.log(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('join-account')
  async handleJoinAccount(
    @MessageBody() data: { accountId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: 'Not authenticated' });
        return;
      }

      // TODO: Verify user has access to this account
      await client.join(`account:${data.accountId}`);
      
      if (!client.accountIds) {
        client.accountIds = [];
      }
      if (!client.accountIds.includes(data.accountId)) {
        client.accountIds.push(data.accountId);
      }

      this.logger.log(`Client ${client.id} joined account ${data.accountId}`);
      
      client.emit('account-joined', { accountId: data.accountId });

      // Send current sync status
      const syncStatus = await this.emailSyncService.getSyncStatus(data.accountId);
      if (syncStatus) {
        client.emit('sync-status', {
          accountId: data.accountId,
          status: syncStatus,
        });
      }

    } catch (error) {
      this.logger.error(`Error joining account for client ${client.id}:`, error);
      client.emit('error', { message: 'Failed to join account' });
    }
  }

  @SubscribeMessage('leave-account')
  async handleLeaveAccount(
    @MessageBody() data: { accountId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      await client.leave(`account:${data.accountId}`);
      
      if (client.accountIds) {
        client.accountIds = client.accountIds.filter(id => id !== data.accountId);
      }

      this.logger.log(`Client ${client.id} left account ${data.accountId}`);
      client.emit('account-left', { accountId: data.accountId });

    } catch (error) {
      this.logger.error(`Error leaving account for client ${client.id}:`, error);
      client.emit('error', { message: 'Failed to leave account' });
    }
  }

  @SubscribeMessage('get-sync-status')
  async handleGetSyncStatus(
    @MessageBody() data: { accountId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: 'Not authenticated' });
        return;
      }

      const syncStatus = await this.emailSyncService.getSyncStatus(data.accountId);
      
      client.emit('sync-status', {
        accountId: data.accountId,
        status: syncStatus,
      });

    } catch (error) {
      this.logger.error(`Error getting sync status for client ${client.id}:`, error);
      client.emit('error', { message: 'Failed to get sync status' });
    }
  }

  // Event listeners for email sync events

  @OnEvent('email.sync.started')
  handleSyncStarted(payload: { accountId: string; syncLogId: string; syncType: string }) {
    this.logger.log(`Sync started for account ${payload.accountId}`);
    
    this.server.to(`account:${payload.accountId}`).emit('sync-started', {
      accountId: payload.accountId,
      syncLogId: payload.syncLogId,
      syncType: payload.syncType,
      timestamp: new Date(),
    });
  }

  @OnEvent('email.sync.completed')
  handleSyncCompleted(payload: {
    accountId: string;
    syncLogId: string;
    syncType: string;
    emailsProcessed: number;
    duration: number;
  }) {
    this.logger.log(`Sync completed for account ${payload.accountId}: ${payload.emailsProcessed} emails processed`);
    
    this.server.to(`account:${payload.accountId}`).emit('sync-completed', {
      accountId: payload.accountId,
      syncLogId: payload.syncLogId,
      syncType: payload.syncType,
      emailsProcessed: payload.emailsProcessed,
      duration: payload.duration,
      timestamp: new Date(),
    });
  }

  @OnEvent('email.sync.failed')
  handleSyncFailed(payload: {
    accountId: string;
    syncLogId: string;
    syncType: string;
    error: string;
  }) {
    this.logger.log(`Sync failed for account ${payload.accountId}: ${payload.error}`);
    
    this.server.to(`account:${payload.accountId}`).emit('sync-failed', {
      accountId: payload.accountId,
      syncLogId: payload.syncLogId,
      syncType: payload.syncType,
      error: payload.error,
      timestamp: new Date(),
    });
  }

  @OnEvent('email.sync.progress')
  handleSyncProgress(payload: {
    accountId: string;
    syncLogId: string;
    processed: number;
    total: number;
    currentOperation: string;
  }) {
    this.server.to(`account:${payload.accountId}`).emit('sync-progress', {
      accountId: payload.accountId,
      syncLogId: payload.syncLogId,
      processed: payload.processed,
      total: payload.total,
      percentage: Math.round((payload.processed / payload.total) * 100),
      currentOperation: payload.currentOperation,
      timestamp: new Date(),
    });
  }

  @OnEvent('email.account.connected')
  handleAccountConnected(payload: {
    userId: string;
    accountId: string;
    provider: string;
    email: string;
  }) {
    this.logger.log(`New email account connected: ${payload.email} for user ${payload.userId}`);
    
    this.server.to(`user:${payload.userId}`).emit('account-connected', {
      accountId: payload.accountId,
      provider: payload.provider,
      email: payload.email,
      timestamp: new Date(),
    });
  }

  @OnEvent('email.account.disconnected')
  handleAccountDisconnected(payload: {
    userId: string;
    accountId: string;
    provider: string;
    email: string;
  }) {
    this.logger.log(`Email account disconnected: ${payload.email} for user ${payload.userId}`);
    
    this.server.to(`user:${payload.userId}`).emit('account-disconnected', {
      accountId: payload.accountId,
      provider: payload.provider,
      email: payload.email,
      timestamp: new Date(),
    });
  }

  @OnEvent('email.new-messages')
  handleNewMessages(payload: {
    accountId: string;
    threadIds: string[];
    messageCount: number;
  }) {
    this.logger.log(`New messages received for account ${payload.accountId}: ${payload.messageCount} messages`);
    
    this.server.to(`account:${payload.accountId}`).emit('new-messages', {
      accountId: payload.accountId,
      threadIds: payload.threadIds,
      messageCount: payload.messageCount,
      timestamp: new Date(),
    });
  }

  // Utility methods

  /**
   * Send message to all clients connected to a specific account
   */
  sendToAccount(accountId: string, event: string, data: any) {
    this.server.to(`account:${accountId}`).emit(event, data);
  }

  /**
   * Send message to all clients connected to a specific user
   */
  sendToUser(userId: string, event: string, data: any) {
    this.server.to(`user:${userId}`).emit(event, data);
  }

  /**
   * Get connected clients for a user
   */
  getUserClients(userId: string): AuthenticatedSocket[] {
    return Array.from(this.connectedClients.values()).filter(
      client => client.userId === userId
    );
  }

  /**
   * Get connected clients for an account
   */
  getAccountClients(accountId: string): AuthenticatedSocket[] {
    return Array.from(this.connectedClients.values()).filter(
      client => client.accountIds?.includes(accountId)
    );
  }
}