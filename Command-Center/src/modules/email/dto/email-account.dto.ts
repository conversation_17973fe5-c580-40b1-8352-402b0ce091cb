import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsString,
  IsBoolean,
  IsOptional,
  IsN<PERSON>ber,
  IsArray,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { EmailProvider, AccountStatus } from '../entities/email-account.entity';

export class CreateEmailAccountDto {
  @ApiProperty({
    description: 'Email provider',
    enum: EmailProvider,
    example: EmailProvider.GMAIL,
  })
  @IsEnum(EmailProvider)
  provider: EmailProvider;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Display name for the account',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  displayName?: string;

  @ApiPropertyOptional({
    description: 'Account settings',
  })
  @IsOptional()
  settings?: EmailAccountSettingsDto;
}

export class UpdateEmailAccountDto {
  @ApiPropertyOptional({
    description: 'Display name for the account',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  displayName?: string;

  @ApiPropertyOptional({
    description: 'Account status',
    enum: AccountStatus,
  })
  @IsOptional()
  @IsEnum(AccountStatus)
  status?: AccountStatus;

  @ApiPropertyOptional({
    description: 'Whether account is active',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Whether sync is enabled',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  syncEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Account settings',
  })
  @IsOptional()
  settings?: EmailAccountSettingsDto;
}

export class EmailAccountSettingsDto {
  @ApiPropertyOptional({
    description: 'Sync interval in minutes',
    example: 15,
    minimum: 1,
    maximum: 1440,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1440)
  syncIntervalMinutes?: number;

  @ApiPropertyOptional({
    description: 'Maximum emails per sync',
    example: 100,
    minimum: 1,
    maximum: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  maxEmailsPerSync?: number;

  @ApiPropertyOptional({
    description: 'Labels to sync',
    example: ['INBOX', 'IMPORTANT'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  syncLabels?: string[];

  @ApiPropertyOptional({
    description: 'Labels to exclude from sync',
    example: ['SPAM', 'TRASH'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeLabels?: string[];

  @ApiPropertyOptional({
    description: 'Enable real-time sync',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  enableRealTimeSync?: boolean;
}

export class OAuth2CallbackDto {
  @ApiProperty({
    description: 'Authorization code from OAuth2 provider',
    example: 'authorization_code_here',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Email provider',
    enum: EmailProvider,
    example: EmailProvider.GMAIL,
  })
  @IsEnum(EmailProvider)
  provider: EmailProvider;

  @ApiPropertyOptional({
    description: 'State parameter for security',
  })
  @IsOptional()
  @IsString()
  state?: string;
}

export class EmailSyncRequestDto {
  @ApiPropertyOptional({
    description: 'Force full sync instead of incremental',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  fullSync?: boolean;

  @ApiPropertyOptional({
    description: 'Specific labels to sync',
    example: ['INBOX'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  labels?: string[];
}

export class EmailAccountResponseDto {
  @ApiProperty({ description: 'Account ID' })
  id: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Email provider', enum: EmailProvider })
  provider: EmailProvider;

  @ApiProperty({ description: 'Email address' })
  email: string;

  @ApiPropertyOptional({ description: 'Display name' })
  displayName?: string;

  @ApiProperty({ description: 'Account status', enum: AccountStatus })
  status: AccountStatus;

  @ApiProperty({ description: 'Whether account is active' })
  isActive: boolean;

  @ApiProperty({ description: 'Whether sync is enabled' })
  syncEnabled: boolean;

  @ApiPropertyOptional({ description: 'Last sync timestamp' })
  lastSyncAt?: Date;

  @ApiPropertyOptional({ description: 'Last successful sync timestamp' })
  lastSuccessfulSyncAt?: Date;

  @ApiPropertyOptional({ description: 'Last sync error message' })
  lastSyncError?: string;

  @ApiPropertyOptional({ description: 'Sync statistics' })
  syncStats?: {
    totalEmailsSynced?: number;
    lastSyncEmailCount?: number;
    averageSyncTime?: number;
    successfulSyncs?: number;
    failedSyncs?: number;
  };

  @ApiProperty({ description: 'Account settings' })
  settings: EmailAccountSettingsDto;

  @ApiProperty({ description: 'Created timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated timestamp' })
  updatedAt: Date;
}