import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsString, IsOptional, IsDateString, IsArray, IsObject } from 'class-validator';
import { SyncType, SyncStatus } from '../entities/email-sync-log.entity';

export class EmailThreadDto {
  @ApiProperty({ description: 'Thread ID' })
  id: string;

  @ApiProperty({ description: 'Thread subject' })
  subject: string;

  @ApiProperty({ description: 'Thread snippet' })
  snippet: string;

  @ApiProperty({ description: 'Thread labels' })
  labels: string[];

  @ApiProperty({ description: 'Message count in thread' })
  messageCount: number;

  @ApiProperty({ description: 'Unread count in thread' })
  unreadCount: number;

  @ApiProperty({ description: 'Whether thread has starred messages' })
  hasStarred: boolean;

  @ApiProperty({ description: 'Whether thread is important' })
  isImportant: boolean;

  @ApiProperty({ description: 'Last message timestamp' })
  lastMessageTime: Date;

  @ApiProperty({ description: 'Thread participants' })
  participants: EmailParticipantDto[];

  @ApiProperty({ description: 'Messages in thread' })
  messages: EmailMessageDto[];
}

export class EmailMessageDto {
  @ApiProperty({ description: 'Message ID' })
  id: string;

  @ApiProperty({ description: 'Thread ID' })
  threadId: string;

  @ApiProperty({ description: 'Message subject' })
  subject: string;

  @ApiProperty({ description: 'Message snippet' })
  snippet: string;

  @ApiProperty({ description: 'Message body (HTML)' })
  bodyHtml?: string;

  @ApiProperty({ description: 'Message body (plain text)' })
  bodyText?: string;

  @ApiProperty({ description: 'Message sender' })
  from: EmailParticipantDto;

  @ApiProperty({ description: 'Message recipients' })
  to: EmailParticipantDto[];

  @ApiProperty({ description: 'Message CC recipients' })
  cc?: EmailParticipantDto[];

  @ApiProperty({ description: 'Message BCC recipients' })
  bcc?: EmailParticipantDto[];

  @ApiProperty({ description: 'Message labels' })
  labels: string[];

  @ApiProperty({ description: 'Whether message is unread' })
  isUnread: boolean;

  @ApiProperty({ description: 'Whether message is starred' })
  isStarred: boolean;

  @ApiProperty({ description: 'Whether message is important' })
  isImportant: boolean;

  @ApiProperty({ description: 'Message timestamp' })
  timestamp: Date;

  @ApiProperty({ description: 'Message attachments' })
  attachments?: EmailAttachmentDto[];

  @ApiProperty({ description: 'Internal message date' })
  internalDate: Date;

  @ApiProperty({ description: 'Message size in bytes' })
  sizeEstimate: number;
}

export class EmailParticipantDto {
  @ApiProperty({ description: 'Participant name' })
  name?: string;

  @ApiProperty({ description: 'Participant email address' })
  email: string;
}

export class EmailAttachmentDto {
  @ApiProperty({ description: 'Attachment ID' })
  id: string;

  @ApiProperty({ description: 'Attachment filename' })
  filename: string;

  @ApiProperty({ description: 'Attachment MIME type' })
  mimeType: string;

  @ApiProperty({ description: 'Attachment size in bytes' })
  size: number;

  @ApiProperty({ description: 'Whether attachment is inline' })
  isInline: boolean;
}

export class EmailSyncLogResponseDto {
  @ApiProperty({ description: 'Sync log ID' })
  id: string;

  @ApiProperty({ description: 'Email account ID' })
  emailAccountId: string;

  @ApiProperty({ description: 'Sync type', enum: SyncType })
  syncType: SyncType;

  @ApiProperty({ description: 'Sync status', enum: SyncStatus })
  status: SyncStatus;

  @ApiPropertyOptional({ description: 'Sync start timestamp' })
  startedAt?: Date;

  @ApiPropertyOptional({ description: 'Sync completion timestamp' })
  completedAt?: Date;

  @ApiProperty({ description: 'Number of emails processed' })
  emailsProcessed: number;

  @ApiProperty({ description: 'Number of emails created' })
  emailsCreated: number;

  @ApiProperty({ description: 'Number of emails updated' })
  emailsUpdated: number;

  @ApiProperty({ description: 'Number of emails skipped' })
  emailsSkipped: number;

  @ApiPropertyOptional({ description: 'Error message if sync failed' })
  errorMessage?: string;

  @ApiPropertyOptional({ description: 'Sync metadata' })
  metadata?: any;

  @ApiPropertyOptional({ description: 'Performance metrics' })
  performance?: {
    totalDurationMs?: number;
    apiCallCount?: number;
    averageApiResponseTime?: number;
    memoryUsageMB?: number;
  };

  @ApiProperty({ description: 'Created timestamp' })
  createdAt: Date;
}

export class EmailSearchDto {
  @ApiPropertyOptional({ description: 'Search query' })
  @IsOptional()
  @IsString()
  q?: string;

  @ApiPropertyOptional({ description: 'Labels to filter by' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  labels?: string[];

  @ApiPropertyOptional({ description: 'Maximum results to return' })
  @IsOptional()
  maxResults?: number;

  @ApiPropertyOptional({ description: 'Page token for pagination' })
  @IsOptional()
  @IsString()
  pageToken?: string;

  @ApiPropertyOptional({ description: 'Include spam and trash' })
  @IsOptional()
  includeSpamTrash?: boolean;
}

export class EmailSyncResponseDto {
  @ApiProperty({ description: 'Sync operation ID' })
  syncId: string;

  @ApiProperty({ description: 'Sync status', enum: SyncStatus })
  status: SyncStatus;

  @ApiProperty({ description: 'Status message' })
  message: string;

  @ApiPropertyOptional({ description: 'Estimated completion time' })
  estimatedCompletion?: Date;

  @ApiPropertyOptional({ description: 'Progress information' })
  progress?: {
    processed: number;
    total: number;
    percentage: number;
  };
}