import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { EmailService } from '../services/email.service';
import { EmailSyncService } from '../services/email-sync.service';
import { EmailProvider } from '../entities/email-account.entity';
import {
  CreateEmailAccountDto,
  UpdateEmailAccountDto,
  OAuth2CallbackDto,
  EmailAccountResponseDto,
  EmailSyncRequestDto,
} from '../dto/email-account.dto';
import {
  EmailThreadDto,
  EmailMessageDto,
  EmailSearchDto,
  EmailSyncResponseDto,
  EmailSyncLogResponseDto,
} from '../dto/email-sync.dto';

@ApiTags('Email Integration')
@Controller('api/email')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class EmailController {
  constructor(
    private readonly emailService: EmailService,
    private readonly emailSyncService: EmailSyncService,
  ) {}

  @Get('auth/:provider')
  @ApiOperation({ summary: 'Get OAuth2 authorization URL for email provider' })
  @ApiParam({ name: 'provider', enum: EmailProvider })
  @ApiResponse({
    status: 200,
    description: 'Authorization URL returned',
    schema: {
      type: 'object',
      properties: {
        authUrl: { type: 'string' },
        provider: { type: 'string' },
      },
    },
  })
  getAuthUrl(
    @Param('provider') provider: EmailProvider,
    @Request() req: any,
  ): { authUrl: string; provider: EmailProvider } {
    const authUrl = this.emailService.getAuthUrl(provider, req.user.id);
    return { authUrl, provider };
  }

  @Post('auth/callback')
  @ApiOperation({ summary: 'Handle OAuth2 callback from email provider' })
  @ApiResponse({
    status: 201,
    description: 'Email account connected successfully',
    type: EmailAccountResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid callback data' })
  async handleAuthCallback(
    @Body(ValidationPipe) callbackData: OAuth2CallbackDto,
    @Request() req: any,
  ): Promise<EmailAccountResponseDto> {
    return await this.emailService.handleOAuth2Callback(req.user.id, callbackData);
  }

  @Get('accounts')
  @ApiOperation({ summary: 'Get user email accounts' })
  @ApiResponse({
    status: 200,
    description: 'Email accounts retrieved successfully',
    type: [EmailAccountResponseDto],
  })
  async getEmailAccounts(@Request() req: any): Promise<EmailAccountResponseDto[]> {
    return await this.emailService.getEmailAccounts(req.user.id);
  }

  @Get('accounts/:accountId')
  @ApiOperation({ summary: 'Get specific email account' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiResponse({
    status: 200,
    description: 'Email account retrieved successfully',
    type: EmailAccountResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  async getEmailAccount(
    @Param('accountId') accountId: string,
    @Request() req: any,
  ): Promise<EmailAccountResponseDto> {
    return await this.emailService.getEmailAccount(req.user.id, accountId);
  }

  @Put('accounts/:accountId')
  @ApiOperation({ summary: 'Update email account settings' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiResponse({
    status: 200,
    description: 'Email account updated successfully',
    type: EmailAccountResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  async updateEmailAccount(
    @Param('accountId') accountId: string,
    @Body(ValidationPipe) updateData: UpdateEmailAccountDto,
    @Request() req: any,
  ): Promise<EmailAccountResponseDto> {
    return await this.emailService.updateEmailAccount(req.user.id, accountId, updateData);
  }

  @Delete('accounts/:accountId')
  @ApiOperation({ summary: 'Delete email account' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiResponse({ status: 204, description: 'Email account deleted successfully' })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteEmailAccount(
    @Param('accountId') accountId: string,
    @Request() req: any,
  ): Promise<void> {
    return await this.emailService.deleteEmailAccount(req.user.id, accountId);
  }

  @Post('accounts/:accountId/sync')
  @ApiOperation({ summary: 'Trigger email sync for account' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiResponse({
    status: 202,
    description: 'Email sync started successfully',
    type: EmailSyncResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  @ApiResponse({ status: 400, description: 'Account cannot be synced' })
  async syncAccount(
    @Param('accountId') accountId: string,
    @Body(ValidationPipe) syncRequest: EmailSyncRequestDto,
    @Request() req: any,
  ): Promise<EmailSyncResponseDto> {
    return await this.emailService.syncAccount(
      req.user.id,
      accountId,
      syncRequest.fullSync || false,
    );
  }

  @Get('accounts/:accountId/threads')
  @ApiOperation({ summary: 'Get email threads for account' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiQuery({ name: 'q', required: false, description: 'Search query' })
  @ApiQuery({ name: 'labels', required: false, description: 'Label filters (comma-separated)' })
  @ApiQuery({ name: 'maxResults', required: false, type: Number, description: 'Maximum results' })
  @ApiQuery({ name: 'pageToken', required: false, description: 'Pagination token' })
  @ApiQuery({ name: 'includeSpamTrash', required: false, type: Boolean, description: 'Include spam and trash' })
  @ApiResponse({
    status: 200,
    description: 'Email threads retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        threads: { type: 'array', items: { $ref: '#/components/schemas/EmailThreadDto' } },
        nextPageToken: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  @ApiResponse({ status: 401, description: 'Account not authenticated' })
  async getThreads(
    @Param('accountId') accountId: string,
    @Query() searchOptions: EmailSearchDto,
    @Request() req: any,
  ): Promise<{ threads: EmailThreadDto[]; nextPageToken?: string }> {
    // Parse comma-separated labels
    if (typeof searchOptions.labels === 'string') {
      searchOptions.labels = (searchOptions.labels as string).split(',').map(l => l.trim());
    }
    
    return await this.emailService.getThreads(req.user.id, accountId, searchOptions);
  }

  @Get('accounts/:accountId/threads/:threadId')
  @ApiOperation({ summary: 'Get specific email thread' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiParam({ name: 'threadId', description: 'Email thread ID' })
  @ApiResponse({
    status: 200,
    description: 'Email thread retrieved successfully',
    type: EmailThreadDto,
  })
  @ApiResponse({ status: 404, description: 'Email account or thread not found' })
  @ApiResponse({ status: 401, description: 'Account not authenticated' })
  async getThread(
    @Param('accountId') accountId: string,
    @Param('threadId') threadId: string,
    @Request() req: any,
  ): Promise<EmailThreadDto> {
    return await this.emailService.getThread(req.user.id, accountId, threadId);
  }

  @Post('accounts/:accountId/send')
  @ApiOperation({ summary: 'Send email message' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiResponse({
    status: 201,
    description: 'Email sent successfully',
    type: EmailMessageDto,
  })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  @ApiResponse({ status: 401, description: 'Account not authenticated' })
  async sendMessage(
    @Param('accountId') accountId: string,
    @Body() messageData: {
      to: string[];
      cc?: string[];
      bcc?: string[];
      subject: string;
      body: string;
      threadId?: string;
    },
    @Request() req: any,
  ): Promise<EmailMessageDto> {
    return await this.emailService.sendMessage(req.user.id, accountId, messageData);
  }

  @Get('accounts/:accountId/sync-logs')
  @ApiOperation({ summary: 'Get sync logs for account' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of logs to return' })
  @ApiResponse({
    status: 200,
    description: 'Sync logs retrieved successfully',
    type: [EmailSyncLogResponseDto],
  })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  async getSyncLogs(
    @Param('accountId') accountId: string,
    @Query('limit') limit?: number,
    @Request() req: any,
  ): Promise<EmailSyncLogResponseDto[]> {
    const logs = await this.emailService.getSyncLogs(
      req.user.id,
      accountId,
      limit ? parseInt(limit.toString()) : 20,
    );

    return logs.map(log => ({
      id: log.id,
      emailAccountId: log.emailAccountId,
      syncType: log.syncType,
      status: log.status,
      startedAt: log.startedAt,
      completedAt: log.completedAt,
      emailsProcessed: log.emailsProcessed,
      emailsCreated: log.emailsCreated,
      emailsUpdated: log.emailsUpdated,
      emailsSkipped: log.emailsSkipped,
      errorMessage: log.errorMessage,
      metadata: log.metadata,
      performance: log.performance,
      createdAt: log.createdAt,
    }));
  }

  @Get('accounts/:accountId/sync-status')
  @ApiOperation({ summary: 'Get current sync status for account' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiResponse({
    status: 200,
    description: 'Sync status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        isActive: { type: 'boolean' },
        currentSync: { $ref: '#/components/schemas/EmailSyncLogResponseDto' },
      },
    },
  })
  async getSyncStatus(
    @Param('accountId') accountId: string,
    @Request() req: any,
  ): Promise<{ isActive: boolean; currentSync?: EmailSyncLogResponseDto }> {
    // Verify account belongs to user
    await this.emailService.getEmailAccount(req.user.id, accountId);
    
    const currentSync = await this.emailSyncService.getSyncStatus(accountId);
    
    return {
      isActive: !!currentSync,
      ...(currentSync && {
        currentSync: {
          id: currentSync.id,
          emailAccountId: currentSync.emailAccountId,
          syncType: currentSync.syncType,
          status: currentSync.status,
          startedAt: currentSync.startedAt,
          completedAt: currentSync.completedAt,
          emailsProcessed: currentSync.emailsProcessed,
          emailsCreated: currentSync.emailsCreated,
          emailsUpdated: currentSync.emailsUpdated,
          emailsSkipped: currentSync.emailsSkipped,
          errorMessage: currentSync.errorMessage,
          metadata: currentSync.metadata,
          performance: currentSync.performance,
          createdAt: currentSync.createdAt,
        },
      }),
    };
  }

  @Delete('sync/:syncLogId')
  @ApiOperation({ summary: 'Cancel ongoing sync operation' })
  @ApiParam({ name: 'syncLogId', description: 'Sync log ID' })
  @ApiResponse({ status: 204, description: 'Sync cancelled successfully' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async cancelSync(@Param('syncLogId') syncLogId: string): Promise<void> {
    return await this.emailSyncService.cancelSync(syncLogId);
  }

  @Post('sync/schedule')
  @ApiOperation({ summary: 'Trigger scheduled sync for all accounts' })
  @ApiResponse({ status: 202, description: 'Scheduled sync triggered' })
  @HttpCode(HttpStatus.ACCEPTED)
  async scheduleSync(): Promise<{ message: string }> {
    await this.emailSyncService.schedulePeriodicSync();
    return { message: 'Scheduled sync triggered for all active accounts' };
  }

  @Get('accounts/:accountId/sync-status/detailed')
  @ApiOperation({ summary: 'Get detailed sync status with error analysis and health metrics' })
  @ApiParam({ name: 'accountId', description: 'Email account ID' })
  @ApiResponse({
    status: 200,
    description: 'Detailed sync status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        currentSync: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            startedAt: { type: 'string', format: 'date-time' },
            progress: {
              type: 'object',
              properties: {
                processed: { type: 'number' },
                estimated: { type: 'number' },
                percentage: { type: 'number' },
              },
            },
          },
        },
        recentSyncs: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              syncType: { type: 'string' },
              status: { type: 'string' },
              startedAt: { type: 'string', format: 'date-time' },
              completedAt: { type: 'string', format: 'date-time' },
              emailsProcessed: { type: 'number' },
              duration: { type: 'number' },
              errorDetails: { type: 'object' },
            },
          },
        },
        stats: {
          type: 'object',
          properties: {
            totalSyncs: { type: 'number' },
            successfulSyncs: { type: 'number' },
            failedSyncs: { type: 'number' },
            averageDuration: { type: 'number' },
            successRate: { type: 'number' },
            lastSuccessfulSync: { type: 'string', format: 'date-time' },
          },
        },
        health: {
          type: 'object',
          properties: {
            status: { type: 'string', enum: ['healthy', 'warning', 'error'] },
            issues: { type: 'array', items: { type: 'string' } },
            recommendations: { type: 'array', items: { type: 'string' } },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Email account not found' })
  async getDetailedSyncStatus(
    @Param('accountId') accountId: string,
    @Request() req: any,
  ): Promise<any> {
    // Verify account belongs to user
    await this.emailService.getEmailAccount(req.user.id, accountId);
    
    return await this.emailSyncService.getDetailedSyncStatus(accountId);
  }
}