import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { EmailThreadDto, EmailMessageDto, EmailParticipantDto, EmailAttachmentDto } from '../dto/email-sync.dto';
import { EmailAccount } from '../entities/email-account.entity';
import { EmailProvider, OAuth2Tokens, EmailProfile, ListOptions, ListResult, SendMessageData } from './email-provider.interface';

export interface GmailOAuth2Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

export interface GmailProfile {
  emailAddress: string;
  messagesTotal: number;
  threadsTotal: number;
  historyId: string;
}

@Injectable()
export class GmailProvider extends EmailProvider {
  private readonly logger = new Logger(GmailProvider.name);
  private readonly gmailApi: AxiosInstance;
  private readonly oauth2Api: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    super();
    this.gmailApi = axios.create({
      baseURL: 'https://gmail.googleapis.com/gmail/v1',
      timeout: 30000,
    });

    this.oauth2Api = axios.create({
      baseURL: 'https://oauth2.googleapis.com',
      timeout: 10000,
    });

    // Add request interceptor for logging
    this.gmailApi.interceptors.request.use((config) => {
      this.logger.debug(`Gmail API Request: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    });

    // Add response interceptor for error handling
    this.gmailApi.interceptors.response.use(
      (response) => response,
      (error) => {
        this.logger.error(
          `Gmail API Error: ${error.response?.status} ${error.response?.statusText}`,
          error.response?.data,
        );
        throw error;
      },
    );
  }

  /**
   * Get OAuth2 authorization URL
   */
  getAuthUrl(state?: string): string {
    const clientId = this.configService.get<string>('GMAIL_CLIENT_ID');
    const redirectUri = this.configService.get<string>('GMAIL_REDIRECT_URI');
    
    if (!clientId || !redirectUri) {
      throw new BadRequestException('Gmail OAuth2 configuration missing');
    }

    const scopes = [
      'https://www.googleapis.com/auth/gmail.readonly',
      'https://www.googleapis.com/auth/gmail.send',
      'https://www.googleapis.com/auth/gmail.modify',
    ];

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: scopes.join(' '),
      access_type: 'offline',
      prompt: 'consent',
      ...(state && { state }),
    });

    return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string): Promise<OAuth2Tokens> {
    const clientId = this.configService.get<string>('GMAIL_CLIENT_ID');
    const clientSecret = this.configService.get<string>('GMAIL_CLIENT_SECRET');
    const redirectUri = this.configService.get<string>('GMAIL_REDIRECT_URI');

    if (!clientId || !clientSecret || !redirectUri) {
      throw new BadRequestException('Gmail OAuth2 configuration missing');
    }

    try {
      const response = await this.oauth2Api.post('/token', {
        client_id: clientId,
        client_secret: clientSecret,
        code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to exchange code for tokens', error.response?.data);
      throw new BadRequestException('Failed to authenticate with Gmail');
    }
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(refreshToken: string): Promise<OAuth2Tokens> {
    const clientId = this.configService.get<string>('GMAIL_CLIENT_ID');
    const clientSecret = this.configService.get<string>('GMAIL_CLIENT_SECRET');

    if (!clientId || !clientSecret) {
      throw new BadRequestException('Gmail OAuth2 configuration missing');
    }

    try {
      const response = await this.oauth2Api.post('/token', {
        client_id: clientId,
        client_secret: clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to refresh access token', error.response?.data);
      throw new BadRequestException('Failed to refresh Gmail access token');
    }
  }

  /**
   * Get user profile information
   */
  async getProfile(accessToken: string): Promise<EmailProfile> {
    try {
      const response = await this.gmailApi.get('/users/me/profile', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get Gmail profile', error.response?.data);
      throw new BadRequestException('Failed to get Gmail profile');
    }
  }

  /**
   * List email threads
   */
  async listThreads(
    accessToken: string,
    options?: ListOptions,
  ): Promise<ListResult<{ id: string; historyId?: string }>> {
    try {
      const params = new URLSearchParams();
      
      if (options?.query) params.append('q', options.query);
      if (options?.labelIds?.length) {
        options.labelIds.forEach(label => params.append('labelIds', label));
      }
      if (options?.maxResults) params.append('maxResults', options.maxResults.toString());
      if (options?.pageToken) params.append('pageToken', options.pageToken);
      if (options?.includeSpamTrash) params.append('includeSpamTrash', 'true');

      const response = await this.gmailApi.get(`/users/me/threads?${params.toString()}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      return {
        items: response.data.threads || [],
        nextPageToken: response.data.nextPageToken,
        resultSizeEstimate: response.data.resultSizeEstimate,
      };
    } catch (error) {
      this.logger.error('Failed to list Gmail threads', error.response?.data);
      throw new BadRequestException('Failed to list Gmail threads');
    }
  }

  /**
   * Get thread details
   */
  async getThread(accessToken: string, threadId: string, format: string = 'full'): Promise<any> {
    try {
      const response = await this.gmailApi.get(`/users/me/threads/${threadId}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
        params: { format },
      });

      return this.transformGmailThread(response.data);
    } catch (error) {
      this.logger.error(`Failed to get Gmail thread ${threadId}`, error.response?.data);
      throw new BadRequestException('Failed to get Gmail thread');
    }
  }

  /**
   * Get message details
   */
  async getMessage(accessToken: string, messageId: string, format: string = 'full'): Promise<any> {
    try {
      const response = await this.gmailApi.get(`/users/me/messages/${messageId}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
        params: { format },
      });

      return this.transformGmailMessage(response.data);
    } catch (error) {
      this.logger.error(`Failed to get Gmail message ${messageId}`, error.response?.data);
      throw new BadRequestException('Failed to get Gmail message');
    }
  }

  /**
   * Send email message
   */
  async sendMessage(accessToken: string, messageData: SendMessageData): Promise<any> {
    try {
      // Convert messageData to Gmail's raw format
      const rawMessage = this.buildRawMessage(messageData);
      
      const response = await this.gmailApi.post(
        '/users/me/messages/send',
        { raw: rawMessage },
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        },
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to send Gmail message', error.response?.data);
      throw new BadRequestException('Failed to send Gmail message');
    }
  }

  /**
   * Modify message labels
   */
  async modifyMessage(
    accessToken: string,
    messageId: string,
    addLabelIds?: string[],
    removeLabelIds?: string[],
  ): Promise<any> {
    try {
      const response = await this.gmailApi.post(
        `/users/me/messages/${messageId}/modify`,
        {
          addLabelIds: addLabelIds || [],
          removeLabelIds: removeLabelIds || [],
        },
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        },
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to modify Gmail message ${messageId}`, error.response?.data);
      throw new BadRequestException('Failed to modify Gmail message');
    }
  }

  /**
   * Get labels
   */
  async getLabels(accessToken: string): Promise<any[]> {
    try {
      const response = await this.gmailApi.get('/users/me/labels', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      return response.data.labels || [];
    } catch (error) {
      this.logger.error('Failed to get Gmail labels', error.response?.data);
      throw new BadRequestException('Failed to get Gmail labels');
    }
  }

  /**
   * Watch for changes (for real-time sync)
   */
  async watchMailbox(accessToken: string, topicName: string): Promise<any> {
    try {
      const response = await this.gmailApi.post(
        '/users/me/watch',
        {
          topicName,
          labelIds: ['INBOX'],
          labelFilterAction: 'include',
        },
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        },
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to watch Gmail mailbox', error.response?.data);
      throw new BadRequestException('Failed to watch Gmail mailbox');
    }
  }

  /**
   * Stop watching mailbox
   */
  async stopWatch(accessToken: string): Promise<void> {
    try {
      await this.gmailApi.post(
        '/users/me/stop',
        {},
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        },
      );
    } catch (error) {
      this.logger.error('Failed to stop watching Gmail mailbox', error.response?.data);
      throw new BadRequestException('Failed to stop watching Gmail mailbox');
    }
  }

  /**
   * Transform Gmail thread to our format
   */
  private transformGmailThread(gmailThread: any): EmailThreadDto {
    const messages = gmailThread.messages?.map((msg: any) => this.transformGmailMessage(msg)) || [];
    const lastMessage = messages[messages.length - 1];
    
    // Extract participants from all messages
    const participantMap = new Map<string, EmailParticipantDto>();
    messages.forEach((msg: EmailMessageDto) => {
      if (msg.from) participantMap.set(msg.from.email, msg.from);
      msg.to?.forEach(p => participantMap.set(p.email, p));
      msg.cc?.forEach(p => participantMap.set(p.email, p));
    });

    return {
      id: gmailThread.id,
      subject: lastMessage?.subject || '(No Subject)',
      snippet: gmailThread.snippet || '',
      labels: gmailThread.labelIds || [],
      messageCount: messages.length,
      unreadCount: messages.filter(m => m.isUnread).length,
      hasStarred: messages.some(m => m.isStarred),
      isImportant: gmailThread.labelIds?.includes('IMPORTANT') || false,
      lastMessageTime: lastMessage?.timestamp || new Date(),
      participants: Array.from(participantMap.values()),
      messages,
    };
  }

  /**
   * Transform Gmail message to our format
   */
  private transformGmailMessage(gmailMessage: any): EmailMessageDto {
    const headers = this.parseHeaders(gmailMessage.payload?.headers || []);
    const bodyData = this.extractBody(gmailMessage.payload);
    
    return {
      id: gmailMessage.id,
      threadId: gmailMessage.threadId,
      subject: headers.subject || '(No Subject)',
      snippet: gmailMessage.snippet || '',
      bodyHtml: bodyData.html,
      bodyText: bodyData.text,
      from: this.parseEmailAddress(headers.from),
      to: this.parseEmailAddresses(headers.to),
      cc: this.parseEmailAddresses(headers.cc),
      bcc: this.parseEmailAddresses(headers.bcc),
      labels: gmailMessage.labelIds || [],
      isUnread: gmailMessage.labelIds?.includes('UNREAD') || false,
      isStarred: gmailMessage.labelIds?.includes('STARRED') || false,
      isImportant: gmailMessage.labelIds?.includes('IMPORTANT') || false,
      timestamp: new Date(parseInt(gmailMessage.internalDate)),
      attachments: this.extractAttachments(gmailMessage.payload),
      internalDate: new Date(parseInt(gmailMessage.internalDate)),
      sizeEstimate: gmailMessage.sizeEstimate || 0,
    };
  }

  /**
   * Parse email headers into a simple object
   */
  private parseHeaders(headers: any[]): Record<string, string> {
    const headerMap: Record<string, string> = {};
    headers.forEach(header => {
      headerMap[header.name.toLowerCase()] = header.value;
    });
    return headerMap;
  }

  /**
   * Extract email body from Gmail payload
   */
  private extractBody(payload: any): { text?: string; html?: string } {
    if (!payload) return {};

    // Single part message
    if (payload.body?.data) {
      const data = Buffer.from(payload.body.data, 'base64').toString('utf-8');
      return payload.mimeType === 'text/html' ? { html: data } : { text: data };
    }

    // Multi-part message
    if (payload.parts) {
      const result: { text?: string; html?: string } = {};
      
      const findPart = (parts: any[], mimeType: string): string | undefined => {
        for (const part of parts) {
          if (part.mimeType === mimeType && part.body?.data) {
            return Buffer.from(part.body.data, 'base64').toString('utf-8');
          }
          if (part.parts) {
            const found = findPart(part.parts, mimeType);
            if (found) return found;
          }
        }
      };

      result.text = findPart(payload.parts, 'text/plain');
      result.html = findPart(payload.parts, 'text/html');
      
      return result;
    }

    return {};
  }

  /**
   * Extract attachments from Gmail payload
   */
  private extractAttachments(payload: any): EmailAttachmentDto[] {
    if (!payload) return [];

    const attachments: EmailAttachmentDto[] = [];

    const findAttachments = (parts: any[]) => {
      for (const part of parts) {
        if (part.filename && part.body?.attachmentId) {
          attachments.push({
            id: part.body.attachmentId,
            filename: part.filename,
            mimeType: part.mimeType,
            size: part.body.size || 0,
            isInline: part.headers?.some((h: any) => 
              h.name.toLowerCase() === 'content-disposition' && 
              h.value.includes('inline')
            ) || false,
          });
        }
        if (part.parts) {
          findAttachments(part.parts);
        }
      }
    };

    if (payload.parts) {
      findAttachments(payload.parts);
    }

    return attachments;
  }

  /**
   * Parse single email address
   */
  private parseEmailAddress(addressString?: string): EmailParticipantDto {
    if (!addressString) return { email: '' };

    const match = addressString.match(/^(.*?)\s*<(.+)>$|^(.+)$/);
    if (match) {
      if (match[2]) {
        return { name: match[1]?.trim(), email: match[2].trim() };
      } else {
        return { email: match[3].trim() };
      }
    }

    return { email: addressString };
  }

  /**
   * Parse multiple email addresses
   */
  private parseEmailAddresses(addressString?: string): EmailParticipantDto[] {
    if (!addressString) return [];
    
    return addressString.split(',').map(addr => this.parseEmailAddress(addr.trim()));
  }

  /**
   * Build raw email message for Gmail API
   */
  private buildRawMessage(messageData: SendMessageData): string {
    const { to, cc, bcc, subject, body, threadId } = messageData;
    
    let email = '';
    email += `To: ${to.join(', ')}\r\n`;
    if (cc?.length) email += `Cc: ${cc.join(', ')}\r\n`;
    if (bcc?.length) email += `Bcc: ${bcc.join(', ')}\r\n`;
    email += `Subject: ${subject}\r\n`;
    if (threadId) email += `In-Reply-To: ${threadId}\r\n`;
    email += `Content-Type: text/html; charset=utf-8\r\n`;
    email += `\r\n${body}`;
    
    return Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  }

  /**
   * List email messages (optional method for interface compatibility)
   */
  async listMessages(accessToken: string, options?: ListOptions): Promise<ListResult<any>> {
    try {
      const params = new URLSearchParams();
      
      if (options?.query) params.append('q', options.query);
      if (options?.labelIds?.length) {
        options.labelIds.forEach(label => params.append('labelIds', label));
      }
      if (options?.maxResults) params.append('maxResults', options.maxResults.toString());
      if (options?.pageToken) params.append('pageToken', options.pageToken);
      if (options?.includeSpamTrash) params.append('includeSpamTrash', 'true');

      const response = await this.gmailApi.get(`/users/me/messages?${params.toString()}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      return {
        items: response.data.messages || [],
        nextPageToken: response.data.nextPageToken,
        resultSizeEstimate: response.data.resultSizeEstimate,
      };
    } catch (error) {
      this.logger.error('Failed to list Gmail messages', error.response?.data);
      throw new BadRequestException('Failed to list Gmail messages');
    }
  }
}