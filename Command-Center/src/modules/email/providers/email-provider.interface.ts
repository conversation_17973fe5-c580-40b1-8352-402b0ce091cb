import { EmailThreadDto, EmailMessageDto } from '../dto/email-sync.dto';

export interface OAuth2Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

export interface EmailProfile {
  emailAddress: string;
  displayName?: string;
  id?: string;
  messagesTotal?: number;
  threadsTotal?: number;
  historyId?: string;
}

export interface ListOptions {
  query?: string;
  labelIds?: string[];
  filter?: string;
  search?: string;
  maxResults?: number;
  pageToken?: string;
  skipToken?: string;
  includeSpamTrash?: boolean;
  orderBy?: string;
}

export interface ListResult<T> {
  items: T[];
  nextPageToken?: string;
  nextSkipToken?: string;
  resultSizeEstimate?: number;
  totalCount?: number;
}

export interface SendMessageData {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  threadId?: string;
}

export abstract class EmailProvider {
  abstract getAuthUrl(state?: string): string;
  abstract exchangeCodeForTokens(code: string): Promise<OAuth2Tokens>;
  abstract refreshAccessToken(refreshToken: string): Promise<OAuth2Tokens>;
  abstract getProfile(accessToken: string): Promise<EmailProfile>;
  
  // Thread/Conversation operations
  abstract listThreads(accessToken: string, options?: ListOptions): Promise<ListResult<{ id: string; historyId?: string }>>;
  abstract getThread(accessToken: string, threadId: string, format?: string): Promise<EmailThreadDto>;
  
  // Message operations
  abstract listMessages?(accessToken: string, options?: ListOptions): Promise<ListResult<any>>;
  abstract getMessage(accessToken: string, messageId: string, format?: string): Promise<EmailMessageDto>;
  abstract sendMessage(accessToken: string, messageData: SendMessageData): Promise<any>;
  
  // Label/Folder operations
  abstract getLabels?(accessToken: string): Promise<any[]>;
  abstract getFolders?(accessToken: string): Promise<any[]>;
  
  // Utility operations
  abstract modifyMessage?(accessToken: string, messageId: string, addLabelIds?: string[], removeLabelIds?: string[]): Promise<any>;
  abstract watchMailbox?(accessToken: string, topicName: string): Promise<any>;
  abstract stopWatch?(accessToken: string): Promise<void>;
}