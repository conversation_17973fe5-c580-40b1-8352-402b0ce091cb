import { Injectable, BadRequestException } from '@nestjs/common';
import { EmailProvider } from './email-provider.interface';
import { GmailProvider } from './gmail.provider';
import { OutlookProvider } from './outlook.provider';
import { EmailProvider as EmailProviderEnum } from '../entities/email-account.entity';

@Injectable()
export class EmailProviderFactory {
  constructor(
    private readonly gmailProvider: GmailProvider,
    private readonly outlookProvider: OutlookProvider,
  ) {}

  /**
   * Get provider instance based on provider type
   */
  getProvider(provider: EmailProviderEnum): EmailProvider {
    switch (provider) {
      case EmailProviderEnum.GMAIL:
        return this.gmailProvider as any; // Cast to match interface
      case EmailProviderEnum.OUTLOOK:
        return this.outlookProvider as any; // Cast to match interface
      case EmailProviderEnum.YAHOO:
        throw new BadRequestException('Yahoo Mail provider not implemented yet');
      case EmailProviderEnum.IMAP:
        throw new BadRequestException('IMAP provider not implemented yet');
      default:
        throw new BadRequestException(`Unsupported email provider: ${provider}`);
    }
  }

  /**
   * Get all supported providers
   */
  getSupportedProviders(): EmailProviderEnum[] {
    return [
      EmailProviderEnum.GMAIL,
      EmailProviderEnum.OUTLOOK,
      // EmailProviderEnum.YAHOO, // Not implemented yet
      // EmailProviderEnum.IMAP,  // Not implemented yet
    ];
  }

  /**
   * Check if provider is supported
   */
  isProviderSupported(provider: EmailProviderEnum): boolean {
    return this.getSupportedProviders().includes(provider);
  }

  /**
   * Get provider capabilities
   */
  getProviderCapabilities(provider: EmailProviderEnum): {
    supportsThreads: boolean;
    supportsLabels: boolean;
    supportsFolders: boolean;
    supportsWatch: boolean;
    supportsModifyLabels: boolean;
    supportsBatchOperations: boolean;
    supportsHistorySync: boolean;
  } {
    switch (provider) {
      case EmailProviderEnum.GMAIL:
        return {
          supportsThreads: true,
          supportsLabels: true,
          supportsFolders: false,
          supportsWatch: true,
          supportsModifyLabels: true,
          supportsBatchOperations: true,
          supportsHistorySync: true,
        };
      case EmailProviderEnum.OUTLOOK:
        return {
          supportsThreads: true, // Conversations
          supportsLabels: false,
          supportsFolders: true,
          supportsWatch: false, // Would need webhooks
          supportsModifyLabels: false,
          supportsBatchOperations: false,
          supportsHistorySync: false,
        };
      case EmailProviderEnum.YAHOO:
      case EmailProviderEnum.IMAP:
        return {
          supportsThreads: false,
          supportsLabels: false,
          supportsFolders: true,
          supportsWatch: false,
          supportsModifyLabels: false,
          supportsBatchOperations: false,
          supportsHistorySync: false,
        };
      default:
        throw new BadRequestException(`Unknown provider: ${provider}`);
    }
  }

  /**
   * Get provider-specific configuration requirements
   */
  getProviderConfig(provider: EmailProviderEnum): {
    requiredEnvVars: string[];
    oauthScopes: string[];
    redirectUriEnvVar: string;
  } {
    switch (provider) {
      case EmailProviderEnum.GMAIL:
        return {
          requiredEnvVars: ['GMAIL_CLIENT_ID', 'GMAIL_CLIENT_SECRET'],
          oauthScopes: [
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.send',
            'https://www.googleapis.com/auth/gmail.modify',
          ],
          redirectUriEnvVar: 'GMAIL_REDIRECT_URI',
        };
      case EmailProviderEnum.OUTLOOK:
        return {
          requiredEnvVars: ['OUTLOOK_CLIENT_ID', 'OUTLOOK_CLIENT_SECRET'],
          oauthScopes: [
            'https://graph.microsoft.com/Mail.Read',
            'https://graph.microsoft.com/Mail.Send',
            'https://graph.microsoft.com/Mail.ReadWrite',
            'https://graph.microsoft.com/User.Read',
            'offline_access',
          ],
          redirectUriEnvVar: 'OUTLOOK_REDIRECT_URI',
        };
      case EmailProviderEnum.YAHOO:
        return {
          requiredEnvVars: ['YAHOO_CLIENT_ID', 'YAHOO_CLIENT_SECRET'],
          oauthScopes: ['mail-r', 'mail-w'],
          redirectUriEnvVar: 'YAHOO_REDIRECT_URI',
        };
      case EmailProviderEnum.IMAP:
        return {
          requiredEnvVars: [], // IMAP doesn't use OAuth
          oauthScopes: [],
          redirectUriEnvVar: '',
        };
      default:
        throw new BadRequestException(`Unknown provider: ${provider}`);
    }
  }
}