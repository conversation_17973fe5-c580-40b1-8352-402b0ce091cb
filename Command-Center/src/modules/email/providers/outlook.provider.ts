import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { EmailThreadDto, EmailMessageDto, EmailParticipantDto, EmailAttachmentDto } from '../dto/email-sync.dto';
import { EmailAccount } from '../entities/email-account.entity';
import { EmailProvider, OAuth2Tokens, EmailProfile, ListOptions, ListResult, SendMessageData } from './email-provider.interface';

export interface OutlookOAuth2Tokens {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
  scope: string;
}

export interface OutlookProfile {
  emailAddress: string;
  displayName: string;
  id: string;
}

@Injectable()
export class OutlookProvider extends EmailProvider {
  private readonly logger = new Logger(OutlookProvider.name);
  private readonly graphApi: AxiosInstance;
  private readonly oauth2Api: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    super();
    this.graphApi = axios.create({
      baseURL: 'https://graph.microsoft.com/v1.0',
      timeout: 30000,
    });

    this.oauth2Api = axios.create({
      baseURL: 'https://login.microsoftonline.com/common/oauth2/v2.0',
      timeout: 10000,
    });

    // Add request interceptor for logging
    this.graphApi.interceptors.request.use((config) => {
      this.logger.debug(`Graph API Request: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    });

    // Add response interceptor for error handling
    this.graphApi.interceptors.response.use(
      (response) => response,
      (error) => {
        this.logger.error(
          `Graph API Error: ${error.response?.status} ${error.response?.statusText}`,
          error.response?.data,
        );
        throw error;
      },
    );
  }

  /**
   * Get OAuth2 authorization URL
   */
  getAuthUrl(state?: string): string {
    const clientId = this.configService.get<string>('OUTLOOK_CLIENT_ID');
    const redirectUri = this.configService.get<string>('OUTLOOK_REDIRECT_URI');
    
    if (!clientId || !redirectUri) {
      throw new BadRequestException('Outlook OAuth2 configuration missing');
    }

    const scopes = [
      'https://graph.microsoft.com/Mail.Read',
      'https://graph.microsoft.com/Mail.Send',
      'https://graph.microsoft.com/Mail.ReadWrite',
      'https://graph.microsoft.com/User.Read',
      'offline_access',
    ];

    const params = new URLSearchParams({
      client_id: clientId,
      response_type: 'code',
      redirect_uri: redirectUri,
      response_mode: 'query',
      scope: scopes.join(' '),
      ...(state && { state }),
    });

    return `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?${params.toString()}`;
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string): Promise<OAuth2Tokens> {
    const clientId = this.configService.get<string>('OUTLOOK_CLIENT_ID');
    const clientSecret = this.configService.get<string>('OUTLOOK_CLIENT_SECRET');
    const redirectUri = this.configService.get<string>('OUTLOOK_REDIRECT_URI');

    if (!clientId || !clientSecret || !redirectUri) {
      throw new BadRequestException('Outlook OAuth2 configuration missing');
    }

    try {
      const response = await this.oauth2Api.post('/token', new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to exchange code for tokens', error.response?.data);
      throw new BadRequestException('Failed to authenticate with Outlook');
    }
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(refreshToken: string): Promise<OAuth2Tokens> {
    const clientId = this.configService.get<string>('OUTLOOK_CLIENT_ID');
    const clientSecret = this.configService.get<string>('OUTLOOK_CLIENT_SECRET');

    if (!clientId || !clientSecret) {
      throw new BadRequestException('Outlook OAuth2 configuration missing');
    }

    try {
      const response = await this.oauth2Api.post('/token', new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to refresh access token', error.response?.data);
      throw new BadRequestException('Failed to refresh Outlook access token');
    }
  }

  /**
   * Get user profile information
   */
  async getProfile(accessToken: string): Promise<EmailProfile> {
    try {
      const response = await this.graphApi.get('/me', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      return {
        emailAddress: response.data.mail || response.data.userPrincipalName,
        displayName: response.data.displayName,
        id: response.data.id,
      };
    } catch (error) {
      this.logger.error('Failed to get Outlook profile', error.response?.data);
      throw new BadRequestException('Failed to get Outlook profile');
    }
  }

  /**
   * List email threads (conversations in Outlook)
   */
  async listThreads(accessToken: string, options?: ListOptions): Promise<ListResult<{ id: string; historyId?: string }>> {
    try {
      // In Outlook, we need to get distinct conversations
      const params = new URLSearchParams();
      params.append('$select', 'conversationId,receivedDateTime');
      if (options?.maxResults) params.append('$top', options.maxResults.toString());
      if (options?.skipToken) params.append('$skip', options.skipToken);

      const response = await this.graphApi.get(`/me/messages?${params.toString()}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      // Group by conversation ID to get unique threads
      const conversationMap = new Map();
      response.data.value?.forEach((message: any) => {
        if (!conversationMap.has(message.conversationId)) {
          conversationMap.set(message.conversationId, {
            id: message.conversationId,
            // Outlook doesn't have historyId equivalent
          });
        }
      });

      return {
        items: Array.from(conversationMap.values()),
        nextSkipToken: response.data['@odata.nextLink']?.split('$skip=')[1],
        totalCount: conversationMap.size,
      };
    } catch (error) {
      this.logger.error('Failed to list Outlook threads', error.response?.data);
      throw new BadRequestException('Failed to list Outlook threads');
    }
  }

  /**
   * Get thread (conversation) details
   */
  async getThread(accessToken: string, threadId: string, format?: string): Promise<EmailThreadDto> {
    return this.getConversation(accessToken, threadId);
  }

  /**
   * List email messages (Outlook doesn't have threads like Gmail)
   */
  async listMessages(
    accessToken: string,
    options?: ListOptions,
  ): Promise<ListResult<any>> {
    try {
      const params = new URLSearchParams();
      
      if (options?.filter) params.append('$filter', options.filter);
      if (options?.search) params.append('$search', options.search);
      if (options?.maxResults) params.append('$top', options.maxResults.toString());
      if (options?.skipToken) params.append('$skip', options.skipToken);
      if (options?.orderBy) params.append('$orderby', options.orderBy);

      const response = await this.graphApi.get(`/me/messages?${params.toString()}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      return {
        items: response.data.value || [],
        nextSkipToken: response.data['@odata.nextLink']?.split('$skip=')[1],
        totalCount: response.data['@odata.count'] || response.data.value?.length || 0,
      };
    } catch (error) {
      this.logger.error('Failed to list Outlook messages', error.response?.data);
      throw new BadRequestException('Failed to list Outlook messages');
    }
  }

  /**
   * Get specific message
   */
  async getMessage(accessToken: string, messageId: string, format?: string): Promise<EmailMessageDto> {
    try {
      const response = await this.graphApi.get(`/me/messages/${messageId}`, {
        headers: { Authorization: `Bearer ${accessToken}` },
        params: {
          $expand: 'attachments',
        },
      });

      return this.transformOutlookMessage(response.data);
    } catch (error) {
      this.logger.error(`Failed to get Outlook message ${messageId}`, error.response?.data);
      throw new BadRequestException('Failed to get Outlook message');
    }
  }

  /**
   * Send email message
   */
  async sendMessage(accessToken: string, messageData: SendMessageData): Promise<any> {
    try {
      const message = {
        subject: messageData.subject,
        body: {
          contentType: 'HTML',
          content: messageData.body,
        },
        toRecipients: messageData.to.map(email => ({
          emailAddress: { address: email },
        })),
        ...(messageData.cc && {
          ccRecipients: messageData.cc.map(email => ({
            emailAddress: { address: email },
          })),
        }),
        ...(messageData.bcc && {
          bccRecipients: messageData.bcc.map(email => ({
            emailAddress: { address: email },
          })),
        }),
      };

      const response = await this.graphApi.post(
        '/me/sendMail',
        { message },
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        },
      );

      return response.data;
    } catch (error) {
      this.logger.error('Failed to send Outlook message', error.response?.data);
      throw new BadRequestException('Failed to send Outlook message');
    }
  }

  /**
   * Get mail folders
   */
  async getFolders(accessToken: string): Promise<any[]> {
    try {
      const response = await this.graphApi.get('/me/mailFolders', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      return response.data.value || [];
    } catch (error) {
      this.logger.error('Failed to get Outlook folders', error.response?.data);
      throw new BadRequestException('Failed to get Outlook folders');
    }
  }

  /**
   * Transform Outlook message to our format
   */
  private transformOutlookMessage(outlookMessage: any): EmailMessageDto {
    return {
      id: outlookMessage.id,
      threadId: outlookMessage.conversationId,
      subject: outlookMessage.subject || '(No Subject)',
      snippet: this.extractSnippet(outlookMessage.body?.content),
      bodyHtml: outlookMessage.body?.contentType === 'html' ? outlookMessage.body.content : undefined,
      bodyText: outlookMessage.body?.contentType === 'text' ? outlookMessage.body.content : undefined,
      from: this.parseOutlookParticipant(outlookMessage.from),
      to: outlookMessage.toRecipients?.map((r: any) => this.parseOutlookParticipant(r)) || [],
      cc: outlookMessage.ccRecipients?.map((r: any) => this.parseOutlookParticipant(r)) || [],
      bcc: outlookMessage.bccRecipients?.map((r: any) => this.parseOutlookParticipant(r)) || [],
      labels: [outlookMessage.parentFolderId],
      isUnread: !outlookMessage.isRead,
      isStarred: outlookMessage.flag?.flagStatus === 'flagged',
      isImportant: outlookMessage.importance === 'high',
      timestamp: new Date(outlookMessage.receivedDateTime),
      attachments: this.extractOutlookAttachments(outlookMessage.attachments),
      internalDate: new Date(outlookMessage.createdDateTime),
      sizeEstimate: outlookMessage.bodyPreview?.length || 0,
    };
  }

  /**
   * Parse Outlook participant to our format
   */
  private parseOutlookParticipant(participant: any): EmailParticipantDto {
    if (!participant) return { email: '' };
    
    const emailAddress = participant.emailAddress || participant;
    return {
      name: emailAddress.name,
      email: emailAddress.address,
    };
  }

  /**
   * Extract attachments from Outlook message
   */
  private extractOutlookAttachments(attachments: any[]): EmailAttachmentDto[] {
    if (!attachments) return [];

    return attachments.map(attachment => ({
      id: attachment.id,
      filename: attachment.name,
      mimeType: attachment.contentType,
      size: attachment.size,
      isInline: attachment.isInline || false,
    }));
  }

  /**
   * Extract snippet from message body
   */
  private extractSnippet(content: string, maxLength = 150): string {
    if (!content) return '';
    
    // Remove HTML tags
    const textContent = content.replace(/<[^>]*>/g, '');
    
    // Trim and limit length
    return textContent.trim().substring(0, maxLength) + (textContent.length > maxLength ? '...' : '');
  }

  /**
   * Group messages by conversation (similar to Gmail threads)
   */
  async getConversation(accessToken: string, conversationId: string): Promise<EmailThreadDto> {
    try {
      const response = await this.graphApi.get(`/me/messages`, {
        headers: { Authorization: `Bearer ${accessToken}` },
        params: {
          $filter: `conversationId eq '${conversationId}'`,
          $orderby: 'receivedDateTime asc',
          $expand: 'attachments',
        },
      });

      const messages = response.data.value?.map((msg: any) => this.transformOutlookMessage(msg)) || [];
      const lastMessage = messages[messages.length - 1];
      
      // Extract participants from all messages
      const participantMap = new Map<string, EmailParticipantDto>();
      messages.forEach((msg: EmailMessageDto) => {
        if (msg.from) participantMap.set(msg.from.email, msg.from);
        msg.to?.forEach(p => participantMap.set(p.email, p));
        msg.cc?.forEach(p => participantMap.set(p.email, p));
      });

      return {
        id: conversationId,
        subject: lastMessage?.subject || '(No Subject)',
        snippet: lastMessage?.snippet || '',
        labels: lastMessage?.labels || [],
        messageCount: messages.length,
        unreadCount: messages.filter(m => m.isUnread).length,
        hasStarred: messages.some(m => m.isStarred),
        isImportant: messages.some(m => m.isImportant),
        lastMessageTime: lastMessage?.timestamp || new Date(),
        participants: Array.from(participantMap.values()),
        messages,
      };
    } catch (error) {
      this.logger.error(`Failed to get Outlook conversation ${conversationId}`, error.response?.data);
      throw new BadRequestException('Failed to get Outlook conversation');
    }
  }
}