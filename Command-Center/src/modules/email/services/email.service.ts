import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EmailAccount, EmailProvider, AccountStatus } from '../entities/email-account.entity';
import { EmailSyncLog, SyncType, SyncStatus } from '../entities/email-sync-log.entity';
import { GmailProvider } from '../providers/gmail.provider';
import { EmailProviderFactory } from '../providers/email-provider.factory';
import { EmailSyncService } from './email-sync.service';
import {
  CreateEmailAccountDto,
  UpdateEmailAccountDto,
  OAuth2CallbackDto,
  EmailAccountResponseDto,
} from '../dto/email-account.dto';
import {
  EmailThreadDto,
  EmailMessageDto,
  EmailSearchDto,
  EmailSyncResponseDto,
} from '../dto/email-sync.dto';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    @InjectRepository(EmailAccount)
    private readonly emailAccountRepository: Repository<EmailAccount>,
    @InjectRepository(EmailSyncLog)
    private readonly syncLogRepository: Repository<EmailSyncLog>,
    private readonly gmailProvider: GmailProvider,
    private readonly emailProviderFactory: EmailProviderFactory,
    private readonly emailSyncService: EmailSyncService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Get OAuth2 authorization URL for email provider
   */
  getAuthUrl(provider: EmailProvider, userId: string): string {
    const providerInstance = this.emailProviderFactory.getProvider(provider);
    return providerInstance.getAuthUrl(userId);
  }

  /**
   * Handle OAuth2 callback and create/update email account
   */
  async handleOAuth2Callback(
    userId: string,
    callbackData: OAuth2CallbackDto,
  ): Promise<EmailAccountResponseDto> {
    try {
      const providerInstance = this.emailProviderFactory.getProvider(callbackData.provider);
      const tokens = await providerInstance.exchangeCodeForTokens(callbackData.code);
      const profile = await providerInstance.getProfile(tokens.access_token);

      // Check if account already exists
      let emailAccount = await this.emailAccountRepository.findOne({
        where: {
          userId,
          provider: callbackData.provider,
          email: profile.emailAddress,
        },
      });

      if (emailAccount) {
        // Update existing account
        emailAccount.credentials = {
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          tokenExpiry: new Date(Date.now() + tokens.expires_in * 1000),
          scope: tokens.scope?.split(' ') || [],
        };
        emailAccount.status = AccountStatus.ACTIVE;
        emailAccount.lastSyncError = null;
        
        if (callbackData.provider === EmailProvider.GMAIL && (profile as any).historyId) {
          emailAccount.settings = {
            ...emailAccount.settings,
            historyId: (profile as any).historyId,
          };
        }
      } else {
        // Create new account
        emailAccount = this.emailAccountRepository.create({
          userId,
          provider: callbackData.provider,
          email: profile.emailAddress,
          status: AccountStatus.ACTIVE,
          credentials: {
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token,
            tokenExpiry: new Date(Date.now() + tokens.expires_in * 1000),
            scope: tokens.scope?.split(' ') || [],
          },
          settings: {
            syncIntervalMinutes: 15,
            maxEmailsPerSync: 100,
            syncLabels: ['INBOX'],
            excludeLabels: ['SPAM', 'TRASH'],
            enableRealTimeSync: true,
            ...(callbackData.provider === EmailProvider.GMAIL && (profile as any).historyId && {
              historyId: (profile as any).historyId,
            }),
          },
          syncStats: {
            totalEmailsSynced: 0,
            lastSyncEmailCount: 0,
            averageSyncTime: 0,
            successfulSyncs: 0,
            failedSyncs: 0,
          },
        });
      }

      emailAccount = await this.emailAccountRepository.save(emailAccount);

      // Emit event for new account
      this.eventEmitter.emit('email.account.connected', {
        userId,
        accountId: emailAccount.id,
        provider: callbackData.provider,
        email: profile.emailAddress,
      });

      this.logger.log(`Email account connected: ${profile.emailAddress} for user ${userId}`);

      return this.transformToResponseDto(emailAccount);
    } catch (error) {
      this.logger.error('Failed to handle OAuth2 callback', error);
      throw new BadRequestException('Failed to connect email account');
    }
  }

  /**
   * Get user's email accounts
   */
  async getEmailAccounts(userId: string): Promise<EmailAccountResponseDto[]> {
    const accounts = await this.emailAccountRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });

    return accounts.map(account => this.transformToResponseDto(account));
  }

  /**
   * Get specific email account
   */
  async getEmailAccount(userId: string, accountId: string): Promise<EmailAccountResponseDto> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    return this.transformToResponseDto(account);
  }

  /**
   * Update email account
   */
  async updateEmailAccount(
    userId: string,
    accountId: string,
    updateData: UpdateEmailAccountDto,
  ): Promise<EmailAccountResponseDto> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    // Update account properties
    if (updateData.displayName !== undefined) {
      account.displayName = updateData.displayName;
    }
    if (updateData.status !== undefined) {
      account.status = updateData.status;
    }
    if (updateData.isActive !== undefined) {
      account.isActive = updateData.isActive;
    }
    if (updateData.syncEnabled !== undefined) {
      account.syncEnabled = updateData.syncEnabled;
    }
    if (updateData.settings) {
      account.settings = { ...account.settings, ...updateData.settings };
    }

    const updatedAccount = await this.emailAccountRepository.save(account);
    
    this.logger.log(`Email account updated: ${accountId} for user ${userId}`);

    return this.transformToResponseDto(updatedAccount);
  }

  /**
   * Delete email account
   */
  async deleteEmailAccount(userId: string, accountId: string): Promise<void> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    // Stop watching if real-time sync is enabled
    if (account.settings?.enableRealTimeSync && account.credentials?.accessToken) {
      try {
        switch (account.provider) {
          case EmailProvider.GMAIL:
            await this.gmailProvider.stopWatch(account.credentials.accessToken);
            break;
        }
      } catch (error) {
        this.logger.warn(`Failed to stop watching for account ${accountId}`, error);
      }
    }

    await this.emailAccountRepository.remove(account);

    // Emit event for account deletion
    this.eventEmitter.emit('email.account.disconnected', {
      userId,
      accountId,
      provider: account.provider,
      email: account.email,
    });

    this.logger.log(`Email account deleted: ${accountId} for user ${userId}`);
  }

  /**
   * Sync emails for a specific account
   */
  async syncAccount(
    userId: string,
    accountId: string,
    fullSync: boolean = false,
  ): Promise<EmailSyncResponseDto> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    if (!account.canSync()) {
      throw new BadRequestException('Account cannot be synced at this time');
    }

    // Start sync operation
    const syncLog = await this.emailSyncService.startSync(
      account,
      fullSync ? SyncType.FULL : SyncType.INCREMENTAL,
      'manual',
    );

    return {
      syncId: syncLog.id,
      status: syncLog.status,
      message: 'Sync started successfully',
      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes estimate
    };
  }

  /**
   * Get threads for a specific account
   */
  async getThreads(
    userId: string,
    accountId: string,
    searchOptions: EmailSearchDto = {},
  ): Promise<{ threads: EmailThreadDto[]; nextPageToken?: string }> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    if (!account.credentials?.accessToken) {
      throw new UnauthorizedException('Account not authenticated');
    }

    // Ensure token is valid
    await this.ensureValidToken(account);

    try {
      switch (account.provider) {
        case EmailProvider.GMAIL:
          const result = await this.gmailProvider.listThreads(
            account.credentials.accessToken,
            {
              query: searchOptions.q,
              labelIds: searchOptions.labels,
              maxResults: searchOptions.maxResults || 20,
              pageToken: searchOptions.pageToken,
              includeSpamTrash: searchOptions.includeSpamTrash,
            },
          );

          // Get full thread details
          const threads = await Promise.all(
            result.threads.map(thread =>
              this.gmailProvider.getThread(account.credentials!.accessToken, thread.id),
            ),
          );

          return {
            threads,
            nextPageToken: result.nextPageToken,
          };

        default:
          throw new BadRequestException(`Provider ${account.provider} not supported`);
      }
    } catch (error) {
      this.logger.error(`Failed to get threads for account ${accountId}`, error);
      throw new BadRequestException('Failed to retrieve emails');
    }
  }

  /**
   * Get specific thread
   */
  async getThread(
    userId: string,
    accountId: string,
    threadId: string,
  ): Promise<EmailThreadDto> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    if (!account.credentials?.accessToken) {
      throw new UnauthorizedException('Account not authenticated');
    }

    await this.ensureValidToken(account);

    try {
      switch (account.provider) {
        case EmailProvider.GMAIL:
          return await this.gmailProvider.getThread(
            account.credentials.accessToken,
            threadId,
          );

        default:
          throw new BadRequestException(`Provider ${account.provider} not supported`);
      }
    } catch (error) {
      this.logger.error(`Failed to get thread ${threadId} for account ${accountId}`, error);
      throw new BadRequestException('Failed to retrieve thread');
    }
  }

  /**
   * Send email message
   */
  async sendMessage(
    userId: string,
    accountId: string,
    messageData: {
      to: string[];
      cc?: string[];
      bcc?: string[];
      subject: string;
      body: string;
      threadId?: string;
    },
  ): Promise<EmailMessageDto> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    if (!account.credentials?.accessToken) {
      throw new UnauthorizedException('Account not authenticated');
    }

    await this.ensureValidToken(account);

    // Create raw email message
    const rawMessage = this.createRawMessage(account.email, messageData);

    try {
      switch (account.provider) {
        case EmailProvider.GMAIL:
          const result = await this.gmailProvider.sendMessage(
            account.credentials.accessToken,
            rawMessage,
          );
          
          // Get the sent message details
          return await this.gmailProvider.getMessage(
            account.credentials.accessToken,
            result.id,
          );

        default:
          throw new BadRequestException(`Provider ${account.provider} not supported`);
      }
    } catch (error) {
      this.logger.error(`Failed to send message for account ${accountId}`, error);
      throw new BadRequestException('Failed to send message');
    }
  }

  /**
   * Get sync logs for an account
   */
  async getSyncLogs(
    userId: string,
    accountId: string,
    limit: number = 20,
  ): Promise<EmailSyncLog[]> {
    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId, userId },
    });

    if (!account) {
      throw new NotFoundException('Email account not found');
    }

    return await this.syncLogRepository.find({
      where: { emailAccountId: accountId },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * Ensure access token is valid, refresh if needed
   */
  private async ensureValidToken(account: EmailAccount): Promise<void> {
    if (!account.isTokenExpired()) {
      return;
    }

    if (!account.credentials?.refreshToken) {
      account.status = AccountStatus.REAUTH_REQUIRED;
      await this.emailAccountRepository.save(account);
      throw new UnauthorizedException('Account requires re-authentication');
    }

    try {
      switch (account.provider) {
        case EmailProvider.GMAIL:
          const tokens = await this.gmailProvider.refreshAccessToken(
            account.credentials.refreshToken,
          );
          
          account.credentials = {
            ...account.credentials,
            accessToken: tokens.access_token,
            tokenExpiry: new Date(Date.now() + tokens.expires_in * 1000),
          };
          
          account.status = AccountStatus.ACTIVE;
          await this.emailAccountRepository.save(account);
          break;

        default:
          throw new Error(`Provider ${account.provider} not supported`);
      }
    } catch (error) {
      this.logger.error(`Failed to refresh token for account ${account.id}`, error);
      account.status = AccountStatus.REAUTH_REQUIRED;
      await this.emailAccountRepository.save(account);
      throw new UnauthorizedException('Failed to refresh access token');
    }
  }

  /**
   * Create raw email message for sending
   */
  private createRawMessage(
    fromEmail: string,
    messageData: {
      to: string[];
      cc?: string[];
      bcc?: string[];
      subject: string;
      body: string;
      threadId?: string;
    },
  ): string {
    const lines = [
      `From: ${fromEmail}`,
      `To: ${messageData.to.join(', ')}`,
    ];

    if (messageData.cc?.length) {
      lines.push(`Cc: ${messageData.cc.join(', ')}`);
    }

    if (messageData.bcc?.length) {
      lines.push(`Bcc: ${messageData.bcc.join(', ')}`);
    }

    lines.push(`Subject: ${messageData.subject}`);
    lines.push('Content-Type: text/html; charset=UTF-8');
    lines.push('');
    lines.push(messageData.body);

    const message = lines.join('\n');
    return Buffer.from(message).toString('base64url');
  }

  /**
   * Transform entity to response DTO
   */
  private transformToResponseDto(account: EmailAccount): EmailAccountResponseDto {
    return {
      id: account.id,
      userId: account.userId,
      provider: account.provider,
      email: account.email,
      displayName: account.displayName,
      status: account.status,
      isActive: account.isActive,
      syncEnabled: account.syncEnabled,
      lastSyncAt: account.lastSyncAt,
      lastSuccessfulSyncAt: account.lastSuccessfulSyncAt,
      lastSyncError: account.lastSyncError,
      syncStats: account.syncStats,
      settings: account.settings || {},
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
    };
  }
}