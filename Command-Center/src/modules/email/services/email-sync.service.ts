import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EmailAccount, EmailProvider } from '../entities/email-account.entity';
import { EmailSyncLog, SyncType, SyncStatus } from '../entities/email-sync-log.entity';
import { GmailProvider } from '../providers/gmail.provider';
import { EmailProviderFactory } from '../providers/email-provider.factory';

export interface SyncJobData {
  syncLogId: string;
  accountId: string;
  syncType: SyncType;
  triggerReason: string;
  options?: {
    fullSync?: boolean;
    labels?: string[];
    maxResults?: number;
  };
}

@Injectable()
export class EmailSyncService {
  private readonly logger = new Logger(EmailSyncService.name);

  constructor(
    @InjectRepository(EmailAccount)
    private readonly emailAccountRepository: Repository<EmailAccount>,
    @InjectRepository(EmailSyncLog)
    private readonly syncLogRepository: Repository<EmailSyncLog>,
    @InjectQueue('email-sync')
    private readonly syncQueue: Queue,
    private readonly gmailProvider: GmailProvider,
    private readonly emailProviderFactory: EmailProviderFactory,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Start sync operation for an email account
   */
  async startSync(
    account: EmailAccount,
    syncType: SyncType,
    triggerReason: string,
    options?: SyncJobData['options'],
  ): Promise<EmailSyncLog> {
    this.logger.log(`Starting ${syncType} sync for account ${account.id} (${account.email})`);

    // Create sync log entry
    const syncLog = this.syncLogRepository.create({
      emailAccountId: account.id,
      syncType,
      status: SyncStatus.PENDING,
      metadata: {
        triggerReason,
        ...(options && { options }),
      },
    });

    await this.syncLogRepository.save(syncLog);

    // Emit sync started event
    this.eventEmitter.emit('email.sync.started', {
      accountId: account.id,
      syncLogId: syncLog.id,
      syncType,
    });

    // Add job to queue
    const jobData: SyncJobData = {
      syncLogId: syncLog.id,
      accountId: account.id,
      syncType,
      triggerReason,
      options,
    };

    await this.syncQueue.add('sync-account', jobData, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 50,
      removeOnFail: 20,
    });

    this.logger.log(`Sync job queued for account ${account.id} with sync log ${syncLog.id}`);

    return syncLog;
  }

  /**
   * Process sync job (called by Bull queue processor)
   */
  async processSyncJob(jobData: SyncJobData): Promise<void> {
    const { syncLogId, accountId, syncType } = jobData;
    
    this.logger.log(`Processing sync job for account ${accountId}, sync log ${syncLogId}`);

    let syncLog = await this.syncLogRepository.findOne({
      where: { id: syncLogId },
    });

    if (!syncLog) {
      this.logger.error(`Sync log ${syncLogId} not found`);
      return;
    }

    const account = await this.emailAccountRepository.findOne({
      where: { id: accountId },
    });

    if (!account) {
      this.logger.error(`Email account ${accountId} not found`);
      await this.markSyncFailed(syncLog, 'Account not found');
      return;
    }

    if (!account.canSync()) {
      this.logger.warn(`Account ${accountId} cannot be synced`);
      await this.markSyncFailed(syncLog, 'Account cannot be synced');
      return;
    }

    try {
      // Update sync log status
      syncLog.status = SyncStatus.IN_PROGRESS;
      syncLog.startedAt = new Date();
      await this.syncLogRepository.save(syncLog);

      const startTime = Date.now();

      // Ensure token is valid
      await this.ensureValidToken(account);

      // Perform sync based on provider
      const provider = this.emailProviderFactory.getProvider(account.provider);
      const syncResult = await this.syncAccount(provider, account, syncType, jobData.options);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update sync log with results
      syncLog.status = SyncStatus.COMPLETED;
      syncLog.completedAt = new Date();
      syncLog.emailsProcessed = syncResult.processed;
      syncLog.emailsCreated = syncResult.created;
      syncLog.emailsUpdated = syncResult.updated;
      syncLog.emailsSkipped = syncResult.skipped;
      syncLog.performance = {
        totalDurationMs: duration,
        apiCallCount: syncResult.apiCalls,
        averageApiResponseTime: syncResult.avgResponseTime,
      };

      await this.syncLogRepository.save(syncLog);

      // Update account stats
      await this.updateAccountStats(account, syncLog, true);

      // Emit sync completed event
      this.eventEmitter.emit('email.sync.completed', {
        accountId: account.id,
        syncLogId: syncLog.id,
        syncType,
        emailsProcessed: syncResult.processed,
        duration,
      });

      this.logger.log(
        `Sync completed for account ${accountId}: ${syncResult.processed} processed, ` +
        `${syncResult.created} created, ${syncResult.updated} updated in ${duration}ms`,
      );

    } catch (error) {
      this.logger.error(`Sync failed for account ${accountId}`, error);
      
      // Use enhanced error handling
      await this.handleSyncError(syncLog, account, error);
      await this.updateAccountStats(account, syncLog, false);
    }
  }

  /**
   * Sync Gmail account
   */
  private async syncGmailAccount(
    account: EmailAccount,
    syncType: SyncType,
    options?: SyncJobData['options'],
  ): Promise<{
    processed: number;
    created: number;
    updated: number;
    skipped: number;
    apiCalls: number;
    avgResponseTime: number;
  }> {
    const accessToken = account.credentials!.accessToken;
    let processed = 0;
    let created = 0;
    let updated = 0;
    let skipped = 0;
    let apiCalls = 0;
    let totalResponseTime = 0;

    const measureApiCall = async <T>(apiCall: Promise<T>): Promise<T> => {
      const start = Date.now();
      const result = await apiCall;
      const duration = Date.now() - start;
      apiCalls++;
      totalResponseTime += duration;
      return result;
    };

    try {
      // Get sync configuration
      const syncLabels = options?.labels || account.settings?.syncLabels || ['INBOX'];
      const maxResults = options?.maxResults || account.settings?.maxEmailsPerSync || 100;
      
      let query = '';
      if (syncType === SyncType.INCREMENTAL && account.lastSuccessfulSyncAt) {
        // For incremental sync, only get emails newer than last sync
        const lastSyncDate = Math.floor(account.lastSuccessfulSyncAt.getTime() / 1000);
        query = `after:${lastSyncDate}`;
      }

      // List threads
      const threadsResult = await measureApiCall(
        this.gmailProvider.listThreads(accessToken, {
          query,
          labelIds: syncLabels,
          maxResults,
          includeSpamTrash: false,
        }),
      );

      this.logger.log(
        `Found ${threadsResult.threads?.length || 0} threads to sync for account ${account.id}`,
      );

      // Process each thread
      for (const threadRef of threadsResult.threads || []) {
        try {
          const thread = await measureApiCall(
            this.gmailProvider.getThread(accessToken, threadRef.id),
          );

          // Here you would normally save the thread and messages to your database
          // For now, we'll just count them
          processed++;
          
          // Simulate database operations
          const isNew = !account.lastSuccessfulSyncAt; // Simple logic for demo
          if (isNew) {
            created++;
          } else {
            updated++;
          }

          // Process messages in thread
          for (const message of thread.messages || []) {
            processed++;
            if (isNew) {
              created++;
            } else {
              updated++;
            }
          }

        } catch (error) {
          this.logger.warn(`Failed to process thread ${threadRef.id}`, error);
          skipped++;
        }
      }

      // Update history ID for incremental sync
      if (account.provider === EmailProvider.GMAIL) {
        const profile = await measureApiCall(
          this.gmailProvider.getProfile(accessToken),
        );
        
        account.settings = {
          ...account.settings,
          historyId: profile.historyId,
        };
        await this.emailAccountRepository.save(account);
      }

      return {
        processed,
        created,
        updated,
        skipped,
        apiCalls,
        avgResponseTime: apiCalls > 0 ? totalResponseTime / apiCalls : 0,
      };

    } catch (error) {
      this.logger.error(`Gmail sync failed for account ${account.id}`, error);
      throw error;
    }
  }

  /**
   * Ensure access token is valid
   */
  private async ensureValidToken(account: EmailAccount): Promise<void> {
    if (!account.isTokenExpired()) {
      return;
    }

    if (!account.credentials?.refreshToken) {
      throw new Error('Refresh token not available');
    }

    try {
      switch (account.provider) {
        case EmailProvider.GMAIL:
          const tokens = await this.gmailProvider.refreshAccessToken(
            account.credentials.refreshToken,
          );
          
          account.credentials = {
            ...account.credentials,
            accessToken: tokens.access_token,
            tokenExpiry: new Date(Date.now() + tokens.expires_in * 1000),
          };
          
          await this.emailAccountRepository.save(account);
          break;

        default:
          throw new Error(`Provider ${account.provider} not supported`);
      }
    } catch (error) {
      this.logger.error(`Failed to refresh token for account ${account.id}`, error);
      throw new Error('Failed to refresh access token');
    }
  }

  /**
   * Mark sync as failed
   */
  private async markSyncFailed(syncLog: EmailSyncLog, errorMessage: string): Promise<void> {
    syncLog.status = SyncStatus.FAILED;
    syncLog.completedAt = new Date();
    syncLog.errorMessage = errorMessage;
    await this.syncLogRepository.save(syncLog);
  }

  /**
   * Update account statistics
   */
  private async updateAccountStats(
    account: EmailAccount,
    syncLog: EmailSyncLog,
    success: boolean,
  ): Promise<void> {
    const currentStats = account.syncStats || {
      totalEmailsSynced: 0,
      lastSyncEmailCount: 0,
      averageSyncTime: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
    };

    if (success) {
      currentStats.successfulSyncs++;
      currentStats.totalEmailsSynced += syncLog.emailsProcessed;
      currentStats.lastSyncEmailCount = syncLog.emailsProcessed;
      
      account.lastSyncAt = new Date();
      account.lastSuccessfulSyncAt = new Date();
      account.lastSyncError = null;

      // Update average sync time
      const duration = syncLog.getDuration();
      if (duration) {
        currentStats.averageSyncTime = 
          (currentStats.averageSyncTime * (currentStats.successfulSyncs - 1) + duration) / 
          currentStats.successfulSyncs;
      }
    } else {
      currentStats.failedSyncs++;
      account.lastSyncAt = new Date();
      account.lastSyncError = syncLog.errorMessage || 'Unknown error';
    }

    account.syncStats = currentStats;
    await this.emailAccountRepository.save(account);
  }

  /**
   * Schedule periodic sync for all active accounts
   */
  async schedulePeriodicSync(): Promise<void> {
    const accounts = await this.emailAccountRepository.find({
      where: {
        isActive: true,
        syncEnabled: true,
      },
    });

    this.logger.log(`Scheduling periodic sync for ${accounts.length} accounts`);

    for (const account of accounts) {
      if (account.canSync()) {
        try {
          await this.startSync(account, SyncType.INCREMENTAL, 'scheduled');
        } catch (error) {
          this.logger.error(`Failed to schedule sync for account ${account.id}`, error);
        }
      }
    }
  }

  /**
   * Get sync status for an account
   */
  async getSyncStatus(accountId: string): Promise<EmailSyncLog | null> {
    return await this.syncLogRepository.findOne({
      where: {
        emailAccountId: accountId,
        status: SyncStatus.IN_PROGRESS,
      },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Cancel ongoing sync
   */
  async cancelSync(syncLogId: string): Promise<void> {
    const syncLog = await this.syncLogRepository.findOne({
      where: { id: syncLogId },
    });

    if (syncLog && syncLog.status === SyncStatus.IN_PROGRESS) {
      syncLog.status = SyncStatus.CANCELLED;
      syncLog.completedAt = new Date();
      await this.syncLogRepository.save(syncLog);

      // Try to remove job from queue if it's still pending
      const jobs = await this.syncQueue.getJobs(['waiting', 'active', 'delayed']);
      const job = jobs.find(j => j.data.syncLogId === syncLogId);
      if (job) {
        await job.remove();
      }

      this.logger.log(`Sync cancelled for sync log ${syncLogId}`);
    }
  }

  /**
   * Enhanced error handling with categorization and recovery strategies
   */
  private async handleSyncError(
    syncLog: EmailSyncLog,
    account: EmailAccount,
    error: any,
  ): Promise<void> {
    let errorCategory = 'unknown';
    let errorMessage = error.message || 'Unknown error';
    let retryable = false;
    let retryDelay = 0;

    // Categorize errors for better handling
    if (error.response?.status === 401) {
      errorCategory = 'authentication';
      errorMessage = 'Authentication failed - token may be expired';
      retryable = true;
      retryDelay = 300000; // 5 minutes
      
      // Mark account for token refresh
      account.credentials = {
        ...account.credentials,
        needsRefresh: true,
      };
      await this.emailAccountRepository.save(account);
      
    } else if (error.response?.status === 403) {
      errorCategory = 'authorization';
      errorMessage = 'Access forbidden - insufficient permissions';
      retryable = false;
      
    } else if (error.response?.status === 429) {
      errorCategory = 'rate_limit';
      errorMessage = 'Rate limit exceeded';
      retryable = true;
      retryDelay = parseInt(error.response.headers?.['retry-after'] || '3600') * 1000;
      
    } else if (error.response?.status >= 500) {
      errorCategory = 'server_error';
      errorMessage = 'Provider server error';
      retryable = true;
      retryDelay = Math.min(60000 * Math.pow(2, (syncLog.attemptCount || 0)), 300000); // Exponential backoff, max 5 minutes
      
    } else if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      errorCategory = 'timeout';
      errorMessage = 'Request timeout';
      retryable = true;
      retryDelay = 30000; // 30 seconds
      
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorCategory = 'network';
      errorMessage = 'Network error';
      retryable = true;
      retryDelay = 60000; // 1 minute
    }

    // Update sync log with error details
    syncLog.status = retryable ? SyncStatus.FAILED_RETRYABLE : SyncStatus.FAILED;
    syncLog.completedAt = new Date();
    syncLog.errorDetails = {
      category: errorCategory,
      message: errorMessage,
      originalMessage: error.message,
      stack: error.stack,
      statusCode: error.response?.status,
      timestamp: new Date(),
      retryable,
      retryDelay,
    };
    syncLog.attemptCount = (syncLog.attemptCount || 0) + 1;

    await this.syncLogRepository.save(syncLog);

    // Update account error tracking
    account.lastSyncError = errorMessage;
    account.consecutiveFailures = (account.consecutiveFailures || 0) + 1;
    
    // If too many consecutive failures, disable sync temporarily
    if (account.consecutiveFailures >= 5) {
      account.syncEnabled = false;
      account.disabledReason = `Sync disabled after ${account.consecutiveFailures} consecutive failures`;
      this.logger.warn(`Disabling sync for account ${account.id} after ${account.consecutiveFailures} failures`);
    }

    await this.emailAccountRepository.save(account);

    // Schedule retry if error is retryable and we haven't exceeded max attempts
    if (retryable && syncLog.attemptCount < 3) {
      this.logger.log(
        `Scheduling retry for sync ${syncLog.id} in ${retryDelay}ms (attempt ${syncLog.attemptCount}/3)`,
      );
      
      await this.syncQueue.add(
        'sync-account',
        {
          syncLogId: syncLog.id,
          accountId: account.id,
          syncType: syncLog.syncType,
          triggerReason: 'retry',
        },
        {
          delay: retryDelay,
          attempts: 1, // Don't let Bull handle retries, we manage them
        },
      );
    }

    // Emit detailed error event
    this.eventEmitter.emit('email.sync.error', {
      accountId: account.id,
      syncLogId: syncLog.id,
      errorCategory,
      errorMessage,
      retryable,
      attemptCount: syncLog.attemptCount,
    });
  }

  /**
   * Get comprehensive sync status with error details and performance metrics
   */
  async getDetailedSyncStatus(accountId: string): Promise<{
    currentSync?: {
      id: string;
      status: SyncStatus;
      startedAt: Date;
      progress?: {
        processed: number;
        estimated: number;
        percentage: number;
      };
    };
    recentSyncs: Array<{
      id: string;
      syncType: SyncType;
      status: SyncStatus;
      startedAt: Date;
      completedAt?: Date;
      emailsProcessed?: number;
      duration?: number;
      errorDetails?: any;
    }>;
    stats: {
      totalSyncs: number;
      successfulSyncs: number;
      failedSyncs: number;
      averageDuration: number;
      successRate: number;
      lastSuccessfulSync?: Date;
    };
    health: {
      status: 'healthy' | 'warning' | 'error';
      issues: string[];
      recommendations: string[];
    };
  }> {
    // Get current sync
    const currentSync = await this.syncLogRepository.findOne({
      where: {
        emailAccountId: accountId,
        status: SyncStatus.IN_PROGRESS,
      },
      order: { createdAt: 'DESC' },
    });

    // Get recent syncs (last 10)
    const recentSyncs = await this.syncLogRepository.find({
      where: { emailAccountId: accountId },
      order: { createdAt: 'DESC' },
      take: 10,
    });

    // Calculate stats
    const totalSyncs = recentSyncs.length;
    const successfulSyncs = recentSyncs.filter(s => s.status === SyncStatus.COMPLETED).length;
    const failedSyncs = recentSyncs.filter(s => 
      s.status === SyncStatus.FAILED || s.status === SyncStatus.FAILED_RETRYABLE
    ).length;
    
    const completedSyncs = recentSyncs.filter(s => 
      s.status === SyncStatus.COMPLETED && s.performance?.totalDurationMs
    );
    const averageDuration = completedSyncs.length > 0
      ? completedSyncs.reduce((sum, s) => sum + s.performance!.totalDurationMs, 0) / completedSyncs.length
      : 0;
    
    const successRate = totalSyncs > 0 ? (successfulSyncs / totalSyncs) * 100 : 0;
    
    const lastSuccessfulSync = recentSyncs
      .find(s => s.status === SyncStatus.COMPLETED)?.completedAt;

    // Health assessment
    let health = 'healthy';
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (successRate < 80) {
      health = 'error';
      issues.push(`Low success rate: ${successRate.toFixed(1)}%`);
      recommendations.push('Check account credentials and permissions');
    } else if (successRate < 95) {
      health = 'warning';
      issues.push(`Success rate could be improved: ${successRate.toFixed(1)}%`);
    }

    if (lastSuccessfulSync && Date.now() - lastSuccessfulSync.getTime() > 24 * 60 * 60 * 1000) {
      health = 'error';
      issues.push('No successful sync in the last 24 hours');
      recommendations.push('Check account status and manually trigger sync');
    }

    const recentErrors = recentSyncs
      .filter(s => s.errorDetails)
      .slice(0, 3);
    
    if (recentErrors.length > 0) {
      const errorCategories = [...new Set(recentErrors.map(s => s.errorDetails.category))];
      if (errorCategories.includes('authentication')) {
        issues.push('Authentication errors detected');
        recommendations.push('Reconnect the email account');
      }
      if (errorCategories.includes('rate_limit')) {
        issues.push('Rate limiting detected');
        recommendations.push('Reduce sync frequency or contact support');
      }
    }

    return {
      ...(currentSync && {
        currentSync: {
          id: currentSync.id,
          status: currentSync.status,
          startedAt: currentSync.startedAt!,
          // Progress calculation would need additional implementation
        },
      }),
      recentSyncs: recentSyncs.map(sync => ({
        id: sync.id,
        syncType: sync.syncType,
        status: sync.status,
        startedAt: sync.startedAt || sync.createdAt,
        completedAt: sync.completedAt,
        emailsProcessed: sync.emailsProcessed,
        duration: sync.performance?.totalDurationMs,
        errorDetails: sync.errorDetails,
      })),
      stats: {
        totalSyncs,
        successfulSyncs,
        failedSyncs,
        averageDuration: Math.round(averageDuration),
        successRate: Math.round(successRate * 100) / 100,
        lastSuccessfulSync,
      },
      health: {
        status: health as any,
        issues,
        recommendations,
      },
    };
  }

  /**
   * Generic sync method that works with any provider
   */
  private async syncAccount(
    provider: any,
    account: EmailAccount,
    syncType: SyncType,
    options?: SyncJobData['options'],
  ): Promise<{
    processed: number;
    created: number;
    updated: number;
    skipped: number;
    apiCalls: number;
    avgResponseTime: number;
  }> {
    // For now, delegate to Gmail-specific logic
    // In the future, this can be made truly generic
    if (account.provider === EmailProvider.GMAIL) {
      return this.syncGmailAccount(account, syncType, options);
    } else {
      throw new Error(`Generic sync not yet implemented for ${account.provider}`);
    }
  }
}