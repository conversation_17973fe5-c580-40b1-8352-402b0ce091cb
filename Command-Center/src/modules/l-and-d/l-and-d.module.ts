import { Module, forwardRef } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { LAndDWebSocketService } from './websocket/l-and-d-websocket.service';
import { WebSocketRoomService } from './websocket/websocket-room.service';
import { NotificationModule } from '../notifications/notification.module';
import { SyncModule } from '../sync/sync.module';

/**
 * L&D (Learning and Development) Module
 * Handles real-time WebSocket functionality for L&D applications
 */
@Module({
  imports: [
    EventEmitterModule.forRoot(),
    forwardRef(() => NotificationModule),
    forwardRef(() => SyncModule),
  ],
  providers: [LAndDWebSocketService, WebSocketRoomService],
  exports: [LAndDWebSocketService, WebSocketRoomService],
})
export class LAndDModule {}
