import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { Server } from 'socket.io';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  LAndDApp,
  LAndDEvent,
  CrossAppEvent,
  UserPresenceInfo,
  PresenceStatus,
  ConnectionData,
  EmitOptions,
  BroadcastEvent,
  ActivityEvent,
  OnlineUser,
} from './websocket-event.types';
import { AuthenticatedSocket } from '../../../common/gateways/app.gateway';
import { WebSocketRoomService } from './websocket-room.service';
import { NotificationService } from '../../notifications/notification.service';
import { DataSyncService } from '../../sync/data-sync.service';

/**
 * L&D WebSocket Service
 * Core service for managing L&D-specific WebSocket functionality
 */
@Injectable()
export class LAndDWebSocketService {
  private readonly logger = new Logger(LAndDWebSocketService.name);

  // Active connections: socketId -> ConnectionData
  private readonly connections = new Map<string, ConnectionData>();

  // User connections: userId -> Set<socketId>
  private readonly userConnections = new Map<string, Set<string>>();

  // User presence: userId -> UserPresenceInfo
  private readonly userPresence = new Map<string, UserPresenceInfo>();

  // User activity tracking: userId -> ActivityEvent[]
  private readonly userActivity = new Map<string, ActivityEvent[]>();

  private server: Server;

  constructor(
    private readonly roomService: WebSocketRoomService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => NotificationService))
    private readonly notificationService: NotificationService,
    @Inject(forwardRef(() => DataSyncService))
    private readonly syncService: DataSyncService,
  ) {}

  /**
   * Set the WebSocket server instance
   */
  setServer(server: Server): void {
    this.server = server;
    // Also set server for sync service
    this.syncService.setWebSocketService(this);
  }

  /**
   * Handle user connection
   */
  async handleUserConnection(client: AuthenticatedSocket): Promise<void> {
    const userId = client.userId;
    if (!userId) {
      this.logger.warn(`Connection without userId: ${client.id}`);
      return;
    }

    // Create connection data
    const connectionData: ConnectionData = {
      socketId: client.id,
      userId,
      connectedAt: new Date(),
      apps: [], // Will be populated when user joins apps
      rooms: [],
      metadata: {
        userAgent: client.handshake.headers['user-agent'],
        ip: client.handshake.address,
      },
    };

    // Store connection
    this.connections.set(client.id, connectionData);

    // Track user connections
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(client.id);

    // Update user presence
    await this.updateUserPresence(userId, 'online');

    // Join user to default app rooms (if specified in query)
    const requestedApps = this.extractRequestedApps(client);
    if (requestedApps.length > 0) {
      await this.joinUserToAppRooms(client, requestedApps);
    }

    // Track activity
    await this.trackUserActivity(userId, requestedApps[0], 'connected', {
      socketId: client.id,
      timestamp: new Date(),
    });

    // Emit connection event
    this.eventEmitter.emit('websocket.user.connected', {
      userId,
      socketId: client.id,
      apps: requestedApps,
      timestamp: new Date(),
    });

    this.logger.log(`User ${userId} connected with socket ${client.id}`);
  }

  /**
   * Handle user disconnection
   */
  async handleUserDisconnection(client: AuthenticatedSocket): Promise<void> {
    const userId = client.userId;
    if (!userId) return;

    // Remove from rooms
    await this.roomService.removeUserFromAllRooms(client, this.server);

    // Remove connection
    this.connections.delete(client.id);

    // Update user connections
    const userSocketSet = this.userConnections.get(userId);
    if (userSocketSet) {
      userSocketSet.delete(client.id);

      // If no more connections, update presence to offline
      if (userSocketSet.size === 0) {
        this.userConnections.delete(userId);
        await this.updateUserPresence(userId, 'offline');
      }
    }

    // Track activity
    const connection = this.connections.get(client.id);
    if (connection && connection.apps.length > 0) {
      await this.trackUserActivity(userId, connection.apps[0], 'disconnected', {
        socketId: client.id,
        timestamp: new Date(),
      });
    }

    // Emit disconnection event
    this.eventEmitter.emit('websocket.user.disconnected', {
      userId,
      socketId: client.id,
      timestamp: new Date(),
    });

    this.logger.log(`User ${userId} disconnected socket ${client.id}`);
  }

  /**
   * Join user to app-specific rooms
   */
  async joinUserToAppRooms(
    client: AuthenticatedSocket,
    apps: LAndDApp[],
  ): Promise<void> {
    const userId = client.userId;
    if (!userId) return;

    // Update connection data
    const connection = this.connections.get(client.id);
    if (connection) {
      connection.apps = [...new Set([...connection.apps, ...apps])];
    }

    // Join app rooms
    await this.roomService.joinUserToAppRooms(client, apps, this.server);

    // Update user presence with current app
    const presence = this.userPresence.get(userId);
    if (presence) {
      presence.currentApp = apps[0]; // Set primary app
      presence.activeRooms = this.roomService.getUserRooms(userId);
    }

    this.logger.log(`User ${userId} joined apps: ${apps.join(', ')}`);
  }

  /**
   * Broadcast to specific app
   */
  async broadcastToApp(
    app: LAndDApp,
    event: string,
    data: any,
    options?: EmitOptions,
  ): Promise<void> {
    const roomId = `${app}:app:${app}`;

    // Get app room participants
    const participants = this.roomService.getRoomParticipants(roomId);

    // Apply filters
    let targetUsers = participants;
    if (options?.includeUserIds) {
      targetUsers = targetUsers.filter((userId) =>
        options.includeUserIds!.includes(userId),
      );
    }
    if (options?.excludeUserIds) {
      targetUsers = targetUsers.filter(
        (userId) => !options.excludeUserIds!.includes(userId),
      );
    }

    // Emit to room
    this.server.to(roomId).emit(event, {
      ...data,
      app,
      timestamp: new Date(),
      recipients: targetUsers.length,
    });

    this.logger.log(
      `Broadcasted ${event} to ${app} app (${targetUsers.length} users)`,
    );
  }

  /**
   * Send event to specific user
   */
  async sendToUser(
    userId: string,
    event: string,
    data: any,
    excludeSocketId?: string,
  ): Promise<void> {
    const socketIds = this.userConnections.get(userId);
    if (!socketIds || socketIds.size === 0) {
      this.logger.warn(`User ${userId} not connected`);
      return;
    }

    socketIds.forEach((socketId) => {
      if (socketId !== excludeSocketId) {
        this.server.to(socketId).emit(event, {
          ...data,
          userId,
          timestamp: new Date(),
        });
      }
    });

    this.logger.log(`Sent ${event} to user ${userId}`);
  }

  /**
   * Handle cross-app events
   */
  async handleCrossAppEvent(
    sourceApp: LAndDApp,
    targetApps: LAndDApp[],
    event: LAndDEvent,
  ): Promise<void> {
    const crossAppEvent: CrossAppEvent = {
      ...event,
      sourceApp,
      propagationRules: targetApps.map((app) => ({
        targetApp: app,
        condition: () => true, // Default: propagate to all
        transform: (evt) => ({ ...evt, propagatedFrom: sourceApp }),
      })),
    };

    // Propagate to target apps
    for (const targetApp of targetApps) {
      if (targetApp !== sourceApp) {
        await this.broadcastToApp(targetApp, 'cross-app:event', crossAppEvent);
      }
    }

    // Emit cross-app event
    this.eventEmitter.emit('websocket.cross-app.event', crossAppEvent);

    this.logger.log(
      `Cross-app event from ${sourceApp} to ${targetApps.join(', ')}: ${event.action}`,
    );
  }

  /**
   * Update user presence
   */
  async updateUserPresence(
    userId: string,
    status: PresenceStatus,
    app?: LAndDApp,
  ): Promise<void> {
    const existing = this.userPresence.get(userId);

    const presence: UserPresenceInfo = {
      userId,
      status,
      lastSeen: new Date(),
      currentApp: app || existing?.currentApp,
      activeRooms: this.roomService.getUserRooms(userId),
    };

    this.userPresence.set(userId, presence);

    // Broadcast presence update
    await this.broadcastUserPresence(userId);

    this.logger.debug(`Updated presence for user ${userId}: ${status}`);
  }

  /**
   * Broadcast user presence update
   */
  async broadcastUserPresence(userId: string): Promise<void> {
    const presence = this.userPresence.get(userId);
    if (!presence) return;

    // Broadcast to all apps the user is active in
    const userRooms = this.roomService.getUserRooms(userId);

    userRooms.forEach((roomId) => {
      this.server.to(roomId).emit('presence:updated', presence);
    });

    this.logger.debug(`Broadcasted presence update for user ${userId}`);
  }

  /**
   * Get user presence
   */
  async getUserPresence(userId: string): Promise<UserPresenceInfo | undefined> {
    return this.userPresence.get(userId);
  }

  /**
   * Track user activity
   */
  async trackUserActivity(
    userId: string,
    app: LAndDApp,
    action: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const activity: ActivityEvent = {
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      app,
      action,
      timestamp: new Date(),
      metadata,
    };

    // Store activity (keep last 100 activities per user)
    if (!this.userActivity.has(userId)) {
      this.userActivity.set(userId, []);
    }

    const activities = this.userActivity.get(userId)!;
    activities.unshift(activity);

    // Keep only last 100 activities
    if (activities.length > 100) {
      activities.splice(100);
    }

    // Emit activity event
    this.eventEmitter.emit('websocket.user.activity', activity);

    this.logger.debug(
      `Tracked activity for user ${userId}: ${action} in ${app}`,
    );
  }

  /**
   * Get recent user activity
   */
  async getRecentActivity(
    userId: string,
    limit: number = 20,
  ): Promise<ActivityEvent[]> {
    const activities = this.userActivity.get(userId) || [];
    return activities.slice(0, limit);
  }

  /**
   * Get online users
   */
  async getOnlineUsers(app?: LAndDApp): Promise<OnlineUser[]> {
    const onlineUsers: OnlineUser[] = [];

    this.userPresence.forEach((presence, userId) => {
      if (presence.status === 'online' || presence.status === 'busy') {
        if (!app || presence.currentApp === app) {
          const socketIds = Array.from(this.userConnections.get(userId) || []);

          onlineUsers.push({
            userId,
            presence: presence.status,
            currentApp: presence.currentApp!,
            connectedAt: new Date(), // Should track actual connection time
            socketIds,
            activeRooms: presence.activeRooms,
          });
        }
      }
    });

    return onlineUsers;
  }

  /**
   * Send L&D notification
   */
  async sendLAndDNotification(
    userId: string,
    app: LAndDApp,
    type: string,
    title: string,
    message: string,
    data?: any,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium',
  ): Promise<void> {
    try {
      await this.notificationService.sendRealTimeNotification(userId, {
        title,
        message,
        type: type as any,
        app,
        priority,
        data: data || {},
      });

      this.logger.log(
        `Sent L&D notification to user ${userId} in ${app}: ${title}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send L&D notification to user ${userId}:`,
        error,
      );
    }
  }

  /**
   * Send cross-app L&D notification
   */
  async sendCrossAppLAndDNotification(
    userIds: string[],
    sourceApp: LAndDApp,
    targetApp: LAndDApp,
    type: string,
    title: string,
    message: string,
    data?: any,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium',
  ): Promise<void> {
    try {
      await this.notificationService.sendCrossAppNotification(
        sourceApp,
        userIds,
        {
          title,
          message,
          type: type as any,
          app: targetApp,
          priority,
          data: data || {},
        },
      );

      this.logger.log(
        `Sent cross-app notification from ${sourceApp} to ${targetApp} for ${userIds.length} users`,
      );
    } catch (error) {
      this.logger.error(`Failed to send cross-app notification:`, error);
    }
  }

  /**
   * Sync entity data across applications
   */
  async syncEntityData(
    userId: string,
    entityType: string,
    entityId: string,
    changes: Record<string, any>,
    sourceApp: LAndDApp,
    operation: 'create' | 'update' | 'delete' = 'update',
    metadata?: Record<string, any>,
  ): Promise<void> {
    try {
      await this.syncService.syncEntityUpdate(
        entityType,
        entityId,
        changes,
        sourceApp,
        userId,
        operation,
        metadata,
      );

      this.logger.log(
        `Synced ${entityType}:${entityId} from ${sourceApp} by user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to sync entity data for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Subscribe user to sync events
   */
  async subscribeToSync(
    userId: string,
    app: LAndDApp,
    entityType: string,
    entityId?: string,
  ): Promise<string> {
    try {
      const subscriptionId = await this.syncService.subscribeToSync(
        userId,
        app,
        entityType,
        entityId,
      );

      this.logger.log(`User ${userId} subscribed to ${entityType} sync events`);
      return subscriptionId;
    } catch (error) {
      this.logger.error(`Failed to subscribe user ${userId} to sync:`, error);
      throw error;
    }
  }

  /**
   * Unsubscribe user from sync events
   */
  async unsubscribeFromSync(subscriptionId: string): Promise<void> {
    try {
      await this.syncService.unsubscribeFromSync(subscriptionId);
      this.logger.log(`Unsubscribed from sync: ${subscriptionId}`);
    } catch (error) {
      this.logger.error(
        `Failed to unsubscribe from sync ${subscriptionId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Lock entity for editing
   */
  async lockEntity(
    userId: string,
    entityType: string,
    entityId: string,
    app: LAndDApp,
    lockType: 'read' | 'write' | 'exclusive' = 'write',
    reason?: string,
  ): Promise<string> {
    try {
      const lockId = await this.syncService.lockEntity(
        entityType,
        entityId,
        userId,
        app,
        lockType,
        300000, // 5 minutes
        reason,
      );

      this.logger.log(
        `User ${userId} locked ${entityType}:${entityId} in ${app}`,
      );
      return lockId;
    } catch (error) {
      this.logger.error(`Failed to lock entity for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Unlock entity
   */
  async unlockEntity(
    userId: string,
    entityType: string,
    entityId: string,
  ): Promise<void> {
    try {
      await this.syncService.unlockEntity(entityType, entityId, userId);
      this.logger.log(`User ${userId} unlocked ${entityType}:${entityId}`);
    } catch (error) {
      this.logger.error(`Failed to unlock entity for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get sync status for entity
   */
  getSyncStatus(entityType: string, entityId: string) {
    return this.syncService.getSyncStatus(entityType, entityId);
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      connections: {
        total: this.connections.size,
        byUser: this.userConnections.size,
      },
      presence: {
        online: Array.from(this.userPresence.values()).filter(
          (p) => p.status === 'online',
        ).length,
        away: Array.from(this.userPresence.values()).filter(
          (p) => p.status === 'away',
        ).length,
        busy: Array.from(this.userPresence.values()).filter(
          (p) => p.status === 'busy',
        ).length,
        offline: Array.from(this.userPresence.values()).filter(
          (p) => p.status === 'offline',
        ).length,
      },
      activity: {
        totalEvents: Array.from(this.userActivity.values()).reduce(
          (sum, activities) => sum + activities.length,
          0,
        ),
        activeUsers: this.userActivity.size,
      },
      rooms: this.roomService.getRoomStats(),
    };
  }

  /**
   * Extract requested apps from client handshake
   */
  private extractRequestedApps(client: AuthenticatedSocket): LAndDApp[] {
    const apps = client.handshake.query.apps;
    if (!apps) return [];

    const appString = Array.isArray(apps) ? apps[0] : apps;
    const requestedApps = appString.split(',') as LAndDApp[];

    // Validate apps
    const validApps: LAndDApp[] = ['training', 'vendors', 'wins'];
    return requestedApps.filter((app) => validApps.includes(app));
  }

  /**
   * Clean up inactive connections and presence
   */
  async cleanup(): Promise<void> {
    const now = new Date();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes

    // Clean up stale presence
    this.userPresence.forEach((presence, userId) => {
      if (
        presence.status !== 'offline' &&
        now.getTime() - presence.lastSeen.getTime() > staleThreshold
      ) {
        presence.status = 'offline';
        presence.lastSeen = now;
      }
    });

    // Clean up old activities (keep last 24 hours)
    const activityThreshold = 24 * 60 * 60 * 1000; // 24 hours
    this.userActivity.forEach((activities, userId) => {
      const recentActivities = activities.filter(
        (activity) =>
          now.getTime() - activity.timestamp.getTime() < activityThreshold,
      );
      this.userActivity.set(userId, recentActivities);
    });

    this.logger.debug('Performed cleanup of stale data');
  }
}
