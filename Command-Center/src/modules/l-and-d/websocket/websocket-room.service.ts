import { Injectable, Logger } from '@nestjs/common';
import { Server } from 'socket.io';
import {
  RoomInfo,
  RoomType,
  LAndDApp,
  ConnectionData,
  UserPresenceInfo,
  PresenceStatus,
} from './websocket-event.types';
import { AuthenticatedSocket } from '../../../common/gateways/app.gateway';

/**
 * WebSocket Room Service
 * Manages room creation, user assignments, and room-based communication
 */
@Injectable()
export class WebSocketRoomService {
  private readonly logger = new Logger(WebSocketRoomService.name);

  // Room registry: roomId -> RoomInfo
  private readonly rooms = new Map<string, RoomInfo>();

  // User to rooms mapping: userId -> Set<roomId>
  private readonly userRooms = new Map<string, Set<string>>();

  // Room to users mapping: roomId -> Set<userId>
  private readonly roomUsers = new Map<string, Set<string>>();

  /**
   * Create a new room
   */
  async createRoom(
    type: RoomType,
    app: LAndDApp,
    name: string,
    creatorId: string,
    options?: {
      description?: string;
      metadata?: Record<string, any>;
    },
  ): Promise<RoomInfo> {
    const roomId = this.generateRoomId(type, app, name);

    if (this.rooms.has(roomId)) {
      this.logger.warn(`Room ${roomId} already exists`);
      return this.rooms.get(roomId)!;
    }

    const room: RoomInfo = {
      id: roomId,
      type,
      app,
      name,
      description: options?.description,
      participants: [creatorId],
      createdAt: new Date(),
      metadata: options?.metadata,
    };

    this.rooms.set(roomId, room);
    this.roomUsers.set(roomId, new Set([creatorId]));

    // Add creator to user rooms
    if (!this.userRooms.has(creatorId)) {
      this.userRooms.set(creatorId, new Set());
    }
    this.userRooms.get(creatorId)!.add(roomId);

    this.logger.log(`Created room: ${roomId} by user: ${creatorId}`);
    return room;
  }

  /**
   * Join user to a room
   */
  async joinRoom(
    client: AuthenticatedSocket,
    roomId: string,
    server: Server,
  ): Promise<boolean> {
    const userId = client.userId;
    if (!userId) {
      this.logger.warn(`Cannot join room ${roomId}: User not authenticated`);
      return false;
    }

    const room = this.rooms.get(roomId);
    if (!room) {
      this.logger.warn(`Room ${roomId} does not exist`);
      return false;
    }

    // Check if user can join room
    if (!this.canUserJoinRoom(userId, room)) {
      this.logger.warn(
        `User ${userId} cannot join room ${roomId}: Access denied`,
      );
      return false;
    }

    // Join socket.io room
    await client.join(roomId);

    // Update room data
    const roomUserSet = this.roomUsers.get(roomId) || new Set();
    roomUserSet.add(userId);
    this.roomUsers.set(roomId, roomUserSet);

    // Update user rooms
    if (!this.userRooms.has(userId)) {
      this.userRooms.set(userId, new Set());
    }
    this.userRooms.get(userId)!.add(roomId);

    // Update room participants
    room.participants = Array.from(roomUserSet);

    // Track client rooms
    if (!client.rooms) {
      client.rooms = new Set();
    }
    client.rooms.add(roomId);

    // Notify room members
    server.to(roomId).emit('room:user_joined', {
      roomId,
      userId,
      timestamp: new Date(),
      userCount: roomUserSet.size,
    });

    this.logger.log(`User ${userId} joined room ${roomId}`);
    return true;
  }

  /**
   * Leave user from a room
   */
  async leaveRoom(
    client: AuthenticatedSocket,
    roomId: string,
    server: Server,
  ): Promise<boolean> {
    const userId = client.userId;
    if (!userId) return false;

    const room = this.rooms.get(roomId);
    if (!room) return false;

    // Leave socket.io room
    await client.leave(roomId);

    // Update room data
    const roomUserSet = this.roomUsers.get(roomId);
    if (roomUserSet) {
      roomUserSet.delete(userId);
      if (roomUserSet.size === 0) {
        this.roomUsers.delete(roomId);
      }
    }

    // Update user rooms
    const userRoomSet = this.userRooms.get(userId);
    if (userRoomSet) {
      userRoomSet.delete(roomId);
      if (userRoomSet.size === 0) {
        this.userRooms.delete(userId);
      }
    }

    // Update room participants
    room.participants = Array.from(roomUserSet || []);

    // Track client rooms
    if (client.rooms) {
      client.rooms.delete(roomId);
    }

    // Notify room members
    server.to(roomId).emit('room:user_left', {
      roomId,
      userId,
      timestamp: new Date(),
      userCount: roomUserSet?.size || 0,
    });

    this.logger.log(`User ${userId} left room ${roomId}`);
    return true;
  }

  /**
   * Get app-specific rooms for user auto-join
   */
  getAppRooms(app: LAndDApp): string[] {
    const appRooms: string[] = [];

    this.rooms.forEach((room, roomId) => {
      if (room.app === app && room.type === 'app') {
        appRooms.push(roomId);
      }
    });

    return appRooms;
  }

  /**
   * Join user to all app-specific rooms
   */
  async joinUserToAppRooms(
    client: AuthenticatedSocket,
    apps: LAndDApp[],
    server: Server,
  ): Promise<void> {
    for (const app of apps) {
      const appRoomId = this.generateRoomId('app', app, app);

      // Create app room if it doesn't exist
      if (!this.rooms.has(appRoomId)) {
        await this.createRoom('app', app, app, client.userId!, {
          description: `Main room for ${app} application`,
          metadata: { isAppRoom: true },
        });
      }

      await this.joinRoom(client, appRoomId, server);
    }
  }

  /**
   * Get room information
   */
  getRoomInfo(roomId: string): RoomInfo | undefined {
    return this.rooms.get(roomId);
  }

  /**
   * Get user's rooms
   */
  getUserRooms(userId: string): string[] {
    return Array.from(this.userRooms.get(userId) || []);
  }

  /**
   * Get room participants
   */
  getRoomParticipants(roomId: string): string[] {
    return Array.from(this.roomUsers.get(roomId) || []);
  }

  /**
   * Remove user from all rooms (on disconnect)
   */
  async removeUserFromAllRooms(
    client: AuthenticatedSocket,
    server: Server,
  ): Promise<void> {
    const userId = client.userId;
    if (!userId) return;

    const userRoomSet = this.userRooms.get(userId);
    if (!userRoomSet) return;

    // Leave all rooms
    for (const roomId of userRoomSet) {
      await this.leaveRoom(client, roomId, server);
    }
  }

  /**
   * Get rooms by app
   */
  getRoomsByApp(app: LAndDApp): RoomInfo[] {
    const appRooms: RoomInfo[] = [];

    this.rooms.forEach((room) => {
      if (room.app === app) {
        appRooms.push(room);
      }
    });

    return appRooms;
  }

  /**
   * Get rooms by type
   */
  getRoomsByType(type: RoomType): RoomInfo[] {
    const typeRooms: RoomInfo[] = [];

    this.rooms.forEach((room) => {
      if (room.type === type) {
        typeRooms.push(room);
      }
    });

    return typeRooms;
  }

  /**
   * Check if room exists
   */
  roomExists(roomId: string): boolean {
    return this.rooms.has(roomId);
  }

  /**
   * Get room statistics
   */
  getRoomStats() {
    return {
      totalRooms: this.rooms.size,
      activeRooms: Array.from(this.roomUsers.entries()).filter(
        ([_, users]) => users.size > 0,
      ).length,
      roomsByApp: {
        training: this.getRoomsByApp('training').length,
        vendors: this.getRoomsByApp('vendors').length,
        wins: this.getRoomsByApp('wins').length,
      },
      roomsByType: {
        app: this.getRoomsByType('app').length,
        user: this.getRoomsByType('user').length,
        course: this.getRoomsByType('course').length,
        project: this.getRoomsByType('project').length,
        team: this.getRoomsByType('team').length,
        discussion: this.getRoomsByType('discussion').length,
        live_session: this.getRoomsByType('live_session').length,
      },
    };
  }

  /**
   * Generate consistent room ID
   */
  private generateRoomId(
    type: RoomType,
    app: LAndDApp,
    identifier: string,
  ): string {
    return `${app}:${type}:${identifier}`;
  }

  /**
   * Check if user can join room (basic authorization)
   */
  private canUserJoinRoom(userId: string, room: RoomInfo): boolean {
    // Basic implementation - in production, add proper access control
    // Check if room is public, user has permission, etc.

    // For now, allow authenticated users to join any room
    return !!userId;
  }

  /**
   * Initialize default app rooms
   */
  async initializeDefaultRooms(): Promise<void> {
    const apps: LAndDApp[] = ['training', 'vendors', 'wins'];

    for (const app of apps) {
      const roomId = this.generateRoomId('app', app, app);

      if (!this.rooms.has(roomId)) {
        await this.createRoom('app', app, app, 'system', {
          description: `Main application room for ${app}`,
          metadata: {
            isSystemRoom: true,
            isAppRoom: true,
            autoJoin: true,
          },
        });
      }
    }

    this.logger.log('Default app rooms initialized');
  }
}
