/**
 * WebSocket Event Types for L&D Platform
 * Defines all event types and data structures for real-time communication
 */

// Base event interface
export interface BaseEvent {
  id: string;
  timestamp: Date;
  userId: string;
  app: LAndDApp;
}

// L&D Application types
export type LAndDApp = 'training' | 'vendors' | 'wins';

// L&D specific event interface
export interface LAndDEvent extends BaseEvent {
  type: LAndDApp;
  action: string;
  payload: any;
  targetApps?: LAndDApp[];
  room?: string;
}

// Cross-app event interface
export interface CrossAppEvent extends LAndDEvent {
  sourceApp: LAndDApp;
  propagationRules: PropagationRule[];
}

// Propagation rule for cross-app events
export interface PropagationRule {
  targetApp: LAndDApp;
  condition?: (event: LAndDEvent) => boolean;
  transform?: (event: LAndDEvent) => Partial<LAndDEvent>;
}

// User presence status
export type PresenceStatus = 'online' | 'away' | 'offline' | 'busy';

// User presence information
export interface UserPresenceInfo {
  userId: string;
  status: PresenceStatus;
  lastSeen: Date;
  currentApp?: LAndDApp;
  activeRooms: string[];
}

// App-specific presence information
export interface AppPresenceInfo {
  app: LAndDApp;
  onlineUsers: string[];
  totalUsers: number;
  activeRooms: Record<string, number>; // room -> user count
}

// Activity event for tracking user activity
export interface ActivityEvent {
  id: string;
  userId: string;
  app: LAndDApp;
  action: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Training-specific event types
export interface TrainingEvent extends LAndDEvent {
  type: 'training';
  action:
    | 'course_assigned'
    | 'assessment_due'
    | 'progress_updated'
    | 'completion_achieved'
    | 'discussion_post'
    | 'live_session_started'
    | 'live_session_joined'
    | 'quiz_submitted';
  payload: {
    courseId?: string;
    assessmentId?: string;
    sessionId?: string;
    progress?: number;
    discussionId?: string;
    quizId?: string;
    [key: string]: any;
  };
}

// Vendor-specific event types
export interface VendorEvent extends LAndDEvent {
  type: 'vendors';
  action:
    | 'proposal_received'
    | 'review_required'
    | 'contract_updated'
    | 'payment_processed'
    | 'vendor_approved'
    | 'rfp_published'
    | 'negotiation_started';
  payload: {
    vendorId?: string;
    proposalId?: string;
    contractId?: string;
    rfpId?: string;
    amount?: number;
    [key: string]: any;
  };
}

// Wins-specific event types
export interface WinsEvent extends LAndDEvent {
  type: 'wins';
  action:
    | 'submission_created'
    | 'win_featured'
    | 'team_achievement'
    | 'weekly_report_generated'
    | 'milestone_reached'
    | 'collaboration_started'
    | 'recognition_given';
  payload: {
    submissionId?: string;
    teamId?: string;
    weekNumber?: number;
    category?: string;
    milestoneId?: string;
    recognitionId?: string;
    [key: string]: any;
  };
}

// Room types for different contexts
export type RoomType =
  | 'app' // app-wide room (e.g., 'training', 'vendors', 'wins')
  | 'user' // user-specific room (e.g., 'user:123')
  | 'course' // course-specific room (e.g., 'course:456')
  | 'project' // project-specific room (e.g., 'project:789')
  | 'team' // team-specific room (e.g., 'team:abc')
  | 'discussion' // discussion room (e.g., 'discussion:def')
  | 'live_session'; // live session room (e.g., 'session:ghi')

// Room information
export interface RoomInfo {
  id: string;
  type: RoomType;
  app: LAndDApp;
  name: string;
  description?: string;
  participants: string[]; // user IDs
  createdAt: Date;
  metadata?: Record<string, any>;
}

// WebSocket connection data
export interface ConnectionData {
  socketId: string;
  userId: string;
  connectedAt: Date;
  apps: LAndDApp[];
  rooms: string[];
  metadata?: Record<string, any>;
}

// Event emission options
export interface EmitOptions {
  excludeSocketId?: string;
  excludeUserIds?: string[];
  includeUserIds?: string[];
  rooms?: string[];
  apps?: LAndDApp[];
}

// Broadcast event interface
export interface BroadcastEvent {
  id: string;
  event: string;
  data: any;
  timestamp: Date;
  source: {
    app: LAndDApp;
    userId?: string;
  };
  targets?: {
    userIds?: string[];
    rooms?: string[];
    apps?: LAndDApp[];
    roles?: string[];
    departments?: string[];
  };
}

// Broadcast condition for conditional broadcasting
export interface BroadcastCondition {
  userRole?: string;
  department?: string;
  app?: LAndDApp;
  presence?: PresenceStatus;
  customPredicate?: (userId: string) => Promise<boolean>;
}

// Real-time data sync events
export interface DataSyncEvent {
  id: string;
  entityType: string;
  entityId: string;
  operation: 'create' | 'update' | 'delete';
  changes: Record<string, any>;
  sourceApp: LAndDApp;
  timestamp: Date;
  userId: string;
}

// Sync conflict representation
export interface SyncConflict {
  id: string;
  entityType: string;
  entityId: string;
  conflictType: 'concurrent_update' | 'version_mismatch' | 'data_inconsistency';
  localChanges: Record<string, any>;
  remoteChanges: Record<string, any>;
  timestamp: Date;
}

// Resolved sync change
export interface ResolvedChange {
  entityType: string;
  entityId: string;
  resolvedData: Record<string, any>;
  resolution: 'local_wins' | 'remote_wins' | 'merged' | 'manual_review';
  timestamp: Date;
}

// Sync status information
export interface SyncStatus {
  entityType: string;
  entityId: string;
  lastSyncAt: Date;
  version: number;
  conflicts: SyncConflict[];
  status: 'synced' | 'pending' | 'conflict' | 'error';
}

// Online user information
export interface OnlineUser {
  userId: string;
  presence: PresenceStatus;
  currentApp: LAndDApp;
  connectedAt: Date;
  socketIds: string[];
  activeRooms: string[];
}
