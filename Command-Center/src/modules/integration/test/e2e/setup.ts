import { Client } from 'pg';
import * as dotenv from 'dotenv';

dotenv.config({ path: '.env.test' });

beforeAll(async () => {
  // Create test database if it doesn't exist
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: 'postgres', // Connect to default database
  });

  await client.connect();

  try {
    // Check if test database exists
    const res = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'luminar_test'",
    );

    if (res.rows.length === 0) {
      // Create test database
      await client.query('CREATE DATABASE luminar_test');
      console.log('Test database created');
    }
  } catch (error) {
    console.error('Error setting up test database:', error);
  } finally {
    await client.end();
  }
});

// Increase timeout for E2E tests
jest.setTimeout(30000);

// Mock external services
jest.mock('../../services/connectors/e-connect-connector.service', () => ({
  EConnectConnectorService: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
    sendData: jest.fn().mockResolvedValue({ success: true }),
    disconnect: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock('../../services/connectors/lighthouse-connector.service', () => ({
  LighthouseConnectorService: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
    sendData: jest.fn().mockResolvedValue({ success: true }),
    disconnect: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock('../../services/connectors/training-connector.service', () => ({
  TrainingConnectorService: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
    sendData: jest.fn().mockResolvedValue({ success: true }),
    disconnect: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock('../../services/connectors/vendors-connector.service', () => ({
  VendorsConnectorService: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
    sendData: jest.fn().mockResolvedValue({ success: true }),
    disconnect: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock('../../services/connectors/wins-connector.service', () => ({
  WinsConnectorService: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
    sendData: jest.fn().mockResolvedValue({ success: true }),
    disconnect: jest.fn().mockResolvedValue(true),
  })),
}));

// Global test utilities
global.testUtils = {
  generateTestUser: () => ({
    id: `test-user-${Date.now()}`,
    email: `test-${Date.now()}@example.com`,
    roles: ['user'],
  }),

  generateTestActivity: (overrides = {}) => ({
    userId: 'test-user',
    source: 'e-connect',
    type: 'test.activity',
    data: { test: true },
    timestamp: new Date(),
    ...overrides,
  }),

  waitForWebSocket: (socket, event, timeout = 5000) => {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Timeout waiting for event: ${event}`));
      }, timeout);

      socket.once(event, (data) => {
        clearTimeout(timer);
        resolve(data);
      });
    });
  },
};
