import { Test, TestingModule } from '@nestjs/testing';
import { AmnaIntegrationService } from '../services/core/amna-integration.service';
import { HealthCheckService } from '../services/monitoring/health-check.service';
import { MetricsService } from '../services/monitoring/metrics.service';
import { PerformanceMonitorService } from '../services/monitoring/performance-monitor.service';
import { ErrorTrackerService } from '../services/monitoring/error-tracker.service';
import { EventBusService } from '../services/core/event-bus.service';
import { CacheManagerService } from '../services/core/cache-manager.service';
import { CircuitBreakerService } from '../services/core/circuit-breaker.service';
import { DataFederationService } from '../services/core/data-federation.service';
import { UserPreferencesService } from '../services/core/user-preferences.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { IntegrationLogs } from '../entities/integration-logs.entity';

describe('Integration Module Services', () => {
  let amnaService: AmnaIntegrationService;
  let healthCheckService: HealthCheckService;
  let metricsService: MetricsService;
  let performanceMonitorService: PerformanceMonitorService;
  let errorTrackerService: ErrorTrackerService;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
  };

  const mockCacheManager = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
  };

  const mockEventBus = {
    getActiveConnections: jest.fn().mockResolvedValue(5),
    emit: jest.fn(),
  };

  const mockCircuitBreaker = {
    getState: jest.fn().mockResolvedValue('closed'),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
    on: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AmnaIntegrationService,
        HealthCheckService,
        MetricsService,
        PerformanceMonitorService,
        ErrorTrackerService,
        DataFederationService,
        UserPreferencesService,
        {
          provide: EventBusService,
          useValue: mockEventBus,
        },
        {
          provide: CacheManagerService,
          useValue: mockCacheManager,
        },
        {
          provide: CircuitBreakerService,
          useValue: mockCircuitBreaker,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: getRepositoryToken(IntegrationLogs),
          useValue: mockRepository,
        },
        {
          provide: 'USER_PREFERENCES_REPOSITORY',
          useValue: mockRepository,
        },
      ],
    }).compile();

    amnaService = module.get<AmnaIntegrationService>(AmnaIntegrationService);
    healthCheckService = module.get<HealthCheckService>(HealthCheckService);
    metricsService = module.get<MetricsService>(MetricsService);
    performanceMonitorService = module.get<PerformanceMonitorService>(
      PerformanceMonitorService,
    );
    errorTrackerService = module.get<ErrorTrackerService>(ErrorTrackerService);
  });

  describe('AMNA Integration Service', () => {
    it('should be defined', () => {
      expect(amnaService).toBeDefined();
    });

    it('should gather context from applications', async () => {
      const mockUserId = 'user123';
      const mockContext = { test: 'context' };

      mockCacheManager.get.mockResolvedValue(mockContext);

      const result = await amnaService.gatherContext(mockUserId);

      expect(result).toBeDefined();
      expect(mockCacheManager.get).toHaveBeenCalled();
    });
  });

  describe('Health Check Service', () => {
    it('should be defined', () => {
      expect(healthCheckService).toBeDefined();
    });

    it('should perform health checks', async () => {
      const healthReport = await healthCheckService.getHealthStatus();

      expect(healthReport).toBeDefined();
      expect(healthReport.overall).toBeDefined();
      expect(healthReport.services).toBeInstanceOf(Array);
    });
  });

  describe('Metrics Service', () => {
    it('should be defined', () => {
      expect(metricsService).toBeDefined();
    });

    it('should record requests', () => {
      expect(() => {
        metricsService.recordRequest('GET', '/api/test', 200, 100);
      }).not.toThrow();
    });

    it('should record integration events', () => {
      expect(() => {
        metricsService.recordIntegrationEvent(
          'data.synced',
          'e-connect',
          'amna',
        );
      }).not.toThrow();
    });

    it('should get metrics report', async () => {
      const report = await metricsService.getMetricsReport();

      expect(report).toBeDefined();
      expect(report.timestamp).toBeInstanceOf(Date);
      expect(report.metrics).toBeInstanceOf(Array);
      expect(report.aggregations).toBeDefined();
    });
  });

  describe('Performance Monitor Service', () => {
    it('should be defined', () => {
      expect(performanceMonitorService).toBeDefined();
    });

    it('should get current performance', async () => {
      const performance =
        await performanceMonitorService.getCurrentPerformance();

      expect(performance).toBeDefined();
      expect(performance.timestamp).toBeInstanceOf(Date);
      expect(performance.cpu).toBeDefined();
      expect(performance.memory).toBeDefined();
    });

    it('should monitor operations', () => {
      const endOperation =
        performanceMonitorService.startOperation('test-operation');

      expect(endOperation).toBeInstanceOf(Function);
      expect(() => endOperation()).not.toThrow();
    });
  });

  describe('Error Tracker Service', () => {
    it('should be defined', () => {
      expect(errorTrackerService).toBeDefined();
    });

    it('should track errors', async () => {
      const error = new Error('Test error');
      const context = { service: 'test-service', userId: 'user123' };

      const trackedError = await errorTrackerService.trackError(error, context);

      expect(trackedError).toBeDefined();
      expect(trackedError.id).toBeDefined();
      expect(trackedError.message).toBe('Test error');
      expect(trackedError.context).toEqual(context);
    });

    it('should get error report', async () => {
      const report = await errorTrackerService.getErrorReport();

      expect(report).toBeDefined();
      expect(report.totalErrors).toBeGreaterThanOrEqual(0);
      expect(report.uniqueErrors).toBeGreaterThanOrEqual(0);
      expect(report.errorsByLevel).toBeDefined();
    });
  });
});
