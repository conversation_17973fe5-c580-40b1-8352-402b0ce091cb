import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserPreferencesService } from '../services/core/user-preferences.service';
import { CircuitBreakerService } from '../services/core/circuit-breaker.service';

@Injectable()
export class IntegrationGuard implements CanActivate {
  private readonly logger = new Logger(IntegrationGuard.name);

  constructor(
    private reflector: Reflector,
    private userPreferencesService: UserPreferencesService,
    private circuitBreakerService: CircuitBreakerService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Get the target application from the request
    const targetApp = this.getTargetApplication(request);

    if (!targetApp) {
      // Allow general integration endpoints that don't target specific apps
      return true;
    }

    try {
      // Check if user has integration preferences set up
      const userPreferences =
        await this.userPreferencesService.getUserPreferences(user.id);

      if (!userPreferences) {
        this.logger.warn(`User ${user.id} has no integration preferences`);
        throw new ForbiddenException('Integration preferences not configured');
      }

      // Check if the specific integration is enabled
      const integrationEnabled = this.isIntegrationEnabled(
        userPreferences,
        targetApp,
      );

      if (!integrationEnabled) {
        throw new ForbiddenException(
          `Integration with ${targetApp} is not enabled`,
        );
      }

      // Check circuit breaker state for the target service
      const circuitState = await this.circuitBreakerService.getState(targetApp);

      if (circuitState === 'open') {
        this.logger.warn(`Circuit breaker is open for ${targetApp}`);
        throw new ForbiddenException(
          `Service ${targetApp} is temporarily unavailable`,
        );
      }

      // Additional permission checks can be added here
      // For example, check if user has specific roles or permissions for the integration

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error(
        `Error in IntegrationGuard for user ${user.id}:`,
        error,
      );
      throw new ForbiddenException('Integration access denied');
    }
  }

  /**
   * Extract target application from request
   */
  private getTargetApplication(request: any): string | null {
    // Check route params first
    if (request.params?.application) {
      return request.params.application;
    }

    // Check query params
    if (request.query?.app || request.query?.application) {
      return request.query.app || request.query.application;
    }

    // Check request body
    if (request.body?.targetApp || request.body?.application) {
      return request.body.targetApp || request.body.application;
    }

    // Extract from URL path
    const pathMatch = request.path?.match(/\/integration\/([^\/]+)/);
    if (pathMatch && pathMatch[1]) {
      return pathMatch[1];
    }

    return null;
  }

  /**
   * Check if specific integration is enabled in user preferences
   */
  private isIntegrationEnabled(
    userPreferences: any,
    targetApp: string,
  ): boolean {
    // Check general integration toggle
    if (userPreferences.integrationEnabled === false) {
      return false;
    }

    // Check app-specific toggles
    const appSpecificSettings = userPreferences.apps?.[targetApp];

    if (appSpecificSettings) {
      return appSpecificSettings.enabled !== false;
    }

    // Default to enabled if no specific setting exists
    return true;
  }
}
