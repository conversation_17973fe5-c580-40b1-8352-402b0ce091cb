import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';

// Services
import { PrivacyService } from './services/privacy.service';
import { DataAnonymizationService } from './services/data-anonymization.service';
import { DataExportService } from './services/data-export.service';
import { DataDeletionService } from './services/data-deletion.service';
import { ConsentManagementService } from './services/consent-management.service';
import { AuditService } from './services/audit.service';
import { EncryptionService } from './services/encryption.service';

// Controllers
import { PrivacyController } from './controllers/privacy.controller';
import { ConsentController } from './controllers/consent.controller';

// Entities
import { UserConsent } from './entities/user-consent.entity';
import { PrivacyRequest } from './entities/privacy-request.entity';
import { AuditLog } from './entities/audit-log.entity';
import { DataRetentionPolicy } from './entities/data-retention-policy.entity';

// Guards
import { ConsentGuard } from './guards/consent.guard';
import { PrivacyGuard } from './guards/privacy.guard';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserConsent,
      PrivacyRequest,
      AuditLog,
      DataRetentionPolicy,
    ]),
    BullModule.registerQueue(
      { name: 'data-export' },
      { name: 'data-deletion' },
      { name: 'data-anonymization' },
    ),
    HttpModule,
  ],
  controllers: [PrivacyController, ConsentController],
  providers: [
    PrivacyService,
    DataAnonymizationService,
    DataExportService,
    DataDeletionService,
    ConsentManagementService,
    AuditService,
    EncryptionService,
    ConsentGuard,
    PrivacyGuard,
  ],
  exports: [
    PrivacyService,
    ConsentManagementService,
    AuditService,
    EncryptionService,
    ConsentGuard,
    PrivacyGuard,
  ],
})
export class PrivacyModule {}
