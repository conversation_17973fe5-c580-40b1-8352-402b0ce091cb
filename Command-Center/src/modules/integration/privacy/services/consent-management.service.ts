import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserConsent } from '../entities/user-consent.entity';

export enum ConsentType {
  MARKETING = 'marketing',
  ANALYTICS = 'analytics',
  PERSONALIZATION = 'personalization',
  THIRD_PARTY_SHARING = 'third_party_sharing',
  COOKIES_FUNCTIONAL = 'cookies_functional',
  COOKIES_ANALYTICS = 'cookies_analytics',
  COOKIES_MARKETING = 'cookies_marketing',
  DATA_PROCESSING = 'data_processing',
  COMMUNICATION_EMAIL = 'communication_email',
  COMMUNICATION_SMS = 'communication_sms',
  COMMUNICATION_PHONE = 'communication_phone',
  RESEARCH = 'research',
  PRODUCT_UPDATES = 'product_updates',
}

export enum ConsentStatus {
  GRANTED = 'granted',
  DENIED = 'denied',
  WITHDRAWN = 'withdrawn',
  EXPIRED = 'expired',
  PENDING = 'pending',
}

export enum LegalBasis {
  CONSENT = 'consent',
  CONTRACT = 'contract',
  LEGAL_OBLIGATION = 'legal_obligation',
  VITAL_INTERESTS = 'vital_interests',
  PUBLIC_TASK = 'public_task',
  LEGITIMATE_INTERESTS = 'legitimate_interests',
}

interface ConsentRecord {
  userId: string;
  type: ConsentType;
  status: ConsentStatus;
  grantedAt?: Date;
  withdrawnAt?: Date;
  expiresAt?: Date;
  legalBasis: LegalBasis;
  purpose: string;
  version: string;
  metadata?: any;
}

interface ConsentPreferences {
  userId: string;
  preferences: Map<ConsentType, boolean>;
  lastUpdated: Date;
}

interface ConsentVersion {
  version: string;
  effectiveDate: Date;
  changes: string[];
  requiresReConsent: boolean;
}

@Injectable()
export class ConsentManagementService {
  private readonly logger = new Logger(ConsentManagementService.name);
  private readonly consentVersions: Map<string, ConsentVersion>;

  constructor(
    @InjectRepository(UserConsent)
    private readonly userConsentRepository: Repository<UserConsent>,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.consentVersions = this.initializeConsentVersions();
  }

  /**
   * Record user consent
   */
  async recordConsent(
    userId: string,
    type: ConsentType,
    granted: boolean,
    metadata?: any,
  ): Promise<UserConsent> {
    this.logger.log(
      `Recording consent for user ${userId}, type: ${type}, granted: ${granted}`,
    );

    const currentVersion = this.getCurrentConsentVersion();

    const consent = this.userConsentRepository.create({
      userId,
      type,
      status: granted ? ConsentStatus.GRANTED : ConsentStatus.DENIED,
      grantedAt: granted ? new Date() : null,
      version: currentVersion.version,
      legalBasis: this.determineLegalBasis(type),
      purpose: this.getConsentPurpose(type),
      metadata: {
        ...metadata,
        ip: metadata?.ip,
        userAgent: metadata?.userAgent,
        source: metadata?.source || 'web',
      },
    });

    await this.userConsentRepository.save(consent);

    // Emit consent event
    this.eventEmitter.emit('consent.recorded', {
      userId,
      type,
      status: consent.status,
      timestamp: new Date(),
    });

    // Update consent cache
    await this.updateConsentCache(userId, type, granted);

    return consent;
  }

  /**
   * Get user consent status
   */
  async getConsentStatus(
    userId: string,
    type?: ConsentType,
  ): Promise<ConsentRecord[]> {
    const query = this.userConsentRepository
      .createQueryBuilder('consent')
      .where('consent.userId = :userId', { userId })
      .andWhere('consent.status IN (:...statuses)', {
        statuses: [ConsentStatus.GRANTED, ConsentStatus.DENIED],
      })
      .orderBy('consent.createdAt', 'DESC');

    if (type) {
      query.andWhere('consent.type = :type', { type });
    }

    const consents = await query.getMany();

    // Get latest consent for each type
    const latestConsents = new Map<ConsentType, UserConsent>();
    for (const consent of consents) {
      if (!latestConsents.has(consent.type as ConsentType)) {
        latestConsents.set(consent.type as ConsentType, consent);
      }
    }

    return Array.from(latestConsents.values()).map((consent) => ({
      userId: consent.userId,
      type: consent.type as ConsentType,
      status: consent.status as ConsentStatus,
      grantedAt: consent.grantedAt,
      withdrawnAt: consent.withdrawnAt,
      expiresAt: consent.expiresAt,
      legalBasis: consent.legalBasis as LegalBasis,
      purpose: consent.purpose,
      version: consent.version,
      metadata: consent.metadata,
    }));
  }

  /**
   * Withdraw consent
   */
  async withdrawConsent(
    userId: string,
    types: ConsentType[],
    withdrawnBy: string,
  ): Promise<void> {
    this.logger.log(
      `Withdrawing consent for user ${userId}, types: ${types.join(', ')}`,
    );

    for (const type of types) {
      // Find active consent
      const activeConsent = await this.userConsentRepository.findOne({
        where: {
          userId,
          type,
          status: ConsentStatus.GRANTED,
        },
        order: { createdAt: 'DESC' },
      });

      if (activeConsent) {
        // Create withdrawal record
        const withdrawal = this.userConsentRepository.create({
          userId,
          type,
          status: ConsentStatus.WITHDRAWN,
          withdrawnAt: new Date(),
          withdrawnBy,
          version: activeConsent.version,
          legalBasis: activeConsent.legalBasis,
          purpose: activeConsent.purpose,
          metadata: {
            originalConsentId: activeConsent.id,
            withdrawalReason: 'user_request',
          },
        });

        await this.userConsentRepository.save(withdrawal);

        // Update active consent
        activeConsent.status = ConsentStatus.WITHDRAWN;
        activeConsent.withdrawnAt = new Date();
        await this.userConsentRepository.save(activeConsent);

        // Emit withdrawal event
        this.eventEmitter.emit('consent.withdrawn', {
          userId,
          type,
          timestamp: new Date(),
          withdrawnBy,
        });

        // Update consent cache
        await this.updateConsentCache(userId, type, false);
      }
    }

    // Process consent withdrawal implications
    await this.processConsentWithdrawal(userId, types);
  }

  /**
   * Check if user has valid consent
   */
  async hasValidConsent(
    userId: string,
    type: ConsentType,
    checkExpiry: boolean = true,
  ): Promise<boolean> {
    const consent = await this.userConsentRepository.findOne({
      where: {
        userId,
        type,
        status: ConsentStatus.GRANTED,
      },
      order: { createdAt: 'DESC' },
    });

    if (!consent) {
      return false;
    }

    // Check if consent is expired
    if (checkExpiry && consent.expiresAt && consent.expiresAt < new Date()) {
      return false;
    }

    // Check if consent version is still valid
    const currentVersion = this.getCurrentConsentVersion();
    if (
      consent.version !== currentVersion.version &&
      currentVersion.requiresReConsent
    ) {
      return false;
    }

    return true;
  }

  /**
   * Get user consent preferences
   */
  async getUserConsentPreferences(userId: string): Promise<ConsentPreferences> {
    const consents = await this.getConsentStatus(userId);

    const preferences = new Map<ConsentType, boolean>();
    for (const consent of consents) {
      preferences.set(consent.type, consent.status === ConsentStatus.GRANTED);
    }

    // Add defaults for missing consent types
    for (const type of Object.values(ConsentType)) {
      if (!preferences.has(type as ConsentType)) {
        preferences.set(type as ConsentType, false);
      }
    }

    return {
      userId,
      preferences,
      lastUpdated: new Date(),
    };
  }

  /**
   * Update bulk consent preferences
   */
  async updateConsentPreferences(
    userId: string,
    preferences: Map<ConsentType, boolean>,
    metadata?: any,
  ): Promise<void> {
    this.logger.log(`Updating consent preferences for user ${userId}`);

    for (const [type, granted] of preferences) {
      await this.recordConsent(userId, type, granted, metadata);
    }

    // Emit bulk update event
    this.eventEmitter.emit('consent.preferences.updated', {
      userId,
      preferences: Object.fromEntries(preferences),
      timestamp: new Date(),
    });
  }

  /**
   * Get consent history
   */
  async getConsentHistory(
    userId: string,
    type?: ConsentType,
    limit: number = 100,
  ): Promise<UserConsent[]> {
    const query = this.userConsentRepository
      .createQueryBuilder('consent')
      .where('consent.userId = :userId', { userId })
      .orderBy('consent.createdAt', 'DESC')
      .limit(limit);

    if (type) {
      query.andWhere('consent.type = :type', { type });
    }

    return query.getMany();
  }

  /**
   * Export consent records for user
   */
  async exportConsentRecords(userId: string): Promise<{
    consents: ConsentRecord[];
    history: UserConsent[];
    preferences: ConsentPreferences;
  }> {
    const consents = await this.getConsentStatus(userId);
    const history = await this.getConsentHistory(userId);
    const preferences = await this.getUserConsentPreferences(userId);

    return {
      consents,
      history,
      preferences,
    };
  }

  /**
   * Check consent requirements for operation
   */
  async checkConsentRequirements(
    userId: string,
    requiredConsents: ConsentType[],
  ): Promise<{
    allowed: boolean;
    missingConsents: ConsentType[];
  }> {
    const missingConsents: ConsentType[] = [];

    for (const type of requiredConsents) {
      const hasConsent = await this.hasValidConsent(userId, type);
      if (!hasConsent) {
        missingConsents.push(type);
      }
    }

    return {
      allowed: missingConsents.length === 0,
      missingConsents,
    };
  }

  /**
   * Get consent statistics
   */
  async getConsentStatistics(): Promise<{
    totalUsers: number;
    consentRates: Map<ConsentType, number>;
    withdrawalRates: Map<ConsentType, number>;
  }> {
    // Get all users with consent records
    const allUsers = await this.userConsentRepository
      .createQueryBuilder('consent')
      .select('DISTINCT consent.userId')
      .getRawMany();

    const totalUsers = allUsers.length;

    const consentRates = new Map<ConsentType, number>();
    const withdrawalRates = new Map<ConsentType, number>();

    for (const type of Object.values(ConsentType)) {
      // Calculate consent rate
      const granted = await this.userConsentRepository.count({
        where: {
          type,
          status: ConsentStatus.GRANTED,
        },
      });
      consentRates.set(type as ConsentType, (granted / totalUsers) * 100);

      // Calculate withdrawal rate
      const withdrawn = await this.userConsentRepository.count({
        where: {
          type,
          status: ConsentStatus.WITHDRAWN,
        },
      });
      withdrawalRates.set(type as ConsentType, (withdrawn / totalUsers) * 100);
    }

    return {
      totalUsers,
      consentRates,
      withdrawalRates,
    };
  }

  // Private helper methods

  private initializeConsentVersions(): Map<string, ConsentVersion> {
    const versions = new Map<string, ConsentVersion>();

    versions.set('1.0', {
      version: '1.0',
      effectiveDate: new Date('2024-01-01'),
      changes: ['Initial consent version'],
      requiresReConsent: false,
    });

    versions.set('2.0', {
      version: '2.0',
      effectiveDate: new Date('2024-06-01'),
      changes: [
        'Added third-party data sharing consent',
        'Updated analytics consent scope',
        'Clarified marketing consent options',
      ],
      requiresReConsent: true,
    });

    return versions;
  }

  private getCurrentConsentVersion(): ConsentVersion {
    const versions = Array.from(this.consentVersions.values()).sort(
      (a, b) => b.effectiveDate.getTime() - a.effectiveDate.getTime(),
    );

    const now = new Date();
    for (const version of versions) {
      if (version.effectiveDate <= now) {
        return version;
      }
    }

    return versions[versions.length - 1];
  }

  private determineLegalBasis(type: ConsentType): LegalBasis {
    // Map consent types to legal basis
    const legalBasisMap: Record<ConsentType, LegalBasis> = {
      [ConsentType.MARKETING]: LegalBasis.CONSENT,
      [ConsentType.ANALYTICS]: LegalBasis.LEGITIMATE_INTERESTS,
      [ConsentType.PERSONALIZATION]: LegalBasis.CONSENT,
      [ConsentType.THIRD_PARTY_SHARING]: LegalBasis.CONSENT,
      [ConsentType.COOKIES_FUNCTIONAL]: LegalBasis.LEGITIMATE_INTERESTS,
      [ConsentType.COOKIES_ANALYTICS]: LegalBasis.CONSENT,
      [ConsentType.COOKIES_MARKETING]: LegalBasis.CONSENT,
      [ConsentType.DATA_PROCESSING]: LegalBasis.CONTRACT,
      [ConsentType.COMMUNICATION_EMAIL]: LegalBasis.CONSENT,
      [ConsentType.COMMUNICATION_SMS]: LegalBasis.CONSENT,
      [ConsentType.COMMUNICATION_PHONE]: LegalBasis.CONSENT,
      [ConsentType.RESEARCH]: LegalBasis.CONSENT,
      [ConsentType.PRODUCT_UPDATES]: LegalBasis.LEGITIMATE_INTERESTS,
    };

    return legalBasisMap[type] || LegalBasis.CONSENT;
  }

  private getConsentPurpose(type: ConsentType): string {
    const purposeMap: Record<ConsentType, string> = {
      [ConsentType.MARKETING]:
        'To send you marketing communications about our products and services',
      [ConsentType.ANALYTICS]:
        'To analyze usage patterns and improve our services',
      [ConsentType.PERSONALIZATION]:
        'To personalize your experience based on your preferences',
      [ConsentType.THIRD_PARTY_SHARING]:
        'To share your data with trusted third-party partners',
      [ConsentType.COOKIES_FUNCTIONAL]:
        'To enable essential website functionality',
      [ConsentType.COOKIES_ANALYTICS]: 'To understand how you use our website',
      [ConsentType.COOKIES_MARKETING]: 'To show you relevant advertisements',
      [ConsentType.DATA_PROCESSING]:
        'To process your data as necessary to provide our services',
      [ConsentType.COMMUNICATION_EMAIL]: 'To communicate with you via email',
      [ConsentType.COMMUNICATION_SMS]: 'To send you text messages',
      [ConsentType.COMMUNICATION_PHONE]: 'To contact you by phone',
      [ConsentType.RESEARCH]:
        'To include your data in research and development activities',
      [ConsentType.PRODUCT_UPDATES]:
        'To inform you about product updates and new features',
    };

    return purposeMap[type] || 'To process your data for the specified purpose';
  }

  private async updateConsentCache(
    userId: string,
    type: ConsentType,
    granted: boolean,
  ): Promise<void> {
    // Update consent cache for fast lookups
    const cacheKey = `consent:${userId}:${type}`;
    // This would update Redis or other cache

    this.logger.debug(`Updated consent cache: ${cacheKey} = ${granted}`);
  }

  private async processConsentWithdrawal(
    userId: string,
    types: ConsentType[],
  ): Promise<void> {
    // Process implications of consent withdrawal
    for (const type of types) {
      switch (type) {
        case ConsentType.MARKETING:
          // Unsubscribe from marketing lists
          this.eventEmitter.emit('marketing.unsubscribe', { userId });
          break;

        case ConsentType.ANALYTICS:
          // Stop tracking analytics
          this.eventEmitter.emit('analytics.disable', { userId });
          break;

        case ConsentType.THIRD_PARTY_SHARING:
          // Request data deletion from third parties
          this.eventEmitter.emit('third-party.delete-request', { userId });
          break;

        case ConsentType.COOKIES_MARKETING:
          // Clear marketing cookies
          this.eventEmitter.emit('cookies.clear-marketing', { userId });
          break;

        default:
          // Generic consent withdrawal processing
          this.eventEmitter.emit('consent.process-withdrawal', {
            userId,
            type,
          });
      }
    }
  }
}
