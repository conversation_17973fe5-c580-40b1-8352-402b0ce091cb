import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';

import { PrivacyRequest } from '../entities/privacy-request.entity';
import { DataAnonymizationService } from './data-anonymization.service';
import { DataExportService } from './data-export.service';
import { DataDeletionService } from './data-deletion.service';
import { ConsentManagementService } from './consent-management.service';
import { AuditService } from './audit.service';

export enum PrivacyRequestType {
  DATA_ACCESS = 'data_access',
  DATA_EXPORT = 'data_export',
  DATA_DELETION = 'data_deletion',
  DATA_CORRECTION = 'data_correction',
  DATA_PORTABILITY = 'data_portability',
  CONSENT_WITHDRAWAL = 'consent_withdrawal',
  PROCESSING_RESTRICTION = 'processing_restriction',
}

export enum PrivacyRequestStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Injectable()
export class PrivacyService {
  private readonly logger = new Logger(PrivacyService.name);

  constructor(
    @InjectRepository(PrivacyRequest)
    private readonly privacyRequestRepository: Repository<PrivacyRequest>,
    @InjectQueue('data-export') private readonly exportQueue: Queue,
    @InjectQueue('data-deletion') private readonly deletionQueue: Queue,
    @InjectQueue('data-anonymization')
    private readonly anonymizationQueue: Queue,
    private readonly dataAnonymizationService: DataAnonymizationService,
    private readonly dataExportService: DataExportService,
    private readonly dataDeletionService: DataDeletionService,
    private readonly consentManagementService: ConsentManagementService,
    private readonly auditService: AuditService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Create a privacy request (GDPR Article 15-22)
   */
  async createPrivacyRequest(
    userId: string,
    type: PrivacyRequestType,
    details: any,
    requestedBy: string,
  ): Promise<PrivacyRequest> {
    // Verify user identity and authorization
    await this.verifyUserIdentity(userId, requestedBy);

    // Create request record
    const request = this.privacyRequestRepository.create({
      userId,
      type,
      status: PrivacyRequestStatus.PENDING,
      details,
      requestedBy,
      requestedAt: new Date(),
    });

    await this.privacyRequestRepository.save(request);

    // Audit the request
    await this.auditService.logPrivacyRequest({
      requestId: request.id,
      userId,
      type,
      action: 'privacy_request_created',
      performedBy: requestedBy,
    });

    // Process the request asynchronously
    await this.processPrivacyRequest(request);

    // Emit event
    this.eventEmitter.emit('privacy.request.created', {
      request,
      timestamp: new Date(),
    });

    return request;
  }

  /**
   * Process a privacy request
   */
  private async processPrivacyRequest(request: PrivacyRequest): Promise<void> {
    try {
      switch (request.type) {
        case PrivacyRequestType.DATA_ACCESS:
        case PrivacyRequestType.DATA_EXPORT:
        case PrivacyRequestType.DATA_PORTABILITY:
          await this.exportQueue.add('export-user-data', {
            requestId: request.id,
            userId: request.userId,
            format: request.details.format || 'json',
          });
          break;

        case PrivacyRequestType.DATA_DELETION:
          await this.deletionQueue.add('delete-user-data', {
            requestId: request.id,
            userId: request.userId,
            hardDelete: request.details.hardDelete || false,
          });
          break;

        case PrivacyRequestType.DATA_CORRECTION:
          await this.processCorrectionRequest(request);
          break;

        case PrivacyRequestType.CONSENT_WITHDRAWAL:
          await this.processConsentWithdrawal(request);
          break;

        case PrivacyRequestType.PROCESSING_RESTRICTION:
          await this.processRestrictionRequest(request);
          break;
      }
    } catch (error) {
      this.logger.error(
        `Failed to process privacy request ${request.id}`,
        error,
      );
      await this.updateRequestStatus(request.id, PrivacyRequestStatus.FAILED, {
        error: error.message,
      });
    }
  }

  /**
   * Get user's personal data (GDPR Article 15 - Right of Access)
   */
  async getUserData(userId: string, requestedBy: string): Promise<any> {
    await this.verifyUserIdentity(userId, requestedBy);

    // Audit data access
    await this.auditService.logDataAccess({
      userId,
      accessedBy: requestedBy,
      dataCategories: ['all'],
      purpose: 'user_request',
    });

    // Collect data from all sources
    const userData = await this.dataExportService.collectUserData(userId);

    // Anonymize sensitive data if necessary
    const anonymizedData =
      await this.dataAnonymizationService.anonymizeSensitiveFields(userData, [
        'internalIds',
        'technicalMetadata',
      ]);

    return anonymizedData;
  }

  /**
   * Export user data (GDPR Article 20 - Right to Data Portability)
   */
  async exportUserData(
    userId: string,
    format: 'json' | 'csv' | 'xml' = 'json',
    requestedBy: string,
  ): Promise<{ exportId: string; status: string }> {
    const request = await this.createPrivacyRequest(
      userId,
      PrivacyRequestType.DATA_EXPORT,
      { format },
      requestedBy,
    );

    return {
      exportId: request.id,
      status: request.status,
    };
  }

  /**
   * Delete user data (GDPR Article 17 - Right to Erasure)
   */
  async deleteUserData(
    userId: string,
    reason: string,
    requestedBy: string,
    options: {
      hardDelete?: boolean;
      retainForLegal?: boolean;
      anonymizeInstead?: boolean;
    } = {},
  ): Promise<{ deletionId: string; status: string }> {
    // Check if deletion is allowed
    const canDelete = await this.canDeleteUserData(userId);
    if (!canDelete.allowed) {
      throw new Error(`Data deletion not allowed: ${canDelete.reason}`);
    }

    const request = await this.createPrivacyRequest(
      userId,
      PrivacyRequestType.DATA_DELETION,
      { reason, ...options },
      requestedBy,
    );

    return {
      deletionId: request.id,
      status: request.status,
    };
  }

  /**
   * Anonymize user data (Alternative to deletion)
   */
  async anonymizeUserData(
    userId: string,
    requestedBy: string,
  ): Promise<{ anonymizationId: string; status: string }> {
    await this.verifyUserIdentity(userId, requestedBy);

    const jobId = await this.anonymizationQueue.add('anonymize-user-data', {
      userId,
      requestedBy,
      timestamp: new Date(),
    });

    await this.auditService.logDataAnonymization({
      userId,
      performedBy: requestedBy,
      jobId: jobId.id,
    });

    return {
      anonymizationId: jobId.id as string,
      status: 'processing',
    };
  }

  /**
   * Update user data (GDPR Article 16 - Right to Rectification)
   */
  async updateUserData(
    userId: string,
    updates: Record<string, any>,
    requestedBy: string,
  ): Promise<void> {
    await this.verifyUserIdentity(userId, requestedBy);

    // Validate updates
    const validatedUpdates = await this.validateDataUpdates(updates);

    // Apply updates across all systems
    await this.dataExportService.updateUserData(userId, validatedUpdates);

    // Audit the changes
    await this.auditService.logDataModification({
      userId,
      modifiedBy: requestedBy,
      changes: validatedUpdates,
      timestamp: new Date(),
    });

    // Emit event
    this.eventEmitter.emit('privacy.data.updated', {
      userId,
      updates: validatedUpdates,
      requestedBy,
    });
  }

  /**
   * Restrict processing (GDPR Article 18 - Right to Restriction)
   */
  async restrictProcessing(
    userId: string,
    categories: string[],
    reason: string,
    requestedBy: string,
  ): Promise<void> {
    await this.verifyUserIdentity(userId, requestedBy);

    const request = await this.createPrivacyRequest(
      userId,
      PrivacyRequestType.PROCESSING_RESTRICTION,
      { categories, reason },
      requestedBy,
    );

    // Apply restrictions immediately
    await this.applyProcessingRestrictions(userId, categories);

    await this.updateRequestStatus(request.id, PrivacyRequestStatus.COMPLETED);
  }

  /**
   * Get privacy request status
   */
  async getRequestStatus(requestId: string): Promise<PrivacyRequest> {
    const request = await this.privacyRequestRepository.findOne({
      where: { id: requestId },
    });

    if (!request) {
      throw new Error('Privacy request not found');
    }

    return request;
  }

  /**
   * Get all privacy requests for a user
   */
  async getUserRequests(userId: string): Promise<PrivacyRequest[]> {
    return this.privacyRequestRepository.find({
      where: { userId },
      order: { requestedAt: 'DESC' },
    });
  }

  /**
   * Cancel a privacy request
   */
  async cancelRequest(
    requestId: string,
    reason: string,
    cancelledBy: string,
  ): Promise<void> {
    const request = await this.getRequestStatus(requestId);

    if (request.status !== PrivacyRequestStatus.PENDING) {
      throw new Error('Can only cancel pending requests');
    }

    await this.updateRequestStatus(requestId, PrivacyRequestStatus.CANCELLED, {
      cancelReason: reason,
      cancelledBy,
      cancelledAt: new Date(),
    });

    // Cancel any queued jobs
    switch (request.type) {
      case PrivacyRequestType.DATA_EXPORT:
        await this.exportQueue.removeJobs(requestId);
        break;
      case PrivacyRequestType.DATA_DELETION:
        await this.deletionQueue.removeJobs(requestId);
        break;
    }
  }

  // Helper methods

  private async verifyUserIdentity(
    userId: string,
    requestedBy: string,
  ): Promise<void> {
    // Implement identity verification logic
    // This could include:
    // - Checking if requestedBy === userId (user requesting their own data)
    // - Verifying legal representative status
    // - Checking admin permissions
    // - Multi-factor authentication verification

    if (requestedBy !== userId) {
      // Check if requestedBy has legal authority
      const hasAuthority = await this.checkLegalAuthority(requestedBy, userId);
      if (!hasAuthority) {
        throw new Error('Unauthorized privacy request');
      }
    }
  }

  private async checkLegalAuthority(
    requestedBy: string,
    userId: string,
  ): Promise<boolean> {
    // Check if requestedBy is:
    // - Legal guardian
    // - Power of attorney
    // - Company DPO (Data Protection Officer)
    // - Authorized admin

    // Placeholder implementation
    return false;
  }

  private async canDeleteUserData(userId: string): Promise<{
    allowed: boolean;
    reason?: string;
  }> {
    // Check legal obligations to retain data
    const legalHolds = await this.checkLegalHolds(userId);
    if (legalHolds.length > 0) {
      return {
        allowed: false,
        reason: `Legal hold: ${legalHolds.join(', ')}`,
      };
    }

    // Check active contracts
    const activeContracts = await this.checkActiveContracts(userId);
    if (activeContracts.length > 0) {
      return {
        allowed: false,
        reason: 'Active contracts require data retention',
      };
    }

    // Check pending transactions
    const pendingTransactions = await this.checkPendingTransactions(userId);
    if (pendingTransactions.length > 0) {
      return {
        allowed: false,
        reason: 'Pending transactions must be completed',
      };
    }

    return { allowed: true };
  }

  private async checkLegalHolds(userId: string): Promise<string[]> {
    // Check for legal obligations to retain data
    return [];
  }

  private async checkActiveContracts(userId: string): Promise<string[]> {
    // Check for active contracts requiring data
    return [];
  }

  private async checkPendingTransactions(userId: string): Promise<string[]> {
    // Check for pending transactions
    return [];
  }

  private async validateDataUpdates(
    updates: Record<string, any>,
  ): Promise<Record<string, any>> {
    // Validate and sanitize update data
    const validated: Record<string, any> = {};

    for (const [key, value] of Object.entries(updates)) {
      // Only allow updates to certain fields
      if (this.isUpdatableField(key)) {
        validated[key] = this.sanitizeValue(value);
      }
    }

    return validated;
  }

  private isUpdatableField(field: string): boolean {
    const updatableFields = [
      'name',
      'email',
      'phone',
      'address',
      'preferences',
      'profile',
    ];
    return updatableFields.includes(field);
  }

  private sanitizeValue(value: any): any {
    // Implement sanitization logic
    if (typeof value === 'string') {
      return value.trim();
    }
    return value;
  }

  private async processCorrectionRequest(
    request: PrivacyRequest,
  ): Promise<void> {
    await this.updateRequestStatus(request.id, PrivacyRequestStatus.PROCESSING);

    try {
      await this.updateUserData(
        request.userId,
        request.details.corrections,
        request.requestedBy,
      );

      await this.updateRequestStatus(
        request.id,
        PrivacyRequestStatus.COMPLETED,
      );
    } catch (error) {
      await this.updateRequestStatus(request.id, PrivacyRequestStatus.FAILED, {
        error: error.message,
      });
      throw error;
    }
  }

  private async processConsentWithdrawal(
    request: PrivacyRequest,
  ): Promise<void> {
    await this.updateRequestStatus(request.id, PrivacyRequestStatus.PROCESSING);

    try {
      await this.consentManagementService.withdrawConsent(
        request.userId,
        request.details.consentTypes,
        request.requestedBy,
      );

      await this.updateRequestStatus(
        request.id,
        PrivacyRequestStatus.COMPLETED,
      );
    } catch (error) {
      await this.updateRequestStatus(request.id, PrivacyRequestStatus.FAILED, {
        error: error.message,
      });
      throw error;
    }
  }

  private async processRestrictionRequest(
    request: PrivacyRequest,
  ): Promise<void> {
    await this.applyProcessingRestrictions(
      request.userId,
      request.details.categories,
    );
  }

  private async applyProcessingRestrictions(
    userId: string,
    categories: string[],
  ): Promise<void> {
    // Apply restrictions across all systems
    this.eventEmitter.emit('privacy.processing.restricted', {
      userId,
      categories,
      timestamp: new Date(),
    });
  }

  private async updateRequestStatus(
    requestId: string,
    status: PrivacyRequestStatus,
    metadata?: any,
  ): Promise<void> {
    await this.privacyRequestRepository.update(requestId, {
      status,
      metadata: metadata || {},
      updatedAt: new Date(),
      completedAt:
        status === PrivacyRequestStatus.COMPLETED ? new Date() : null,
    });

    this.eventEmitter.emit('privacy.request.status.updated', {
      requestId,
      status,
      metadata,
    });
  }
}
