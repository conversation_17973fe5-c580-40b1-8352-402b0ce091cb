import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as archiver from 'archiver';
import { parse as csvParse } from 'csv-parse';
import { stringify as csvStringify } from 'csv-stringify';
import * as xml2js from 'xml2js';

interface DataSource {
  name: string;
  endpoint: string;
  dataCategory: string;
  requiredScopes?: string[];
}

interface ExportFormat {
  type: 'json' | 'csv' | 'xml' | 'pdf';
  options?: any;
}

interface ExportResult {
  exportId: string;
  format: string;
  size: number;
  checksum: string;
  createdAt: Date;
  expiresAt: Date;
  downloadUrl?: string;
  filePath?: string;
}

@Injectable()
export class DataExportService {
  private readonly logger = new Logger(DataExportService.name);
  private readonly dataSources: DataSource[];
  private readonly exportDirectory: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.dataSources = this.getConfiguredDataSources();
    this.exportDirectory = this.configService.get(
      'EXPORT_DIRECTORY',
      './exports',
    );
    this.ensureExportDirectory();
  }

  /**
   * Collect all user data from various sources
   */
  async collectUserData(userId: string): Promise<any> {
    this.logger.log(`Collecting data for user ${userId}`);

    const userData = {
      userId,
      exportDate: new Date(),
      dataCategories: {},
    };

    // Collect data from each source in parallel
    const dataPromises = this.dataSources.map(async (source) => {
      try {
        const data = await this.fetchDataFromSource(source, userId);
        return { category: source.dataCategory, data };
      } catch (error) {
        this.logger.error(`Failed to fetch data from ${source.name}`, error);
        return { category: source.dataCategory, error: error.message };
      }
    });

    const results = await Promise.allSettled(dataPromises);

    // Organize data by category
    for (const result of results) {
      if (result.status === 'fulfilled' && result.value.data) {
        userData.dataCategories[result.value.category] = result.value.data;
      }
    }

    // Add metadata
    userData['metadata'] = {
      exportVersion: '1.0',
      totalCategories: Object.keys(userData.dataCategories).length,
      exportFormat: 'structured',
    };

    return userData;
  }

  /**
   * Export user data in specified format
   */
  async exportUserData(
    userId: string,
    format: ExportFormat = { type: 'json' },
  ): Promise<ExportResult> {
    const userData = await this.collectUserData(userId);
    const exportId = this.generateExportId(userId);

    let result: ExportResult;

    switch (format.type) {
      case 'json':
        result = await this.exportAsJSON(exportId, userData);
        break;
      case 'csv':
        result = await this.exportAsCSV(exportId, userData, format.options);
        break;
      case 'xml':
        result = await this.exportAsXML(exportId, userData);
        break;
      case 'pdf':
        result = await this.exportAsPDF(exportId, userData);
        break;
      default:
        throw new Error(`Unsupported export format: ${format.type}`);
    }

    // Create archive with all data
    const archivePath = await this.createDataArchive(exportId, result.filePath);

    return {
      ...result,
      filePath: archivePath,
      downloadUrl: this.generateDownloadUrl(exportId),
    };
  }

  /**
   * Export data as JSON
   */
  private async exportAsJSON(
    exportId: string,
    data: any,
  ): Promise<ExportResult> {
    const filePath = path.join(this.exportDirectory, `${exportId}.json`);
    const jsonData = JSON.stringify(data, null, 2);

    await fs.writeFile(filePath, jsonData, 'utf8');

    const stats = await fs.stat(filePath);
    const checksum = await this.calculateChecksum(filePath);

    return {
      exportId,
      format: 'json',
      size: stats.size,
      checksum,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      filePath,
    };
  }

  /**
   * Export data as CSV
   */
  private async exportAsCSV(
    exportId: string,
    data: any,
    options?: any,
  ): Promise<ExportResult> {
    const csvFiles: string[] = [];

    // Export each data category as separate CSV
    for (const [category, categoryData] of Object.entries(
      data.dataCategories,
    )) {
      if (Array.isArray(categoryData)) {
        const csvPath = path.join(
          this.exportDirectory,
          `${exportId}_${category}.csv`,
        );
        const csvData = await this.convertToCSV(categoryData, options);
        await fs.writeFile(csvPath, csvData, 'utf8');
        csvFiles.push(csvPath);
      }
    }

    // Create summary CSV
    const summaryPath = path.join(
      this.exportDirectory,
      `${exportId}_summary.csv`,
    );
    const summaryData = await this.createSummaryCSV(data);
    await fs.writeFile(summaryPath, summaryData, 'utf8');
    csvFiles.push(summaryPath);

    // Create zip archive of all CSV files
    const archivePath = path.join(this.exportDirectory, `${exportId}.zip`);
    await this.createZipArchive(csvFiles, archivePath);

    const stats = await fs.stat(archivePath);
    const checksum = await this.calculateChecksum(archivePath);

    return {
      exportId,
      format: 'csv',
      size: stats.size,
      checksum,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      filePath: archivePath,
    };
  }

  /**
   * Export data as XML
   */
  private async exportAsXML(
    exportId: string,
    data: any,
  ): Promise<ExportResult> {
    const builder = new xml2js.Builder({
      rootName: 'UserDataExport',
      xmldec: { version: '1.0', encoding: 'UTF-8' },
    });

    const xmlData = builder.buildObject(data);
    const filePath = path.join(this.exportDirectory, `${exportId}.xml`);

    await fs.writeFile(filePath, xmlData, 'utf8');

    const stats = await fs.stat(filePath);
    const checksum = await this.calculateChecksum(filePath);

    return {
      exportId,
      format: 'xml',
      size: stats.size,
      checksum,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      filePath,
    };
  }

  /**
   * Export data as PDF (placeholder - would need PDF library)
   */
  private async exportAsPDF(
    exportId: string,
    data: any,
  ): Promise<ExportResult> {
    // In a real implementation, you would use a PDF library like pdfkit or puppeteer
    this.logger.warn(
      'PDF export not fully implemented - creating JSON instead',
    );
    return this.exportAsJSON(exportId, data);
  }

  /**
   * Update user data across all systems
   */
  async updateUserData(
    userId: string,
    updates: Record<string, any>,
  ): Promise<void> {
    this.logger.log(`Updating data for user ${userId}`);

    const updatePromises = this.dataSources.map(async (source) => {
      if (source.dataCategory in updates) {
        try {
          await this.updateDataInSource(
            source,
            userId,
            updates[source.dataCategory],
          );
          this.logger.log(`Updated ${source.dataCategory} in ${source.name}`);
        } catch (error) {
          this.logger.error(`Failed to update ${source.name}`, error);
          throw error;
        }
      }
    });

    await Promise.all(updatePromises);
  }

  /**
   * Get data export status
   */
  async getExportStatus(exportId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'expired';
    progress?: number;
    result?: ExportResult;
  }> {
    try {
      const filePath = path.join(this.exportDirectory, `${exportId}.*`);
      const files = await fs.readdir(this.exportDirectory);
      const exportFile = files.find((file) => file.startsWith(exportId));

      if (!exportFile) {
        return { status: 'pending' };
      }

      const stats = await fs.stat(path.join(this.exportDirectory, exportFile));
      const createdAt = stats.birthtime;
      const expiresAt = new Date(
        createdAt.getTime() + 30 * 24 * 60 * 60 * 1000,
      );

      if (new Date() > expiresAt) {
        return { status: 'expired' };
      }

      return {
        status: 'completed',
        result: {
          exportId,
          format: path.extname(exportFile).substring(1),
          size: stats.size,
          checksum: await this.calculateChecksum(
            path.join(this.exportDirectory, exportFile),
          ),
          createdAt,
          expiresAt,
          downloadUrl: this.generateDownloadUrl(exportId),
        },
      };
    } catch (error) {
      return { status: 'processing' };
    }
  }

  /**
   * Clean up expired exports
   */
  async cleanupExpiredExports(): Promise<void> {
    this.logger.log('Cleaning up expired exports');

    const files = await fs.readdir(this.exportDirectory);
    const now = new Date();

    for (const file of files) {
      const filePath = path.join(this.exportDirectory, file);
      const stats = await fs.stat(filePath);
      const expiresAt = new Date(
        stats.birthtime.getTime() + 30 * 24 * 60 * 60 * 1000,
      );

      if (now > expiresAt) {
        await fs.unlink(filePath);
        this.logger.log(`Deleted expired export: ${file}`);
      }
    }
  }

  // Private helper methods

  private getConfiguredDataSources(): DataSource[] {
    return [
      {
        name: 'E-Connect',
        endpoint: 'http://localhost:3001/api/user-data',
        dataCategory: 'emails',
        requiredScopes: ['email.read'],
      },
      {
        name: 'Lighthouse',
        endpoint: 'http://localhost:3002/api/user-data',
        dataCategory: 'research',
        requiredScopes: ['research.read'],
      },
      {
        name: 'Training',
        endpoint: 'http://localhost:3003/api/user-data',
        dataCategory: 'training',
        requiredScopes: ['training.read'],
      },
      {
        name: 'Vendors',
        endpoint: 'http://localhost:3004/api/user-data',
        dataCategory: 'vendors',
        requiredScopes: ['vendors.read'],
      },
      {
        name: 'Wins',
        endpoint: 'http://localhost:3005/api/user-data',
        dataCategory: 'wins',
        requiredScopes: ['wins.read'],
      },
      {
        name: 'AMNA',
        endpoint: 'http://localhost:3000/api/user-data',
        dataCategory: 'chat',
        requiredScopes: ['chat.read'],
      },
    ];
  }

  private async fetchDataFromSource(
    source: DataSource,
    userId: string,
  ): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${source.endpoint}/${userId}`, {
          headers: {
            'X-User-Id': userId,
            'X-Data-Category': source.dataCategory,
          },
          timeout: 30000,
        }),
      );

      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // No data for user in this source
      }
      throw error;
    }
  }

  private async updateDataInSource(
    source: DataSource,
    userId: string,
    updates: any,
  ): Promise<void> {
    await firstValueFrom(
      this.httpService.put(`${source.endpoint}/${userId}`, updates, {
        headers: {
          'X-User-Id': userId,
          'X-Data-Category': source.dataCategory,
        },
        timeout: 30000,
      }),
    );
  }

  private async ensureExportDirectory(): Promise<void> {
    try {
      await fs.access(this.exportDirectory);
    } catch {
      await fs.mkdir(this.exportDirectory, { recursive: true });
    }
  }

  private generateExportId(userId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    return `export_${userId}_${timestamp}_${random}`;
  }

  private generateDownloadUrl(exportId: string): string {
    const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3000');
    return `${baseUrl}/api/privacy/exports/${exportId}/download`;
  }

  private async calculateChecksum(filePath: string): Promise<string> {
    const crypto = await import('crypto');
    const fileBuffer = await fs.readFile(filePath);
    const hash = crypto.createHash('sha256');
    hash.update(fileBuffer);
    return hash.digest('hex');
  }

  private async convertToCSV(data: any[], options?: any): Promise<string> {
    return new Promise((resolve, reject) => {
      csvStringify(
        data,
        {
          header: true,
          ...options,
        },
        (err, output) => {
          if (err) reject(err);
          else resolve(output);
        },
      );
    });
  }

  private async createSummaryCSV(data: any): Promise<string> {
    const summary = [
      ['Category', 'Record Count', 'Export Date'],
      ...Object.entries(data.dataCategories).map(([category, categoryData]) => [
        category,
        Array.isArray(categoryData) ? categoryData.length : 1,
        data.exportDate,
      ]),
    ];

    return this.convertToCSV(summary);
  }

  private async createZipArchive(
    files: string[],
    outputPath: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(outputPath);
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => resolve());
      archive.on('error', (err) => reject(err));

      archive.pipe(output);

      for (const file of files) {
        archive.file(file, { name: path.basename(file) });
      }

      archive.finalize();
    });
  }

  private async createDataArchive(
    exportId: string,
    dataPath: string,
  ): Promise<string> {
    const archivePath = path.join(
      this.exportDirectory,
      `${exportId}_complete.zip`,
    );
    const archive = archiver('zip', { zlib: { level: 9 } });
    const output = fs.createWriteStream(archivePath);

    return new Promise((resolve, reject) => {
      output.on('close', () => resolve(archivePath));
      archive.on('error', reject);

      archive.pipe(output);

      // Add main data file
      archive.file(dataPath, { name: path.basename(dataPath) });

      // Add metadata
      const metadata = {
        exportId,
        exportDate: new Date(),
        dataProtectionNotice:
          'This export contains personal data protected under GDPR',
        retentionPeriod: '30 days',
        checksum: this.calculateChecksum(dataPath),
      };

      archive.append(JSON.stringify(metadata, null, 2), {
        name: 'metadata.json',
      });

      // Add README
      const readme = `
# Personal Data Export

This archive contains your personal data as requested under GDPR Article 20 (Right to Data Portability).

## Contents
- Your personal data in the requested format
- Metadata about the export
- This README file

## Data Categories Included
- Emails (E-Connect)
- Research (Lighthouse)
- Training records
- Vendor interactions
- Weekly wins
- Chat conversations (AMNA)

## Important Notes
- This export expires in 30 days
- Keep this data secure as it contains personal information
- For questions, contact <EMAIL>

Generated on: ${new Date().toISOString()}
`;

      archive.append(readme, { name: 'README.txt' });

      archive.finalize();
    });
  }
}
