import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { IntegrationController } from './integration.controller';
import { AMNAIntegrationService } from '../services/core/amna-integration.service';
import { EventBusService } from '../services/core/event-bus.service';
import { DataFederationService } from '../services/core/data-federation.service';
import { IntegrationTracingService } from '../tracing/integration-tracing.service';
import { TracingInterceptor } from '../tracing/tracing.interceptor';
import { Trace } from '../tracing/tracing.decorator';

/**
 * Example of Integration Controller with OpenTelemetry tracing
 * This shows how to add tracing to existing controllers
 */
@ApiTags('integration-traced')
@Controller('api/integration-traced')
@UseGuards(AuthGuard('jwt'))
@UseInterceptors(TracingInterceptor)
export class IntegrationControllerTraced extends IntegrationController {
  constructor(
    amnaIntegrationService: AMNAIntegrationService,
    eventBusService: EventBusService,
    dataFederationService: DataFederationService,
    private readonly integrationTracingService: IntegrationTracingService,
  ) {
    super(amnaIntegrationService, eventBusService, dataFederationService);
  }

  @Post('activity')
  @ApiOperation({ summary: 'Create integration activity with tracing' })
  @ApiBearerAuth()
  @Trace({ recordArgs: true, recordResult: true })
  async createActivity(@Body() activityDto: any) {
    return this.integrationTracingService.traceActivityProcessing(
      activityDto.id || 'new',
      activityDto.source,
      activityDto.type,
      () => super.createActivity(activityDto),
    );
  }

  @Post('sync/:userId')
  @ApiOperation({ summary: 'Sync user data across applications with tracing' })
  @ApiBearerAuth()
  async syncUserData(@Param('userId') userId: string) {
    const applications = [
      'e-connect',
      'lighthouse',
      'training',
      'vendors',
      'wins',
    ];

    return this.integrationTracingService.traceDataFederation(
      userId,
      applications,
      () => super.syncUserData(userId),
    );
  }

  @Get('federated/:userId')
  @ApiOperation({ summary: 'Get federated data with tracing' })
  @ApiBearerAuth()
  async getFederatedData(@Param('userId') userId: string) {
    return this.integrationTracingService.traceCrossAppFlow(
      'api',
      'all-applications',
      'federated-query',
      () => super.getFederatedData(userId),
    );
  }

  /**
   * Example of manual span creation for complex operations
   */
  @Post('complex-operation')
  @ApiOperation({ summary: 'Complex operation with detailed tracing' })
  @ApiBearerAuth()
  async complexOperation(@Body() data: any) {
    const traceContext =
      this.integrationTracingService.createAsyncTraceContext(
        'complex-operation',
      );

    // Step 1: Validate data
    await this.integrationTracingService.traceCrossAppFlow(
      'api',
      'validation',
      'validate-input',
      async () => {
        // Validation logic
        return { valid: true };
      },
    );

    // Step 2: Process in multiple applications
    const results = await Promise.all([
      this.processInEConnect(data, traceContext.headers),
      this.processInLighthouse(data, traceContext.headers),
      this.processInTraining(data, traceContext.headers),
    ]);

    // Step 3: Aggregate results
    return this.integrationTracingService.traceCrossAppFlow(
      'aggregator',
      'api',
      'aggregate-results',
      async () => {
        return {
          traceId: traceContext.traceId,
          results: results,
          timestamp: new Date(),
        };
      },
    );
  }

  private async processInEConnect(
    data: any,
    traceHeaders: Record<string, string>,
  ) {
    return this.integrationTracingService.continueAsyncTrace(
      traceHeaders,
      'process-e-connect',
      async () => {
        // E-Connect processing logic
        return { application: 'e-connect', processed: true };
      },
    );
  }

  private async processInLighthouse(
    data: any,
    traceHeaders: Record<string, string>,
  ) {
    return this.integrationTracingService.continueAsyncTrace(
      traceHeaders,
      'process-lighthouse',
      async () => {
        // Lighthouse processing logic
        return { application: 'lighthouse', processed: true };
      },
    );
  }

  private async processInTraining(
    data: any,
    traceHeaders: Record<string, string>,
  ) {
    return this.integrationTracingService.continueAsyncTrace(
      traceHeaders,
      'process-training',
      async () => {
        // Training processing logic
        return { application: 'training', processed: true };
      },
    );
  }
}
