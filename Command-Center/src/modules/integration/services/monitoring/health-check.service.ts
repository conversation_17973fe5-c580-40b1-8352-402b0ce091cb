import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CircuitBreakerService } from '../core/circuit-breaker.service';
import { EventBusService } from '../core/event-bus.service';
import { IntegrationActivityEvent } from '../../types/event-definitions';

export interface ServiceHealthStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastChecked: Date;
  responseTime?: number;
  errorRate?: number;
  circuitState?: 'closed' | 'open' | 'half-open';
  details?: Record<string, any>;
}

export interface SystemHealthReport {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: ServiceHealthStatus[];
  timestamp: Date;
  uptime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage?: number;
}

@Injectable()
export class HealthCheckService implements OnModuleInit {
  private readonly logger = new Logger(HealthCheckService.name);
  private readonly healthChecks = new Map<
    string,
    () => Promise<ServiceHealthStatus>
  >();
  private startTime: Date;
  private lastHealthReport: SystemHealthReport;

  constructor(
    private readonly eventBus: EventBusService,
    private readonly circuitBreaker: CircuitBreakerService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.startTime = new Date();
  }

  async onModuleInit() {
    this.logger.log('Initializing health check service');
    this.registerDefaultHealthChecks();
  }

  /**
   * Register a health check for a service
   */
  registerHealthCheck(
    serviceName: string,
    healthCheck: () => Promise<ServiceHealthStatus>,
  ) {
    this.healthChecks.set(serviceName, healthCheck);
    this.logger.debug(`Registered health check for ${serviceName}`);
  }

  /**
   * Perform health check for all registered services
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async performHealthChecks(): Promise<SystemHealthReport> {
    const services: ServiceHealthStatus[] = [];

    for (const [name, check] of this.healthChecks.entries()) {
      try {
        const startTime = Date.now();
        const status = await this.executeHealthCheck(name, check);
        status.responseTime = Date.now() - startTime;
        services.push(status);
      } catch (error) {
        this.logger.error(`Health check failed for ${name}:`, error);
        services.push({
          name,
          status: 'unhealthy',
          lastChecked: new Date(),
          details: { error: error.message },
        });
      }
    }

    const report = this.generateHealthReport(services);
    this.lastHealthReport = report;

    // Emit health status event
    await this.emitHealthEvent(report);

    return report;
  }

  /**
   * Get current health status
   */
  async getHealthStatus(): Promise<SystemHealthReport> {
    if (!this.lastHealthReport || this.isReportStale(this.lastHealthReport)) {
      return await this.performHealthChecks();
    }
    return this.lastHealthReport;
  }

  /**
   * Get health status for a specific service
   */
  async getServiceHealth(
    serviceName: string,
  ): Promise<ServiceHealthStatus | null> {
    const check = this.healthChecks.get(serviceName);
    if (!check) {
      return null;
    }

    try {
      return await this.executeHealthCheck(serviceName, check);
    } catch (error) {
      this.logger.error(`Failed to check health for ${serviceName}:`, error);
      return {
        name: serviceName,
        status: 'unhealthy',
        lastChecked: new Date(),
        details: { error: error.message },
      };
    }
  }

  /**
   * Execute a single health check with circuit breaker protection
   */
  private async executeHealthCheck(
    name: string,
    check: () => Promise<ServiceHealthStatus>,
  ): Promise<ServiceHealthStatus> {
    const circuitState = await this.circuitBreaker.getState(name);

    if (circuitState === 'open') {
      return {
        name,
        status: 'unhealthy',
        lastChecked: new Date(),
        circuitState,
        details: { reason: 'Circuit breaker is open' },
      };
    }

    try {
      const result = await check();
      result.circuitState = circuitState;
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate overall health report
   */
  private generateHealthReport(
    services: ServiceHealthStatus[],
  ): SystemHealthReport {
    const unhealthyCount = services.filter(
      (s) => s.status === 'unhealthy',
    ).length;
    const degradedCount = services.filter(
      (s) => s.status === 'degraded',
    ).length;

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (unhealthyCount > 0) {
      overall = 'unhealthy';
    } else if (degradedCount > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    const uptime = Date.now() - this.startTime.getTime();
    const memoryUsage = process.memoryUsage();

    return {
      overall,
      services,
      timestamp: new Date(),
      uptime,
      memoryUsage,
      cpuUsage: this.getCpuUsage(),
    };
  }

  /**
   * Register default health checks for core services
   */
  private registerDefaultHealthChecks() {
    // Database health check
    this.registerHealthCheck('database', async () => {
      try {
        // Simple query to check database connectivity
        const startTime = Date.now();
        // This would be replaced with actual database check
        const responseTime = Date.now() - startTime;

        return {
          name: 'database',
          status: responseTime < 100 ? 'healthy' : 'degraded',
          lastChecked: new Date(),
          responseTime,
          details: { connections: 'active' },
        };
      } catch (error) {
        return {
          name: 'database',
          status: 'unhealthy',
          lastChecked: new Date(),
          details: { error: error.message },
        };
      }
    });

    // Redis health check
    this.registerHealthCheck('redis', async () => {
      try {
        // This would be replaced with actual Redis ping
        return {
          name: 'redis',
          status: 'healthy',
          lastChecked: new Date(),
          details: { connected: true },
        };
      } catch (error) {
        return {
          name: 'redis',
          status: 'unhealthy',
          lastChecked: new Date(),
          details: { error: error.message },
        };
      }
    });

    // Event bus health check
    this.registerHealthCheck('event-bus', async () => {
      const activeConnections = await this.eventBus.getActiveConnections();
      const status = activeConnections >= 0 ? 'healthy' : 'unhealthy';

      return {
        name: 'event-bus',
        status,
        lastChecked: new Date(),
        details: { activeConnections },
      };
    });
  }

  /**
   * Emit health status event
   */
  private async emitHealthEvent(report: SystemHealthReport) {
    const event: IntegrationActivityEvent = {
      type: 'SYSTEM_HEALTH_CHECK',
      timestamp: new Date(),
      userId: 'system',
      source: 'health-check-service',
      data: {
        overall: report.overall,
        services: report.services.map((s) => ({
          name: s.name,
          status: s.status,
          responseTime: s.responseTime,
        })),
        uptime: report.uptime,
      },
    };

    this.eventEmitter.emit('integration.health', event);
  }

  /**
   * Check if health report is stale
   */
  private isReportStale(report: SystemHealthReport): boolean {
    const staleThreshold = 60000; // 1 minute
    return Date.now() - report.timestamp.getTime() > staleThreshold;
  }

  /**
   * Get CPU usage (simplified implementation)
   */
  private getCpuUsage(): number {
    // This is a simplified implementation
    // In production, you'd use proper CPU monitoring
    const usage = process.cpuUsage();
    return (usage.user + usage.system) / 1000000; // Convert to seconds
  }
}
