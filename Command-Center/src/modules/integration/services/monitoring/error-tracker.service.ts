import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CacheManagerService } from '../core/cache-manager.service';
import { MetricsService } from './metrics.service';
import { IntegrationLogs } from '../../entities/integration-logs.entity';

export interface ErrorContext {
  userId?: string;
  requestId?: string;
  service: string;
  operation?: string;
  metadata?: Record<string, any>;
}

export interface TrackedError {
  id: string;
  timestamp: Date;
  message: string;
  stack?: string;
  code?: string;
  level: 'error' | 'warning' | 'critical';
  context: ErrorContext;
  count: number;
  firstOccurrence: Date;
  lastOccurrence: Date;
  resolved: boolean;
  resolutionNotes?: string;
}

export interface ErrorPattern {
  pattern: RegExp;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  autoResolve?: boolean;
  alertThreshold?: number;
}

export interface ErrorReport {
  totalErrors: number;
  uniqueErrors: number;
  errorsByLevel: Record<string, number>;
  errorsByService: Record<string, number>;
  topErrors: TrackedError[];
  errorRate: number;
  trends: {
    hourly: number[];
    daily: number[];
  };
}

@Injectable()
export class ErrorTrackerService implements OnModuleInit {
  private readonly logger = new Logger(ErrorTrackerService.name);
  private readonly errorCache = new Map<string, TrackedError>();
  private readonly errorPatterns: ErrorPattern[] = [];
  private readonly recentErrors: TrackedError[] = [];
  private readonly maxRecentErrors = 1000;

  constructor(
    @InjectRepository(IntegrationLogs)
    private readonly logsRepository: Repository<IntegrationLogs>,
    private readonly cacheManager: CacheManagerService,
    private readonly metricsService: MetricsService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeErrorPatterns();
  }

  async onModuleInit() {
    this.logger.log('Initializing error tracker service');
    await this.loadPersistedErrors();
    this.setupGlobalErrorHandlers();
  }

  /**
   * Track an error
   */
  async trackError(
    error: Error | string,
    context: ErrorContext,
    level: 'error' | 'warning' | 'critical' = 'error',
  ): Promise<TrackedError> {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;
    const errorCode =
      typeof error === 'object' && 'code' in error ? error.code : undefined;

    // Generate error fingerprint
    const fingerprint = this.generateErrorFingerprint(errorMessage, context);

    // Check if error already exists
    let trackedError = this.errorCache.get(fingerprint);

    if (trackedError) {
      // Update existing error
      trackedError.count++;
      trackedError.lastOccurrence = new Date();

      // Check if error pattern matches for auto-resolution
      const pattern = this.matchErrorPattern(errorMessage);
      if (pattern?.autoResolve) {
        trackedError.resolved = true;
        trackedError.resolutionNotes = 'Auto-resolved by pattern match';
      }
    } else {
      // Create new tracked error
      trackedError = {
        id: this.generateErrorId(),
        timestamp: new Date(),
        message: errorMessage,
        stack: errorStack,
        code: errorCode,
        level,
        context,
        count: 1,
        firstOccurrence: new Date(),
        lastOccurrence: new Date(),
        resolved: false,
      };

      this.errorCache.set(fingerprint, trackedError);
    }

    // Add to recent errors
    this.addToRecentErrors(trackedError);

    // Persist error
    await this.persistError(trackedError);

    // Update metrics
    this.updateErrorMetrics(trackedError);

    // Check alert thresholds
    await this.checkAlertThresholds(trackedError);

    // Emit error event
    this.emitErrorEvent(trackedError);

    return trackedError;
  }

  /**
   * Track exception with additional context
   */
  async trackException(
    exception: any,
    context: ErrorContext,
  ): Promise<TrackedError> {
    const level = this.determineErrorLevel(exception);
    return this.trackError(exception, context, level);
  }

  /**
   * Get error by ID
   */
  async getError(errorId: string): Promise<TrackedError | null> {
    for (const error of this.errorCache.values()) {
      if (error.id === errorId) {
        return error;
      }
    }

    // Check persisted errors
    const persistedError = await this.cacheManager.get<TrackedError>(
      `error:${errorId}`,
    );
    return persistedError || null;
  }

  /**
   * Get error report
   */
  async getErrorReport(timeRange?: {
    start: Date;
    end: Date;
  }): Promise<ErrorReport> {
    const errors = Array.from(this.errorCache.values());
    const filteredErrors = timeRange
      ? errors.filter(
          (e) => e.timestamp >= timeRange.start && e.timestamp <= timeRange.end,
        )
      : errors;

    const uniqueErrors = new Set(filteredErrors.map((e) => e.message)).size;
    const totalErrors = filteredErrors.reduce((sum, e) => sum + e.count, 0);

    const errorsByLevel = this.groupErrorsByLevel(filteredErrors);
    const errorsByService = this.groupErrorsByService(filteredErrors);
    const topErrors = this.getTopErrors(filteredErrors, 10);
    const errorRate = this.calculateErrorRate(filteredErrors);
    const trends = await this.calculateErrorTrends();

    return {
      totalErrors,
      uniqueErrors,
      errorsByLevel,
      errorsByService,
      topErrors,
      errorRate,
      trends,
    };
  }

  /**
   * Resolve an error
   */
  async resolveError(
    errorId: string,
    resolutionNotes?: string,
  ): Promise<boolean> {
    const error = await this.getError(errorId);

    if (!error) {
      return false;
    }

    error.resolved = true;
    error.resolutionNotes = resolutionNotes;

    // Update in cache and persist
    await this.persistError(error);

    // Emit resolution event
    this.eventEmitter.emit('error.resolved', {
      errorId,
      resolutionNotes,
      timestamp: new Date(),
    });

    return true;
  }

  /**
   * Add error pattern for categorization
   */
  addErrorPattern(pattern: ErrorPattern) {
    this.errorPatterns.push(pattern);
    this.logger.debug(`Added error pattern for category: ${pattern.category}`);
  }

  /**
   * Clear resolved errors older than specified days
   */
  async clearResolvedErrors(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    let clearedCount = 0;

    for (const [fingerprint, error] of this.errorCache.entries()) {
      if (error.resolved && error.lastOccurrence < cutoffDate) {
        this.errorCache.delete(fingerprint);
        clearedCount++;
      }
    }

    this.logger.log(
      `Cleared ${clearedCount} resolved errors older than ${daysOld} days`,
    );
    return clearedCount;
  }

  /**
   * Get errors by service
   */
  async getErrorsByService(service: string): Promise<TrackedError[]> {
    return Array.from(this.errorCache.values())
      .filter((e) => e.context.service === service)
      .sort((a, b) => b.lastOccurrence.getTime() - a.lastOccurrence.getTime());
  }

  /**
   * Get unresolved errors
   */
  getUnresolvedErrors(): TrackedError[] {
    return Array.from(this.errorCache.values())
      .filter((e) => !e.resolved)
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Initialize common error patterns
   */
  private initializeErrorPatterns() {
    this.errorPatterns.push(
      {
        pattern: /ECONNREFUSED/,
        category: 'connection',
        severity: 'high',
        alertThreshold: 5,
      },
      {
        pattern: /ETIMEDOUT/,
        category: 'timeout',
        severity: 'medium',
        alertThreshold: 10,
      },
      {
        pattern: /Circuit breaker is open/,
        category: 'circuit_breaker',
        severity: 'high',
        alertThreshold: 1,
      },
      {
        pattern: /Out of memory/,
        category: 'memory',
        severity: 'critical',
        alertThreshold: 1,
      },
      {
        pattern: /Rate limit exceeded/,
        category: 'rate_limit',
        severity: 'low',
        autoResolve: true,
      },
      {
        pattern: /Invalid token/,
        category: 'authentication',
        severity: 'medium',
        alertThreshold: 20,
      },
    );
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers() {
    // Listen for unhandled errors
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception:', error);
      this.trackError(error, { service: 'global' }, 'critical');
    });

    process.on('unhandledRejection', (reason: any) => {
      this.logger.error('Unhandled rejection:', reason);
      this.trackError(
        reason instanceof Error ? reason : new Error(String(reason)),
        { service: 'global' },
        'critical',
      );
    });

    // Listen for integration errors
    this.eventEmitter.on('integration.error', async (event: any) => {
      await this.trackError(event.error, event.context, event.level);
    });
  }

  /**
   * Generate error fingerprint
   */
  private generateErrorFingerprint(
    message: string,
    context: ErrorContext,
  ): string {
    const key = `${context.service}:${context.operation || 'unknown'}:${message}`;
    return Buffer.from(key).toString('base64');
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Add error to recent errors list
   */
  private addToRecentErrors(error: TrackedError) {
    this.recentErrors.unshift(error);

    if (this.recentErrors.length > this.maxRecentErrors) {
      this.recentErrors.pop();
    }
  }

  /**
   * Persist error to storage
   */
  private async persistError(error: TrackedError) {
    try {
      // Cache the error
      await this.cacheManager.set(`error:${error.id}`, error, 86400); // 24 hours

      // Log to database
      const logEntry = this.logsRepository.create({
        timestamp: error.timestamp,
        level: error.level,
        service: error.context.service,
        message: error.message,
        details: {
          errorId: error.id,
          stack: error.stack,
          context: error.context,
          count: error.count,
        },
      });

      await this.logsRepository.save(logEntry);
    } catch (err) {
      this.logger.error('Failed to persist error:', err);
    }
  }

  /**
   * Load persisted errors on startup
   */
  private async loadPersistedErrors() {
    try {
      const recentLogs = await this.logsRepository.find({
        where: { level: 'error' },
        order: { timestamp: 'DESC' },
        take: 100,
      });

      for (const log of recentLogs) {
        if (log.details?.errorId) {
          const cachedError = await this.cacheManager.get<TrackedError>(
            `error:${log.details.errorId}`,
          );
          if (cachedError) {
            const fingerprint = this.generateErrorFingerprint(
              cachedError.message,
              cachedError.context,
            );
            this.errorCache.set(fingerprint, cachedError);
          }
        }
      }

      this.logger.log(`Loaded ${this.errorCache.size} persisted errors`);
    } catch (error) {
      this.logger.error('Failed to load persisted errors:', error);
    }
  }

  /**
   * Match error against patterns
   */
  private matchErrorPattern(message: string): ErrorPattern | null {
    for (const pattern of this.errorPatterns) {
      if (pattern.pattern.test(message)) {
        return pattern;
      }
    }
    return null;
  }

  /**
   * Determine error level based on exception
   */
  private determineErrorLevel(
    exception: any,
  ): 'error' | 'warning' | 'critical' {
    if (exception?.code === 'ECONNREFUSED' || exception?.code === 'ENOTFOUND') {
      return 'critical';
    }

    const pattern = this.matchErrorPattern(exception?.message || '');
    if (pattern) {
      switch (pattern.severity) {
        case 'critical':
          return 'critical';
        case 'high':
          return 'error';
        default:
          return 'warning';
      }
    }

    return 'error';
  }

  /**
   * Update error metrics
   */
  private updateErrorMetrics(error: TrackedError) {
    this.metricsService.recordRequest(
      'error',
      error.context.service,
      error.level === 'critical' ? 500 : 400,
      0,
    );
  }

  /**
   * Check alert thresholds
   */
  private async checkAlertThresholds(error: TrackedError) {
    const pattern = this.matchErrorPattern(error.message);

    if (pattern?.alertThreshold && error.count >= pattern.alertThreshold) {
      this.eventEmitter.emit('error.threshold.exceeded', {
        error,
        pattern,
        threshold: pattern.alertThreshold,
        timestamp: new Date(),
      });

      this.logger.warn(
        `Error threshold exceeded for ${pattern.category}: ${error.count} occurrences`,
      );
    }
  }

  /**
   * Emit error event
   */
  private emitErrorEvent(error: TrackedError) {
    this.eventEmitter.emit('error.tracked', {
      errorId: error.id,
      message: error.message,
      level: error.level,
      service: error.context.service,
      count: error.count,
      timestamp: new Date(),
    });
  }

  /**
   * Group errors by level
   */
  private groupErrorsByLevel(errors: TrackedError[]): Record<string, number> {
    return errors.reduce(
      (acc, error) => {
        acc[error.level] = (acc[error.level] || 0) + error.count;
        return acc;
      },
      {} as Record<string, number>,
    );
  }

  /**
   * Group errors by service
   */
  private groupErrorsByService(errors: TrackedError[]): Record<string, number> {
    return errors.reduce(
      (acc, error) => {
        acc[error.context.service] =
          (acc[error.context.service] || 0) + error.count;
        return acc;
      },
      {} as Record<string, number>,
    );
  }

  /**
   * Get top errors by count
   */
  private getTopErrors(errors: TrackedError[], limit: number): TrackedError[] {
    return errors.sort((a, b) => b.count - a.count).slice(0, limit);
  }

  /**
   * Calculate error rate
   */
  private calculateErrorRate(errors: TrackedError[]): number {
    if (errors.length === 0) return 0;

    const totalRequests = 1000; // This should come from metrics service
    const totalErrors = errors.reduce((sum, e) => sum + e.count, 0);

    return (totalErrors / totalRequests) * 100;
  }

  /**
   * Calculate error trends
   */
  private async calculateErrorTrends() {
    const now = new Date();
    const hourly: number[] = [];
    const daily: number[] = [];

    // Calculate hourly trend (last 24 hours)
    for (let i = 23; i >= 0; i--) {
      const startTime = new Date(now);
      startTime.setHours(startTime.getHours() - i);
      const endTime = new Date(startTime);
      endTime.setHours(endTime.getHours() + 1);

      const count = this.recentErrors.filter(
        (e) => e.timestamp >= startTime && e.timestamp < endTime,
      ).length;

      hourly.push(count);
    }

    // Calculate daily trend (last 7 days)
    for (let i = 6; i >= 0; i--) {
      const startTime = new Date(now);
      startTime.setDate(startTime.getDate() - i);
      startTime.setHours(0, 0, 0, 0);
      const endTime = new Date(startTime);
      endTime.setDate(endTime.getDate() + 1);

      const count = this.recentErrors.filter(
        (e) => e.timestamp >= startTime && e.timestamp < endTime,
      ).length;

      daily.push(count);
    }

    return { hourly, daily };
  }
}
