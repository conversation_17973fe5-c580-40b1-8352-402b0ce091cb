import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Counter, Histogram, Gauge, Registry } from 'prom-client';
import { CacheManagerService } from '../core/cache-manager.service';

export interface MetricSnapshot {
  name: string;
  type: 'counter' | 'gauge' | 'histogram';
  value: number | Record<string, number>;
  labels?: Record<string, string>;
  timestamp: Date;
}

export interface MetricsReport {
  timestamp: Date;
  metrics: MetricSnapshot[];
  aggregations: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    activeUsers: number;
    cacheHitRate: number;
  };
}

@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  private readonly registry: Registry;

  // Counters
  private readonly requestCounter: Counter;
  private readonly errorCounter: Counter;
  private readonly integrationEventCounter: Counter;
  private readonly cacheHitCounter: Counter;
  private readonly cacheMissCounter: Counter;

  // Gauges
  private readonly activeConnectionsGauge: Gauge;
  private readonly memoryUsageGauge: Gauge;
  private readonly queueSizeGauge: Gauge;
  private readonly circuitBreakerStateGauge: Gauge;

  // Histograms
  private readonly responseTimeHistogram: Histogram;
  private readonly databaseQueryHistogram: Histogram;
  private readonly externalApiHistogram: Histogram;

  private metricsBuffer: MetricSnapshot[] = [];
  private readonly bufferMaxSize = 10000;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly cacheManager: CacheManagerService,
  ) {
    this.registry = new Registry();

    // Initialize counters
    this.requestCounter = new Counter({
      name: 'integration_requests_total',
      help: 'Total number of integration requests',
      labelNames: ['method', 'endpoint', 'status'],
      registers: [this.registry],
    });

    this.errorCounter = new Counter({
      name: 'integration_errors_total',
      help: 'Total number of integration errors',
      labelNames: ['type', 'service'],
      registers: [this.registry],
    });

    this.integrationEventCounter = new Counter({
      name: 'integration_events_total',
      help: 'Total number of integration events',
      labelNames: ['event_type', 'source', 'target'],
      registers: [this.registry],
    });

    this.cacheHitCounter = new Counter({
      name: 'cache_hits_total',
      help: 'Total number of cache hits',
      labelNames: ['cache_key_prefix'],
      registers: [this.registry],
    });

    this.cacheMissCounter = new Counter({
      name: 'cache_misses_total',
      help: 'Total number of cache misses',
      labelNames: ['cache_key_prefix'],
      registers: [this.registry],
    });

    // Initialize gauges
    this.activeConnectionsGauge = new Gauge({
      name: 'active_connections',
      help: 'Number of active WebSocket connections',
      labelNames: ['type'],
      registers: [this.registry],
    });

    this.memoryUsageGauge = new Gauge({
      name: 'memory_usage_bytes',
      help: 'Memory usage in bytes',
      labelNames: ['type'],
      registers: [this.registry],
    });

    this.queueSizeGauge = new Gauge({
      name: 'queue_size',
      help: 'Number of items in various queues',
      labelNames: ['queue_name'],
      registers: [this.registry],
    });

    this.circuitBreakerStateGauge = new Gauge({
      name: 'circuit_breaker_state',
      help: 'Circuit breaker state (0=closed, 1=open, 2=half-open)',
      labelNames: ['service'],
      registers: [this.registry],
    });

    // Initialize histograms
    this.responseTimeHistogram = new Histogram({
      name: 'response_time_seconds',
      help: 'Response time in seconds',
      labelNames: ['method', 'endpoint'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
      registers: [this.registry],
    });

    this.databaseQueryHistogram = new Histogram({
      name: 'database_query_duration_seconds',
      help: 'Database query duration in seconds',
      labelNames: ['query_type', 'table'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5],
      registers: [this.registry],
    });

    this.externalApiHistogram = new Histogram({
      name: 'external_api_duration_seconds',
      help: 'External API call duration in seconds',
      labelNames: ['api', 'endpoint'],
      buckets: [0.1, 0.5, 1, 2, 5, 10, 30],
      registers: [this.registry],
    });

    this.setupEventListeners();
  }

  /**
   * Record a request metric
   */
  recordRequest(
    method: string,
    endpoint: string,
    status: number,
    duration: number,
  ) {
    this.requestCounter.inc({ method, endpoint, status: status.toString() });
    this.responseTimeHistogram.observe({ method, endpoint }, duration / 1000);

    if (status >= 400) {
      this.errorCounter.inc({ type: 'http_error', service: endpoint });
    }

    this.bufferMetric({
      name: 'request',
      type: 'counter',
      value: 1,
      labels: { method, endpoint, status: status.toString() },
      timestamp: new Date(),
    });
  }

  /**
   * Record an integration event
   */
  recordIntegrationEvent(eventType: string, source: string, target: string) {
    this.integrationEventCounter.inc({ event_type: eventType, source, target });

    this.bufferMetric({
      name: 'integration_event',
      type: 'counter',
      value: 1,
      labels: { event_type: eventType, source, target },
      timestamp: new Date(),
    });
  }

  /**
   * Record cache access
   */
  recordCacheAccess(hit: boolean, keyPrefix: string) {
    if (hit) {
      this.cacheHitCounter.inc({ cache_key_prefix: keyPrefix });
    } else {
      this.cacheMissCounter.inc({ cache_key_prefix: keyPrefix });
    }

    this.bufferMetric({
      name: hit ? 'cache_hit' : 'cache_miss',
      type: 'counter',
      value: 1,
      labels: { cache_key_prefix: keyPrefix },
      timestamp: new Date(),
    });
  }

  /**
   * Update active connections gauge
   */
  updateActiveConnections(type: string, count: number) {
    this.activeConnectionsGauge.set({ type }, count);
  }

  /**
   * Update queue size gauge
   */
  updateQueueSize(queueName: string, size: number) {
    this.queueSizeGauge.set({ queue_name: queueName }, size);
  }

  /**
   * Update circuit breaker state
   */
  updateCircuitBreakerState(
    service: string,
    state: 'closed' | 'open' | 'half-open',
  ) {
    const stateValue = state === 'closed' ? 0 : state === 'open' ? 1 : 2;
    this.circuitBreakerStateGauge.set({ service }, stateValue);
  }

  /**
   * Record database query duration
   */
  recordDatabaseQuery(queryType: string, table: string, duration: number) {
    this.databaseQueryHistogram.observe(
      { query_type: queryType, table },
      duration / 1000,
    );
  }

  /**
   * Record external API call duration
   */
  recordExternalApiCall(api: string, endpoint: string, duration: number) {
    this.externalApiHistogram.observe({ api, endpoint }, duration / 1000);
  }

  /**
   * Get Prometheus metrics
   */
  async getPrometheusMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  /**
   * Get metrics report
   */
  async getMetricsReport(): Promise<MetricsReport> {
    const metrics = await this.collectCurrentMetrics();
    const aggregations = await this.calculateAggregations();

    return {
      timestamp: new Date(),
      metrics,
      aggregations,
    };
  }

  /**
   * Get buffered metrics
   */
  getBufferedMetrics(limit?: number): MetricSnapshot[] {
    if (limit) {
      return this.metricsBuffer.slice(-limit);
    }
    return [...this.metricsBuffer];
  }

  /**
   * Clear metrics buffer
   */
  clearMetricsBuffer() {
    const size = this.metricsBuffer.length;
    this.metricsBuffer = [];
    this.logger.debug(`Cleared ${size} metrics from buffer`);
  }

  /**
   * Collect memory metrics periodically
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  private collectMemoryMetrics() {
    const memoryUsage = process.memoryUsage();

    this.memoryUsageGauge.set({ type: 'heap_used' }, memoryUsage.heapUsed);
    this.memoryUsageGauge.set({ type: 'heap_total' }, memoryUsage.heapTotal);
    this.memoryUsageGauge.set({ type: 'rss' }, memoryUsage.rss);
    this.memoryUsageGauge.set({ type: 'external' }, memoryUsage.external);
  }

  /**
   * Persist metrics to cache periodically
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  private async persistMetrics() {
    if (this.metricsBuffer.length === 0) {
      return;
    }

    try {
      const key = `metrics:buffer:${Date.now()}`;
      await this.cacheManager.set(key, this.metricsBuffer, 3600); // 1 hour TTL

      this.logger.debug(
        `Persisted ${this.metricsBuffer.length} metrics to cache`,
      );
      this.clearMetricsBuffer();
    } catch (error) {
      this.logger.error('Failed to persist metrics:', error);
    }
  }

  /**
   * Buffer metric for batch processing
   */
  private bufferMetric(metric: MetricSnapshot) {
    this.metricsBuffer.push(metric);

    // Prevent buffer overflow
    if (this.metricsBuffer.length > this.bufferMaxSize) {
      this.metricsBuffer.shift();
    }
  }

  /**
   * Collect current metrics from registry
   */
  private async collectCurrentMetrics(): Promise<MetricSnapshot[]> {
    const metrics: MetricSnapshot[] = [];
    const timestamp = new Date();

    // Get all metric families from registry
    const metricFamilies = await this.registry.getMetricsAsJSON();

    for (const family of metricFamilies) {
      const metricType = family.type as 'counter' | 'gauge' | 'histogram';

      if (family.values && family.values.length > 0) {
        for (const value of family.values) {
          metrics.push({
            name: family.name,
            type: metricType,
            value: value.value,
            labels: value.labels,
            timestamp,
          });
        }
      }
    }

    return metrics;
  }

  /**
   * Calculate aggregated metrics
   */
  private async calculateAggregations() {
    // These would be calculated from actual metrics
    // This is a simplified implementation
    const recentRequests = this.metricsBuffer.filter(
      (m) => m.name === 'request' && Date.now() - m.timestamp.getTime() < 60000,
    ).length;

    const recentErrors = this.metricsBuffer.filter(
      (m) =>
        m.name === 'integration_event' &&
        m.labels?.event_type === 'error' &&
        Date.now() - m.timestamp.getTime() < 60000,
    ).length;

    const cacheHits = this.metricsBuffer.filter(
      (m) => m.name === 'cache_hit',
    ).length;
    const cacheMisses = this.metricsBuffer.filter(
      (m) => m.name === 'cache_miss',
    ).length;
    const totalCacheAccess = cacheHits + cacheMisses;

    return {
      requestsPerMinute: recentRequests,
      averageResponseTime: 0, // Would be calculated from histogram
      errorRate: recentRequests > 0 ? (recentErrors / recentRequests) * 100 : 0,
      activeUsers: 0, // Would be tracked separately
      cacheHitRate:
        totalCacheAccess > 0 ? (cacheHits / totalCacheAccess) * 100 : 0,
    };
  }

  /**
   * Setup event listeners for automatic metric collection
   */
  private setupEventListeners() {
    // Listen for integration events
    this.eventEmitter.on('integration.**', (event: any) => {
      if (event.type && event.source && event.target) {
        this.recordIntegrationEvent(event.type, event.source, event.target);
      }
    });

    // Listen for error events
    this.eventEmitter.on('error.**', (error: any) => {
      this.errorCounter.inc({
        type: error.type || 'unknown',
        service: error.service || 'unknown',
      });
    });
  }
}
