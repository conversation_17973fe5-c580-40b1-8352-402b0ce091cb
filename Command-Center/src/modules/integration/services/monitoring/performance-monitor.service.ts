import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as os from 'os';
import { MetricsService } from './metrics.service';
import { CacheManagerService } from '../core/cache-manager.service';

export interface PerformanceMetric {
  timestamp: Date;
  cpu: {
    usage: number;
    loadAverage: number[];
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
    heapUsed: number;
    heapTotal: number;
  };
  eventLoop: {
    lag: number;
    utilization: number;
  };
  gc?: {
    collections: number;
    pauseTime: number;
  };
}

export interface PerformanceThreshold {
  metric: string;
  threshold: number;
  action: 'warn' | 'alert' | 'critical';
}

export interface PerformanceReport {
  current: PerformanceMetric;
  average: {
    cpu: number;
    memory: number;
    eventLoopLag: number;
  };
  trends: {
    cpu: 'stable' | 'increasing' | 'decreasing';
    memory: 'stable' | 'increasing' | 'decreasing';
  };
  alerts: string[];
  recommendations: string[];
}

@Injectable()
export class PerformanceMonitorService implements OnModuleInit {
  private readonly logger = new Logger(PerformanceMonitorService.name);
  private readonly metricsHistory: PerformanceMetric[] = [];
  private readonly maxHistorySize = 1000;
  private lastCpuUsage: any;
  private eventLoopMonitor: any;

  private readonly thresholds: PerformanceThreshold[] = [
    { metric: 'cpu.usage', threshold: 80, action: 'warn' },
    { metric: 'cpu.usage', threshold: 90, action: 'alert' },
    { metric: 'cpu.usage', threshold: 95, action: 'critical' },
    { metric: 'memory.percentage', threshold: 80, action: 'warn' },
    { metric: 'memory.percentage', threshold: 90, action: 'alert' },
    { metric: 'memory.percentage', threshold: 95, action: 'critical' },
    { metric: 'eventLoop.lag', threshold: 100, action: 'warn' },
    { metric: 'eventLoop.lag', threshold: 500, action: 'alert' },
    { metric: 'eventLoop.lag', threshold: 1000, action: 'critical' },
  ];

  constructor(
    private readonly metricsService: MetricsService,
    private readonly cacheManager: CacheManagerService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.lastCpuUsage = process.cpuUsage();
  }

  async onModuleInit() {
    this.logger.log('Initializing performance monitor service');
    this.startEventLoopMonitoring();
  }

  /**
   * Collect current performance metrics
   */
  @Cron(CronExpression.EVERY_10_SECONDS)
  async collectMetrics(): Promise<PerformanceMetric> {
    const metric = await this.gatherPerformanceData();

    // Store in history
    this.metricsHistory.push(metric);
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory.shift();
    }

    // Check thresholds and emit alerts
    await this.checkThresholds(metric);

    // Update metrics service
    this.updateMetricsService(metric);

    return metric;
  }

  /**
   * Get current performance snapshot
   */
  async getCurrentPerformance(): Promise<PerformanceMetric> {
    return await this.gatherPerformanceData();
  }

  /**
   * Get performance report with analysis
   */
  async getPerformanceReport(): Promise<PerformanceReport> {
    const current = await this.getCurrentPerformance();
    const average = this.calculateAverages();
    const trends = this.analyzeTrends();
    const alerts = await this.getActiveAlerts(current);
    const recommendations = this.generateRecommendations(current, trends);

    return {
      current,
      average,
      trends,
      alerts,
      recommendations,
    };
  }

  /**
   * Get historical metrics
   */
  getHistoricalMetrics(duration?: number): PerformanceMetric[] {
    if (!duration) {
      return [...this.metricsHistory];
    }

    const cutoff = Date.now() - duration;
    return this.metricsHistory.filter((m) => m.timestamp.getTime() > cutoff);
  }

  /**
   * Set custom threshold
   */
  setThreshold(
    metric: string,
    threshold: number,
    action: 'warn' | 'alert' | 'critical',
  ) {
    const existing = this.thresholds.findIndex(
      (t) => t.metric === metric && t.action === action,
    );

    if (existing >= 0) {
      this.thresholds[existing].threshold = threshold;
    } else {
      this.thresholds.push({ metric, threshold, action });
    }

    this.logger.debug(`Set threshold for ${metric}: ${threshold} (${action})`);
  }

  /**
   * Start monitoring a specific operation
   */
  startOperation(operationId: string): () => void {
    const startTime = Date.now();
    const startCpu = process.cpuUsage();
    const startMem = process.memoryUsage();

    return () => {
      const duration = Date.now() - startTime;
      const cpuUsage = process.cpuUsage(startCpu);
      const endMem = process.memoryUsage();

      const memoryDelta = {
        heapUsed: endMem.heapUsed - startMem.heapUsed,
        external: endMem.external - startMem.external,
      };

      this.eventEmitter.emit('performance.operation', {
        operationId,
        duration,
        cpu: cpuUsage,
        memory: memoryDelta,
      });

      // Log if operation was slow
      if (duration > 1000) {
        this.logger.warn(
          `Slow operation detected: ${operationId} took ${duration}ms`,
        );
      }
    };
  }

  /**
   * Gather all performance data
   */
  private async gatherPerformanceData(): Promise<PerformanceMetric> {
    const cpuUsage = this.calculateCpuUsage();
    const memoryUsage = this.getMemoryUsage();
    const eventLoopMetrics = this.getEventLoopMetrics();
    const gcMetrics = this.getGCMetrics();

    return {
      timestamp: new Date(),
      cpu: {
        usage: cpuUsage,
        loadAverage: os.loadavg(),
        cores: os.cpus().length,
      },
      memory: memoryUsage,
      eventLoop: eventLoopMetrics,
      gc: gcMetrics,
    };
  }

  /**
   * Calculate CPU usage percentage
   */
  private calculateCpuUsage(): number {
    const currentUsage = process.cpuUsage();
    const deltaUser = currentUsage.user - this.lastCpuUsage.user;
    const deltaSystem = currentUsage.system - this.lastCpuUsage.system;
    const deltaTotal = deltaUser + deltaSystem;

    const elapsedTime = 10000000; // 10 seconds in microseconds
    const percentage = (deltaTotal / elapsedTime) * 100;

    this.lastCpuUsage = currentUsage;

    return Math.min(100, Math.max(0, percentage));
  }

  /**
   * Get memory usage information
   */
  private getMemoryUsage() {
    const processMemory = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    return {
      used: usedMemory,
      total: totalMemory,
      percentage: (usedMemory / totalMemory) * 100,
      heapUsed: processMemory.heapUsed,
      heapTotal: processMemory.heapTotal,
    };
  }

  /**
   * Get event loop metrics
   */
  private getEventLoopMetrics() {
    // Simplified implementation
    // In production, use proper event loop monitoring
    return {
      lag: 0,
      utilization: 0,
    };
  }

  /**
   * Get garbage collection metrics
   */
  private getGCMetrics() {
    // This would require --expose-gc flag
    // Simplified implementation
    return undefined;
  }

  /**
   * Start event loop monitoring
   */
  private startEventLoopMonitoring() {
    let lastCheck = Date.now();

    setInterval(() => {
      const now = Date.now();
      const lag = now - lastCheck - 1000;

      if (lag > 50) {
        this.logger.warn(`Event loop lag detected: ${lag}ms`);
      }

      lastCheck = now;
    }, 1000);
  }

  /**
   * Check performance thresholds
   */
  private async checkThresholds(metric: PerformanceMetric) {
    for (const threshold of this.thresholds) {
      const value = this.getMetricValue(metric, threshold.metric);

      if (value > threshold.threshold) {
        const alert = {
          metric: threshold.metric,
          value,
          threshold: threshold.threshold,
          action: threshold.action,
          timestamp: new Date(),
        };

        this.eventEmitter.emit(`performance.${threshold.action}`, alert);

        // Cache alert for retrieval
        await this.cacheManager.set(
          `performance:alert:${threshold.metric}:${Date.now()}`,
          alert,
          300, // 5 minutes
        );
      }
    }
  }

  /**
   * Get metric value from nested object
   */
  private getMetricValue(metric: PerformanceMetric, path: string): number {
    const parts = path.split('.');
    let value: any = metric;

    for (const part of parts) {
      value = value?.[part];
    }

    return typeof value === 'number' ? value : 0;
  }

  /**
   * Update metrics service with performance data
   */
  private updateMetricsService(metric: PerformanceMetric) {
    // Update gauges in metrics service
    this.metricsService.updateQueueSize('performance_cpu', metric.cpu.usage);
    this.metricsService.updateQueueSize(
      'performance_memory',
      metric.memory.percentage,
    );

    if (metric.eventLoop.lag > 0) {
      this.metricsService.recordRequest(
        'eventloop',
        'lag',
        200,
        metric.eventLoop.lag,
      );
    }
  }

  /**
   * Calculate average metrics
   */
  private calculateAverages() {
    if (this.metricsHistory.length === 0) {
      return { cpu: 0, memory: 0, eventLoopLag: 0 };
    }

    const sum = this.metricsHistory.reduce(
      (acc, metric) => ({
        cpu: acc.cpu + metric.cpu.usage,
        memory: acc.memory + metric.memory.percentage,
        eventLoopLag: acc.eventLoopLag + metric.eventLoop.lag,
      }),
      { cpu: 0, memory: 0, eventLoopLag: 0 },
    );

    const count = this.metricsHistory.length;

    return {
      cpu: sum.cpu / count,
      memory: sum.memory / count,
      eventLoopLag: sum.eventLoopLag / count,
    };
  }

  /**
   * Analyze performance trends
   */
  private analyzeTrends() {
    if (this.metricsHistory.length < 10) {
      return { cpu: 'stable' as const, memory: 'stable' as const };
    }

    const recent = this.metricsHistory.slice(-10);
    const older = this.metricsHistory.slice(-20, -10);

    const recentAvg = this.calculateAveragesForSet(recent);
    const olderAvg = this.calculateAveragesForSet(older);

    const cpuTrend = this.determineTrend(olderAvg.cpu, recentAvg.cpu);
    const memoryTrend = this.determineTrend(olderAvg.memory, recentAvg.memory);

    return {
      cpu: cpuTrend,
      memory: memoryTrend,
    };
  }

  /**
   * Calculate averages for a set of metrics
   */
  private calculateAveragesForSet(metrics: PerformanceMetric[]) {
    const sum = metrics.reduce(
      (acc, metric) => ({
        cpu: acc.cpu + metric.cpu.usage,
        memory: acc.memory + metric.memory.percentage,
      }),
      { cpu: 0, memory: 0 },
    );

    return {
      cpu: sum.cpu / metrics.length,
      memory: sum.memory / metrics.length,
    };
  }

  /**
   * Determine trend direction
   */
  private determineTrend(
    older: number,
    recent: number,
  ): 'stable' | 'increasing' | 'decreasing' {
    const threshold = 5; // 5% change threshold
    const change = ((recent - older) / older) * 100;

    if (Math.abs(change) < threshold) {
      return 'stable';
    }

    return change > 0 ? 'increasing' : 'decreasing';
  }

  /**
   * Get active alerts for current metrics
   */
  private async getActiveAlerts(metric: PerformanceMetric): Promise<string[]> {
    const alerts: string[] = [];

    for (const threshold of this.thresholds) {
      const value = this.getMetricValue(metric, threshold.metric);

      if (value > threshold.threshold) {
        alerts.push(
          `${threshold.action.toUpperCase()}: ${threshold.metric} is ${value.toFixed(2)} (threshold: ${threshold.threshold})`,
        );
      }
    }

    return alerts;
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    current: PerformanceMetric,
    trends: { cpu: string; memory: string },
  ): string[] {
    const recommendations: string[] = [];

    // CPU recommendations
    if (current.cpu.usage > 80) {
      recommendations.push(
        'Consider scaling horizontally to distribute CPU load',
      );
    }
    if (trends.cpu === 'increasing') {
      recommendations.push(
        'CPU usage is trending upward - investigate resource-intensive operations',
      );
    }

    // Memory recommendations
    if (current.memory.percentage > 80) {
      recommendations.push(
        'Memory usage is high - consider increasing memory allocation',
      );
    }
    if (trends.memory === 'increasing') {
      recommendations.push(
        'Memory usage is increasing - check for memory leaks',
      );
    }
    if (current.memory.heapUsed > current.memory.heapTotal * 0.9) {
      recommendations.push(
        'Heap memory is nearly full - increase --max-old-space-size',
      );
    }

    // Event loop recommendations
    if (current.eventLoop.lag > 100) {
      recommendations.push(
        'Event loop lag detected - optimize blocking operations',
      );
    }

    return recommendations;
  }
}
