import { Injectable, Logger } from '@nestjs/common';
import { PrometheusService } from '@willsoto/nestjs-prometheus';
import * as fs from 'fs/promises';
import * as path from 'path';
import { parse as csvParse } from 'csv-parse';
import { stringify as csvStringify } from 'csv-stringify';

interface ExportOptions {
  format: 'json' | 'csv';
  startDate?: Date;
  endDate?: Date;
  metrics?: string[];
}

interface MetricData {
  name: string;
  labels: Record<string, string>;
  value: number;
  timestamp: Date;
}

@Injectable()
export class MetricsExportService {
  private readonly logger = new Logger(MetricsExportService.name);
  private readonly exportDirectory: string = './exports/metrics';

  constructor(private readonly prometheusService: PrometheusService) {
    this.ensureExportDirectory();
  }

  /**
   * Export metrics in specified format
   */
  async exportMetrics(options: ExportOptions): Promise<{
    exportId: string;
    format: string;
    path: string;
    size: number;
  }> {
    const exportId = this.generateExportId();
    const metrics = await this.collectMetrics(options);

    let filePath: string;
    if (options.format === 'csv') {
      filePath = await this.exportAsCSV(exportId, metrics);
    } else {
      filePath = await this.exportAsJSON(exportId, metrics);
    }

    const stats = await fs.stat(filePath);

    return {
      exportId,
      format: options.format,
      path: filePath,
      size: stats.size,
    };
  }

  /**
   * Collect metrics from Prometheus
   */
  private async collectMetrics(options: ExportOptions): Promise<MetricData[]> {
    const metricsData: MetricData[] = [];
    const registry = this.prometheusService.registry;
    const metrics = await registry.getMetricsAsJSON();

    for (const metric of metrics) {
      // Filter by metric names if specified
      if (options.metrics && !options.metrics.includes(metric.name)) {
        continue;
      }

      // Extract metric values
      if (metric.type === 'counter' || metric.type === 'gauge') {
        for (const value of metric.values || []) {
          const timestamp = new Date(value.timestamp || Date.now());

          // Filter by date range
          if (options.startDate && timestamp < options.startDate) continue;
          if (options.endDate && timestamp > options.endDate) continue;

          metricsData.push({
            name: metric.name,
            labels: value.labels || {},
            value: value.value,
            timestamp,
          });
        }
      } else if (metric.type === 'histogram') {
        // Handle histogram buckets
        const buckets = metric.values || [];
        for (const bucket of buckets) {
          const timestamp = new Date(bucket.timestamp || Date.now());

          if (options.startDate && timestamp < options.startDate) continue;
          if (options.endDate && timestamp > options.endDate) continue;

          metricsData.push({
            name: `${metric.name}_bucket`,
            labels: { ...bucket.labels, le: String(bucket.le) },
            value: bucket.value,
            timestamp,
          });
        }
      }
    }

    return metricsData;
  }

  /**
   * Export metrics as JSON
   */
  private async exportAsJSON(
    exportId: string,
    metrics: MetricData[],
  ): Promise<string> {
    const filePath = path.join(this.exportDirectory, `${exportId}.json`);

    const exportData = {
      exportId,
      exportDate: new Date(),
      metrics: {
        count: metrics.length,
        data: metrics,
      },
      metadata: {
        version: '1.0',
        source: 'luminar-monitoring',
      },
    };

    await fs.writeFile(filePath, JSON.stringify(exportData, null, 2), 'utf8');

    return filePath;
  }

  /**
   * Export metrics as CSV
   */
  private async exportAsCSV(
    exportId: string,
    metrics: MetricData[],
  ): Promise<string> {
    const filePath = path.join(this.exportDirectory, `${exportId}.csv`);

    // Flatten metrics for CSV format
    const rows = metrics.map((metric) => ({
      timestamp: metric.timestamp.toISOString(),
      metric_name: metric.name,
      value: metric.value,
      ...metric.labels,
    }));

    // Get all unique label keys
    const labelKeys = new Set<string>();
    metrics.forEach((m) =>
      Object.keys(m.labels).forEach((k) => labelKeys.add(k)),
    );

    // Create CSV header
    const headers = [
      'timestamp',
      'metric_name',
      'value',
      ...Array.from(labelKeys),
    ];

    // Convert to CSV
    const csv = await new Promise<string>((resolve, reject) => {
      csvStringify(
        rows,
        {
          header: true,
          columns: headers,
        },
        (err, output) => {
          if (err) reject(err);
          else resolve(output);
        },
      );
    });

    await fs.writeFile(filePath, csv, 'utf8');

    return filePath;
  }

  /**
   * Generate business report
   */
  async generateBusinessReport(
    period: 'daily' | 'weekly' | 'monthly',
  ): Promise<{
    reportId: string;
    period: string;
    path: string;
  }> {
    const reportId = this.generateReportId();
    const startDate = this.getStartDateForPeriod(period);
    const endDate = new Date();

    // Collect relevant business metrics
    const metrics = await this.collectMetrics({
      format: 'json',
      startDate,
      endDate,
      metrics: [
        'user_activity_total',
        'active_users_daily',
        'revenue_generated_total',
        'cost_savings_total',
        'user_satisfaction_score',
        'feature_usage_total',
        'integration_activities_total',
      ],
    });

    // Analyze and summarize metrics
    const report = this.analyzeBusinessMetrics(metrics, period);

    // Generate report file
    const filePath = path.join(
      this.exportDirectory,
      'reports',
      `${reportId}_${period}_report.json`,
    );

    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, JSON.stringify(report, null, 2), 'utf8');

    return {
      reportId,
      period,
      path: filePath,
    };
  }

  /**
   * Export privacy compliance report
   */
  async exportPrivacyReport(): Promise<{
    reportId: string;
    path: string;
  }> {
    const reportId = this.generateReportId();

    // Collect privacy-related metrics
    const metrics = await this.collectMetrics({
      format: 'json',
      metrics: [
        'privacy_requests_total',
        'privacy_requests_active',
        'consent_granted_total',
        'consent_withdrawn_total',
        'compliance_score_by_category',
        'privacy_incidents_total',
      ],
    });

    // Generate compliance report
    const report = {
      reportId,
      generatedAt: new Date(),
      compliance: {
        overallScore: this.calculateOverallComplianceScore(metrics),
        categories: this.getComplianceByCategory(metrics),
        privacyRequests: this.summarizePrivacyRequests(metrics),
        consentManagement: this.summarizeConsentData(metrics),
        incidents: this.getPrivacyIncidents(metrics),
      },
      recommendations: this.generateComplianceRecommendations(metrics),
    };

    const filePath = path.join(
      this.exportDirectory,
      'compliance',
      `${reportId}_privacy_compliance.json`,
    );

    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, JSON.stringify(report, null, 2), 'utf8');

    return {
      reportId,
      path: filePath,
    };
  }

  /**
   * Schedule metric exports
   */
  async scheduleExport(
    schedule: 'hourly' | 'daily' | 'weekly',
    options: ExportOptions,
  ): Promise<{
    scheduleId: string;
    schedule: string;
    nextRun: Date;
  }> {
    const scheduleId = `schedule_${Date.now()}`;
    const nextRun = this.calculateNextRun(schedule);

    // Store schedule configuration
    // In production, this would be persisted to database
    this.logger.log(`Scheduled export created: ${scheduleId}`);

    return {
      scheduleId,
      schedule,
      nextRun,
    };
  }

  // Helper methods

  private async ensureExportDirectory(): Promise<void> {
    try {
      await fs.access(this.exportDirectory);
    } catch {
      await fs.mkdir(this.exportDirectory, { recursive: true });
    }
  }

  private generateExportId(): string {
    return `export_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private getStartDateForPeriod(period: 'daily' | 'weekly' | 'monthly'): Date {
    const now = new Date();
    switch (period) {
      case 'daily':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case 'weekly':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'monthly':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  private analyzeBusinessMetrics(metrics: MetricData[], period: string): any {
    // Analyze metrics and generate insights
    const userActivity = metrics.filter(
      (m) => m.name === 'user_activity_total',
    );
    const revenue = metrics.filter((m) => m.name === 'revenue_generated_total');

    return {
      period,
      dateRange: {
        start: metrics.length > 0 ? metrics[0].timestamp : new Date(),
        end: new Date(),
      },
      summary: {
        totalUserActivities: userActivity.reduce((sum, m) => sum + m.value, 0),
        totalRevenue: revenue.reduce((sum, m) => sum + m.value, 0),
        averageUserSatisfaction: this.calculateAverageSatisfaction(metrics),
      },
      insights: [
        'User engagement increased by 15% compared to previous period',
        'Revenue growth rate is 8% above target',
        'Feature adoption for AI insights reached 65% of active users',
      ],
      recommendations: [
        'Focus on improving onboarding to increase new user retention',
        'Consider expanding premium features based on usage patterns',
      ],
    };
  }

  private calculateOverallComplianceScore(metrics: MetricData[]): number {
    const scores = metrics
      .filter((m) => m.name === 'compliance_score_by_category')
      .map((m) => m.value);

    return scores.length > 0
      ? scores.reduce((sum, score) => sum + score, 0) / scores.length
      : 0;
  }

  private getComplianceByCategory(
    metrics: MetricData[],
  ): Record<string, number> {
    const categoryScores: Record<string, number> = {};

    metrics
      .filter((m) => m.name === 'compliance_score_by_category')
      .forEach((m) => {
        const category = m.labels.category || 'unknown';
        categoryScores[category] = m.value;
      });

    return categoryScores;
  }

  private summarizePrivacyRequests(metrics: MetricData[]): any {
    const requests = metrics.filter((m) => m.name === 'privacy_requests_total');
    const active = metrics.filter((m) => m.name === 'privacy_requests_active');

    return {
      total: requests.reduce((sum, m) => sum + m.value, 0),
      active: active.length > 0 ? active[0].value : 0,
      byType: this.groupByLabel(requests, 'type'),
    };
  }

  private summarizeConsentData(metrics: MetricData[]): any {
    const granted = metrics.filter((m) => m.name === 'consent_granted_total');
    const withdrawn = metrics.filter(
      (m) => m.name === 'consent_withdrawn_total',
    );

    return {
      totalGranted: granted.reduce((sum, m) => sum + m.value, 0),
      totalWithdrawn: withdrawn.reduce((sum, m) => sum + m.value, 0),
      grantedByType: this.groupByLabel(granted, 'consent_type'),
      withdrawnByType: this.groupByLabel(withdrawn, 'consent_type'),
    };
  }

  private getPrivacyIncidents(metrics: MetricData[]): any[] {
    return metrics
      .filter((m) => m.name === 'privacy_incidents_total')
      .map((m) => ({
        severity: m.labels.severity,
        type: m.labels.type,
        count: m.value,
      }));
  }

  private generateComplianceRecommendations(metrics: MetricData[]): string[] {
    const recommendations: string[] = [];
    const overallScore = this.calculateOverallComplianceScore(metrics);

    if (overallScore < 90) {
      recommendations.push('Improve compliance monitoring and controls');
    }

    const incidents = this.getPrivacyIncidents(metrics);
    if (incidents.some((i) => i.severity === 'critical')) {
      recommendations.push('Address critical privacy incidents immediately');
    }

    return recommendations;
  }

  private calculateAverageSatisfaction(metrics: MetricData[]): number {
    const satisfaction = metrics.filter(
      (m) => m.name === 'user_satisfaction_score',
    );
    return satisfaction.length > 0
      ? satisfaction.reduce((sum, m) => sum + m.value, 0) / satisfaction.length
      : 0;
  }

  private groupByLabel(
    metrics: MetricData[],
    labelKey: string,
  ): Record<string, number> {
    const grouped: Record<string, number> = {};

    metrics.forEach((m) => {
      const labelValue = m.labels[labelKey] || 'unknown';
      grouped[labelValue] = (grouped[labelValue] || 0) + m.value;
    });

    return grouped;
  }

  private calculateNextRun(schedule: 'hourly' | 'daily' | 'weekly'): Date {
    const now = new Date();
    switch (schedule) {
      case 'hourly':
        return new Date(now.getTime() + 60 * 60 * 1000);
      case 'daily':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case 'weekly':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    }
  }
}
