import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { firstValueFrom } from 'rxjs';

interface Alert {
  name: string;
  severity: 'info' | 'warning' | 'critical';
  condition: string;
  threshold: number;
  message: string;
  tags: string[];
}

interface AlertNotification {
  alertName: string;
  severity: string;
  message: string;
  value: number;
  timestamp: Date;
  metadata?: any;
}

@Injectable()
export class AlertingService {
  private readonly logger = new Logger(AlertingService.name);
  private readonly alerts: Map<string, Alert>;
  private readonly alertHistory: AlertNotification[] = [];

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.alerts = this.initializeAlerts();
  }

  private initializeAlerts(): Map<string, Alert> {
    const alerts = new Map<string, Alert>();

    // Integration Health Alerts
    alerts.set('integration-down', {
      name: 'Integration Service Down',
      severity: 'critical',
      condition: 'up == 0',
      threshold: 0,
      message: 'Integration service {{service}} is down',
      tags: ['integration', 'availability'],
    });

    alerts.set('high-error-rate', {
      name: 'High Error Rate',
      severity: 'warning',
      condition: 'error_rate > 5',
      threshold: 5,
      message: 'Error rate is {{value}}% (threshold: 5%)',
      tags: ['integration', 'errors'],
    });

    alerts.set('slow-response-time', {
      name: 'Slow Response Time',
      severity: 'warning',
      condition: 'avg_response_time > 1000',
      threshold: 1000,
      message: 'Average response time is {{value}}ms (threshold: 1000ms)',
      tags: ['integration', 'performance'],
    });

    // Business Metrics Alerts
    alerts.set('low-user-engagement', {
      name: 'Low User Engagement',
      severity: 'info',
      condition: 'user_engagement_rate < 40',
      threshold: 40,
      message: 'User engagement rate dropped to {{value}}%',
      tags: ['business', 'engagement'],
    });

    alerts.set('revenue-decline', {
      name: 'Revenue Decline',
      severity: 'warning',
      condition: 'revenue_growth < 0',
      threshold: 0,
      message: 'Revenue growth is negative: {{value}}%',
      tags: ['business', 'revenue'],
    });

    // Privacy & Compliance Alerts
    alerts.set('privacy-request-backlog', {
      name: 'Privacy Request Backlog',
      severity: 'warning',
      condition: 'active_privacy_requests > 50',
      threshold: 50,
      message: 'Privacy request backlog: {{value}} active requests',
      tags: ['privacy', 'compliance'],
    });

    alerts.set('compliance-score-drop', {
      name: 'Compliance Score Drop',
      severity: 'critical',
      condition: 'compliance_score < 85',
      threshold: 85,
      message: 'Compliance score dropped to {{value}}%',
      tags: ['privacy', 'compliance'],
    });

    alerts.set('privacy-incident', {
      name: 'Privacy Incident Detected',
      severity: 'critical',
      condition: 'privacy_incidents > 0',
      threshold: 0,
      message: 'Privacy incident detected: {{metadata}}',
      tags: ['privacy', 'security'],
    });

    // Resource Alerts
    alerts.set('high-memory-usage', {
      name: 'High Memory Usage',
      severity: 'warning',
      condition: 'memory_usage_percent > 80',
      threshold: 80,
      message: 'Memory usage is {{value}}%',
      tags: ['infrastructure', 'resources'],
    });

    alerts.set('high-cpu-usage', {
      name: 'High CPU Usage',
      severity: 'warning',
      condition: 'cpu_usage_percent > 80',
      threshold: 80,
      message: 'CPU usage is {{value}}%',
      tags: ['infrastructure', 'resources'],
    });

    return alerts;
  }

  /**
   * Check alert conditions
   */
  async checkAlerts(
    metrics: Record<string, number>,
  ): Promise<AlertNotification[]> {
    const triggeredAlerts: AlertNotification[] = [];

    for (const [alertId, alert] of this.alerts) {
      const isTriggered = this.evaluateCondition(alert, metrics);

      if (isTriggered) {
        const notification: AlertNotification = {
          alertName: alert.name,
          severity: alert.severity,
          message: this.formatMessage(alert.message, metrics),
          value: metrics[this.extractMetricName(alert.condition)] || 0,
          timestamp: new Date(),
        };

        triggeredAlerts.push(notification);
        await this.sendAlert(notification);
      }
    }

    return triggeredAlerts;
  }

  /**
   * Send alert notification
   */
  private async sendAlert(notification: AlertNotification): Promise<void> {
    this.logger.warn(
      `Alert triggered: ${notification.alertName}`,
      notification,
    );

    // Store in history
    this.alertHistory.push(notification);
    if (this.alertHistory.length > 1000) {
      this.alertHistory.shift();
    }

    // Emit event
    this.eventEmitter.emit('alert.triggered', notification);

    // Send to external services based on severity
    if (notification.severity === 'critical') {
      await this.sendCriticalAlert(notification);
    } else if (notification.severity === 'warning') {
      await this.sendWarningAlert(notification);
    }
  }

  /**
   * Send critical alert
   */
  private async sendCriticalAlert(
    notification: AlertNotification,
  ): Promise<void> {
    // Send to multiple channels for critical alerts
    const promises = [];

    // Slack
    const slackWebhook = this.configService.get('SLACK_WEBHOOK_URL');
    if (slackWebhook) {
      promises.push(this.sendSlackAlert(notification, slackWebhook));
    }

    // PagerDuty
    const pagerDutyKey = this.configService.get('PAGERDUTY_KEY');
    if (pagerDutyKey) {
      promises.push(this.sendPagerDutyAlert(notification, pagerDutyKey));
    }

    // Email
    const emailEndpoint = this.configService.get('EMAIL_ALERT_ENDPOINT');
    if (emailEndpoint) {
      promises.push(this.sendEmailAlert(notification, emailEndpoint));
    }

    await Promise.allSettled(promises);
  }

  /**
   * Send warning alert
   */
  private async sendWarningAlert(
    notification: AlertNotification,
  ): Promise<void> {
    // Send only to Slack for warnings
    const slackWebhook = this.configService.get('SLACK_WEBHOOK_URL');
    if (slackWebhook) {
      await this.sendSlackAlert(notification, slackWebhook);
    }
  }

  /**
   * Send Slack alert
   */
  private async sendSlackAlert(
    notification: AlertNotification,
    webhookUrl: string,
  ): Promise<void> {
    try {
      const color = this.getSeverityColor(notification.severity);

      await firstValueFrom(
        this.httpService.post(webhookUrl, {
          attachments: [
            {
              color,
              title: `🚨 ${notification.alertName}`,
              text: notification.message,
              fields: [
                {
                  title: 'Severity',
                  value: notification.severity,
                  short: true,
                },
                {
                  title: 'Time',
                  value: notification.timestamp.toISOString(),
                  short: true,
                },
                {
                  title: 'Value',
                  value: String(notification.value),
                  short: true,
                },
              ],
              footer: 'Luminar Monitoring',
              ts: Math.floor(notification.timestamp.getTime() / 1000),
            },
          ],
        }),
      );
    } catch (error) {
      this.logger.error('Failed to send Slack alert', error);
    }
  }

  /**
   * Send PagerDuty alert
   */
  private async sendPagerDutyAlert(
    notification: AlertNotification,
    routingKey: string,
  ): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.post('https://events.pagerduty.com/v2/enqueue', {
          routing_key: routingKey,
          event_action: 'trigger',
          payload: {
            summary: notification.message,
            severity:
              notification.severity === 'critical' ? 'error' : 'warning',
            source: 'luminar-monitoring',
            component: 'integration',
            custom_details: {
              alert_name: notification.alertName,
              value: notification.value,
              timestamp: notification.timestamp,
            },
          },
        }),
      );
    } catch (error) {
      this.logger.error('Failed to send PagerDuty alert', error);
    }
  }

  /**
   * Send email alert
   */
  private async sendEmailAlert(
    notification: AlertNotification,
    endpoint: string,
  ): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.post(endpoint, {
          to: this.configService.get(
            'ALERT_EMAIL_RECIPIENTS',
            '<EMAIL>',
          ),
          subject: `[${notification.severity.toUpperCase()}] ${notification.alertName}`,
          body: `
Alert: ${notification.alertName}
Severity: ${notification.severity}
Message: ${notification.message}
Value: ${notification.value}
Time: ${notification.timestamp.toISOString()}

Please check the monitoring dashboard for more details.
          `,
          priority: notification.severity === 'critical' ? 'high' : 'normal',
        }),
      );
    } catch (error) {
      this.logger.error('Failed to send email alert', error);
    }
  }

  /**
   * Get alert history
   */
  getAlertHistory(limit: number = 100): AlertNotification[] {
    return this.alertHistory.slice(-limit);
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.alerts.values());
  }

  /**
   * Create custom alert
   */
  createCustomAlert(alert: Alert): void {
    this.alerts.set(`custom-${Date.now()}`, alert);
  }

  /**
   * Remove alert
   */
  removeAlert(alertId: string): boolean {
    return this.alerts.delete(alertId);
  }

  // Helper methods

  private evaluateCondition(
    alert: Alert,
    metrics: Record<string, number>,
  ): boolean {
    // Simple condition evaluation - in production, use a proper expression parser
    const parts = alert.condition.split(' ');
    const metricName = parts[0];
    const operator = parts[1];
    const threshold = parseFloat(parts[2]);
    const value = metrics[metricName] || 0;

    switch (operator) {
      case '>':
        return value > threshold;
      case '<':
        return value < threshold;
      case '>=':
        return value >= threshold;
      case '<=':
        return value <= threshold;
      case '==':
        return value === threshold;
      default:
        return false;
    }
  }

  private formatMessage(
    template: string,
    metrics: Record<string, number>,
  ): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return String(metrics[key] || match);
    });
  }

  private extractMetricName(condition: string): string {
    return condition.split(' ')[0];
  }

  private getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical':
        return '#FF0000';
      case 'warning':
        return '#FFA500';
      case 'info':
        return '#0000FF';
      default:
        return '#808080';
    }
  }

  /**
   * Listen for specific events that should trigger alerts
   */
  @OnEvent('privacy.incident')
  async handlePrivacyIncident(event: any) {
    const notification: AlertNotification = {
      alertName: 'Privacy Incident',
      severity: 'critical',
      message: `Privacy incident: ${event.description}`,
      value: 1,
      timestamp: new Date(),
      metadata: event,
    };

    await this.sendAlert(notification);
  }
}
