import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { PrometheusController } from '@willsoto/nestjs-prometheus';
import { BusinessMetricsService } from './business-metrics.service';
import { MetricsExportService } from './metrics-export.service';

@ApiTags('Monitoring')
@Controller('api/monitoring')
export class MonitoringController extends PrometheusController {
  constructor(
    private readonly businessMetricsService: BusinessMetricsService,
    private readonly metricsExportService: MetricsExportService,
  ) {
    super();
  }

  /**
   * Get business insights
   */
  @Get('insights')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get business insights' })
  @ApiResponse({ status: 200, description: 'Business insights retrieved' })
  async getBusinessInsights() {
    return this.businessMetricsService.generateBusinessInsights();
  }

  /**
   * Export metrics
   */
  @Get('export')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Export metrics' })
  @ApiResponse({ status: 200, description: 'Metrics exported' })
  async exportMetrics(
    @Query('format') format: 'json' | 'csv' = 'json',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.metricsExportService.exportMetrics({
      format,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    });
  }

  /**
   * Get dashboard configuration
   */
  @Get('dashboards')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get dashboard configurations' })
  @ApiResponse({ status: 200, description: 'Dashboard configurations' })
  async getDashboards() {
    return {
      dashboards: [
        {
          id: 'integration-overview',
          name: 'AMNA Integration Overview',
          description: 'Overview of all integrations and their status',
          url: '/grafana/d/integration-overview',
        },
        {
          id: 'business-metrics',
          name: 'Business Metrics Dashboard',
          description: 'Key business metrics and KPIs',
          url: '/grafana/d/business-metrics',
        },
        {
          id: 'privacy-compliance',
          name: 'Privacy & Compliance Dashboard',
          description: 'GDPR compliance and privacy metrics',
          url: '/grafana/d/privacy-compliance',
        },
      ],
    };
  }

  /**
   * Health check endpoint for monitoring
   */
  @Get('health')
  @ApiOperation({ summary: 'Health check for monitoring system' })
  @ApiResponse({ status: 200, description: 'Monitoring system is healthy' })
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date(),
      metrics: {
        prometheus: 'active',
        grafana: 'configured',
        alerts: 'enabled',
      },
    };
  }
}
