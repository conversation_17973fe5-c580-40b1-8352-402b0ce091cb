import {
  IsString,
  IsEnum,
  IsObject,
  IsOptional,
  IsDate,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum IntegrationEventType {
  // Connection Events
  CONNECTION_ESTABLISHED = 'connection.established',
  CONNECTION_FAILED = 'connection.failed',
  CONNECTION_LOST = 'connection.lost',
  CONNECTION_RESTORED = 'connection.restored',

  // Data Events
  DATA_SYNCED = 'data.synced',
  DATA_SYNC_FAILED = 'data.sync_failed',
  DATA_TRANSFORMED = 'data.transformed',
  DATA_AGGREGATED = 'data.aggregated',

  // Integration Events
  INTEGRATION_ENABLED = 'integration.enabled',
  INTEGRATION_DISABLED = 'integration.disabled',
  INTEGRATION_ERROR = 'integration.error',
  INTEGRATION_WARNING = 'integration.warning',

  // Activity Events
  ACTIVITY_TRACKED = 'activity.tracked',
  ACTIVITY_AGGREGATED = 'activity.aggregated',

  // System Events
  HEALTH_CHECK_COMPLETED = 'health_check.completed',
  CIRCUIT_BREAKER_OPENED = 'circuit_breaker.opened',
  CIRCUIT_BREAKER_CLOSED = 'circuit_breaker.closed',
  RATE_LIMIT_EXCEEDED = 'rate_limit.exceeded',

  // Performance Events
  PERFORMANCE_DEGRADED = 'performance.degraded',
  PERFORMANCE_RESTORED = 'performance.restored',

  // Custom Events
  CUSTOM = 'custom',
}

export enum IntegrationEventPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export class EventMetadata {
  @ApiPropertyOptional({ description: 'User ID associated with the event' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: 'Session ID for tracking' })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({ description: 'Request ID for correlation' })
  @IsOptional()
  @IsString()
  requestId?: string;

  @ApiPropertyOptional({ description: 'IP address of the client' })
  @IsOptional()
  @IsString()
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'User agent string' })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({ description: 'Additional custom metadata' })
  @IsOptional()
  @IsObject()
  custom?: Record<string, any>;
}

export class IntegrationEventDto {
  @ApiProperty({ description: 'Unique event ID', example: 'evt_123456789' })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({
    enum: IntegrationEventType,
    description: 'Type of the integration event',
    example: IntegrationEventType.DATA_SYNCED,
  })
  @IsEnum(IntegrationEventType)
  type: IntegrationEventType;

  @ApiProperty({
    description: 'Source application or service',
    example: 'e-connect',
  })
  @IsString()
  source: string;

  @ApiPropertyOptional({
    description: 'Target application or service',
    example: 'amna',
  })
  @IsOptional()
  @IsString()
  target?: string;

  @ApiProperty({
    enum: IntegrationEventPriority,
    description: 'Event priority level',
    example: IntegrationEventPriority.MEDIUM,
  })
  @IsEnum(IntegrationEventPriority)
  @IsOptional()
  priority?: IntegrationEventPriority = IntegrationEventPriority.MEDIUM;

  @ApiProperty({ description: 'Event timestamp' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  timestamp?: Date = new Date();

  @ApiProperty({ description: 'Event data payload' })
  @IsObject()
  data: Record<string, any>;

  @ApiPropertyOptional({ description: 'Event metadata' })
  @IsOptional()
  @ValidateNested()
  @Type(() => EventMetadata)
  metadata?: EventMetadata;

  @ApiPropertyOptional({ description: 'Error details if applicable' })
  @IsOptional()
  @IsObject()
  error?: {
    message: string;
    code?: string;
    stack?: string;
    details?: Record<string, any>;
  };

  @ApiPropertyOptional({ description: 'Event tags for filtering' })
  @IsOptional()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Parent event ID for event chains' })
  @IsOptional()
  @IsString()
  parentId?: string;

  @ApiPropertyOptional({
    description: 'Correlation ID for tracking related events',
  })
  @IsOptional()
  @IsString()
  correlationId?: string;
}

export class BroadcastIntegrationEventDto extends IntegrationEventDto {
  @ApiProperty({
    description: 'Target user IDs for the broadcast',
    type: [String],
  })
  @IsString({ each: true })
  @IsOptional()
  targetUserIds?: string[];

  @ApiProperty({
    description: 'Target roles for the broadcast',
    type: [String],
  })
  @IsString({ each: true })
  @IsOptional()
  targetRoles?: string[];

  @ApiProperty({ description: 'Whether to persist the event', default: true })
  @IsOptional()
  persist?: boolean = true;

  @ApiProperty({ description: 'Whether to emit via WebSocket', default: true })
  @IsOptional()
  broadcast?: boolean = true;
}

export class IntegrationEventFilterDto {
  @ApiPropertyOptional({
    description: 'Filter by event type',
    enum: IntegrationEventType,
  })
  @IsOptional()
  @IsEnum(IntegrationEventType)
  type?: IntegrationEventType;

  @ApiPropertyOptional({ description: 'Filter by source' })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({ description: 'Filter by target' })
  @IsOptional()
  @IsString()
  target?: string;

  @ApiPropertyOptional({
    description: 'Filter by priority',
    enum: IntegrationEventPriority,
  })
  @IsOptional()
  @IsEnum(IntegrationEventPriority)
  priority?: IntegrationEventPriority;

  @ApiPropertyOptional({ description: 'Filter by user ID' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: 'Filter by correlation ID' })
  @IsOptional()
  @IsString()
  correlationId?: string;

  @ApiPropertyOptional({ description: 'Filter by tags', type: [String] })
  @IsOptional()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Start date for filtering' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @ApiPropertyOptional({ description: 'End date for filtering' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;
}

export class IntegrationEventResponseDto {
  @ApiProperty({ description: 'Event was successfully processed' })
  success: boolean;

  @ApiProperty({ description: 'Event ID' })
  eventId: string;

  @ApiPropertyOptional({ description: 'Processing timestamp' })
  processedAt?: Date;

  @ApiPropertyOptional({ description: 'Additional response data' })
  data?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Error message if processing failed' })
  error?: string;
}
