import { Injectable } from '@nestjs/common';
import { TracingService } from './tracing.service';
import { SpanKind } from '@opentelemetry/api';

@Injectable()
export class IntegrationTracingService {
  constructor(private readonly tracingService: TracingService) {}

  /**
   * Trace cross-application data flow
   */
  async traceCrossAppFlow<T>(
    source: string,
    destination: string,
    operation: string,
    fn: () => Promise<T>,
  ): Promise<T> {
    return this.tracingService.withSpan(
      `integration.${source}_to_${destination}.${operation}`,
      async (span) => {
        span.setAttributes({
          'integration.flow.source': source,
          'integration.flow.destination': destination,
          'integration.flow.operation': operation,
          'integration.flow.timestamp': new Date().toISOString(),
        });

        const startTime = Date.now();

        try {
          const result = await fn();
          const duration = Date.now() - startTime;

          span.setAttributes({
            'integration.flow.duration_ms': duration,
            'integration.flow.success': true,
          });

          span.addEvent('flow.completed', {
            source,
            destination,
            duration,
          });

          return result;
        } catch (error) {
          const duration = Date.now() - startTime;

          span.setAttributes({
            'integration.flow.duration_ms': duration,
            'integration.flow.success': false,
            'integration.flow.error': error.message,
          });

          span.addEvent('flow.failed', {
            source,
            destination,
            duration,
            error: error.message,
          });

          throw error;
        }
      },
      {
        kind: SpanKind.INTERNAL,
        attributes: {
          'integration.type': 'cross_app_flow',
        },
      },
    );
  }

  /**
   * Trace WebSocket events
   */
  traceWebSocketEvent(eventName: string, userId: string, data: any) {
    const span = this.tracingService.startSpan(`websocket.${eventName}`, {
      kind: SpanKind.PRODUCER,
      attributes: {
        'websocket.event': eventName,
        'websocket.user_id': userId,
        'websocket.data_size': JSON.stringify(data).length,
        'websocket.timestamp': new Date().toISOString(),
      },
    });

    try {
      span.addEvent('websocket.event.sent', {
        event: eventName,
        userId,
      });
      span.end();
    } catch (error) {
      span.recordException(error);
      span.end();
    }
  }

  /**
   * Trace activity processing
   */
  async traceActivityProcessing<T>(
    activityId: string,
    source: string,
    type: string,
    fn: () => Promise<T>,
  ): Promise<T> {
    return this.tracingService.withSpan(
      `activity.process.${source}.${type}`,
      async (span) => {
        span.setAttributes({
          'activity.id': activityId,
          'activity.source': source,
          'activity.type': type,
          'activity.start_time': new Date().toISOString(),
        });

        const startTime = Date.now();

        try {
          span.addEvent('activity.processing.started');

          const result = await fn();
          const duration = Date.now() - startTime;

          span.setAttributes({
            'activity.duration_ms': duration,
            'activity.status': 'completed',
          });

          span.addEvent('activity.processing.completed', {
            duration,
            activityId,
          });

          return result;
        } catch (error) {
          const duration = Date.now() - startTime;

          span.setAttributes({
            'activity.duration_ms': duration,
            'activity.status': 'failed',
            'activity.error': error.message,
          });

          span.addEvent('activity.processing.failed', {
            duration,
            activityId,
            error: error.message,
          });

          throw error;
        }
      },
      {
        kind: SpanKind.CONSUMER,
        attributes: {
          'integration.type': 'activity_processing',
        },
      },
    );
  }

  /**
   * Trace data federation operations
   */
  async traceDataFederation<T>(
    userId: string,
    applications: string[],
    fn: () => Promise<T>,
  ): Promise<T> {
    return this.tracingService.withSpan(
      'data.federation',
      async (span) => {
        span.setAttributes({
          'federation.user_id': userId,
          'federation.applications': applications.join(','),
          'federation.application_count': applications.length,
          'federation.timestamp': new Date().toISOString(),
        });

        const startTime = Date.now();
        const appMetrics = new Map<
          string,
          { duration: number; success: boolean }
        >();

        try {
          span.addEvent('federation.started', {
            applications: applications.length,
          });

          // Track individual application data fetches
          for (const app of applications) {
            const appStartTime = Date.now();
            try {
              span.addEvent(`federation.${app}.started`);
              // The actual fetch would happen in the fn()
              appMetrics.set(app, {
                duration: Date.now() - appStartTime,
                success: true,
              });
              span.addEvent(`federation.${app}.completed`);
            } catch (error) {
              appMetrics.set(app, {
                duration: Date.now() - appStartTime,
                success: false,
              });
              span.addEvent(`federation.${app}.failed`, {
                error: error.message,
              });
            }
          }

          const result = await fn();
          const totalDuration = Date.now() - startTime;

          span.setAttributes({
            'federation.total_duration_ms': totalDuration,
            'federation.success': true,
          });

          // Add metrics for each application
          appMetrics.forEach((metrics, app) => {
            span.setAttributes({
              [`federation.${app}.duration_ms`]: metrics.duration,
              [`federation.${app}.success`]: metrics.success,
            });
          });

          span.addEvent('federation.completed', {
            totalDuration,
            successfulApps: Array.from(appMetrics.entries())
              .filter(([_, m]) => m.success)
              .map(([app]) => app),
          });

          return result;
        } catch (error) {
          const totalDuration = Date.now() - startTime;

          span.setAttributes({
            'federation.total_duration_ms': totalDuration,
            'federation.success': false,
            'federation.error': error.message,
          });

          span.addEvent('federation.failed', {
            totalDuration,
            error: error.message,
          });

          throw error;
        }
      },
      {
        kind: SpanKind.INTERNAL,
        attributes: {
          'integration.type': 'data_federation',
        },
      },
    );
  }

  /**
   * Create trace context for async operations
   */
  createAsyncTraceContext(operationName: string): {
    traceId: string;
    spanId: string;
    headers: Record<string, string>;
  } {
    const span = this.tracingService.startSpan(`async.${operationName}`, {
      kind: SpanKind.PRODUCER,
    });

    const spanContext = span.spanContext();
    const headers = this.tracingService.injectContext({});

    span.end(); // End immediately as this is just for context creation

    return {
      traceId: spanContext.traceId,
      spanId: spanContext.spanId,
      headers,
    };
  }

  /**
   * Continue trace from async context
   */
  async continueAsyncTrace<T>(
    headers: Record<string, string>,
    operationName: string,
    fn: () => Promise<T>,
  ): Promise<T> {
    const extractedContext = this.tracingService.extractContext(headers);

    return this.tracingService.withSpan(
      `async.continue.${operationName}`,
      async (span) => {
        span.setAttributes({
          'async.operation': operationName,
          'async.continued': true,
        });

        return fn();
      },
      {
        kind: SpanKind.CONSUMER,
      },
    );
  }
}
