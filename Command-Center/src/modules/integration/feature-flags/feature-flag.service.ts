import { Injectable, Inject, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { FeatureFlagProvider } from './interfaces/feature-flag-provider.interface';
import {
  FeatureFlag,
  FeatureFlagUser,
  FeatureFlagValue,
  FeatureFlagEvaluation,
  FeatureFlagConfig,
} from './interfaces/feature-flag.interface';

@Injectable()
export class FeatureFlagService {
  private readonly logger = new Logger(FeatureFlagService.name);
  private readonly cache = new Map<string, FeatureFlagEvaluation>();
  private readonly cacheTTL = 60000; // 1 minute

  constructor(
    @Inject('FEATURE_FLAG_PROVIDER')
    private readonly provider: FeatureFlagProvider,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initialize();
  }

  private async initialize() {
    try {
      await this.provider.initialize();
      this.logger.log('Feature flag provider initialized');

      // Start polling for updates if supported
      if (this.provider.startPolling) {
        this.provider.startPolling((flags) => {
          this.handleFlagUpdates(flags);
        });
      }
    } catch (error) {
      this.logger.error('Failed to initialize feature flag provider', error);
    }
  }

  /**
   * Check if a feature is enabled for a user
   */
  async isEnabled(
    flagKey: string,
    user?: FeatureFlagUser,
    defaultValue: boolean = false,
  ): Promise<boolean> {
    try {
      const evaluation = await this.evaluate(flagKey, user, defaultValue);
      return evaluation.value as boolean;
    } catch (error) {
      this.logger.error(`Error evaluating flag ${flagKey}`, error);
      return defaultValue;
    }
  }

  /**
   * Get the value of a feature flag
   */
  async getValue<T extends FeatureFlagValue>(
    flagKey: string,
    user?: FeatureFlagUser,
    defaultValue?: T,
  ): Promise<T> {
    try {
      const evaluation = await this.evaluate(flagKey, user, defaultValue);
      return evaluation.value as T;
    } catch (error) {
      this.logger.error(`Error getting value for flag ${flagKey}`, error);
      return defaultValue as T;
    }
  }

  /**
   * Evaluate a feature flag with detailed information
   */
  async evaluate(
    flagKey: string,
    user?: FeatureFlagUser,
    defaultValue?: FeatureFlagValue,
  ): Promise<FeatureFlagEvaluation> {
    // Check cache first
    const cacheKey = this.getCacheKey(flagKey, user);
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached;
    }

    // Evaluate from provider
    const evaluation = await this.provider.evaluate(
      flagKey,
      user,
      defaultValue,
    );

    // Cache the result
    this.cache.set(cacheKey, {
      ...evaluation,
      timestamp: Date.now(),
    });

    // Emit evaluation event
    this.eventEmitter.emit('feature-flag.evaluated', {
      flagKey,
      user,
      evaluation,
    });

    // Track usage
    this.trackUsage(flagKey, user, evaluation);

    return evaluation;
  }

  /**
   * Get all feature flags for a user
   */
  async getAllFlags(
    user?: FeatureFlagUser,
  ): Promise<Record<string, FeatureFlagValue>> {
    try {
      return await this.provider.getAllFlags(user);
    } catch (error) {
      this.logger.error('Error getting all flags', error);
      return {};
    }
  }

  /**
   * Get specific integration feature flags
   */
  async getIntegrationFlags(
    user?: FeatureFlagUser,
  ): Promise<IntegrationFeatureFlags> {
    const flags = await this.getAllFlags(user);

    return {
      // Integration-specific flags
      enableEConnectIntegration:
        (flags['integration.e-connect.enabled'] as boolean) ?? true,
      enableLighthouseIntegration:
        (flags['integration.lighthouse.enabled'] as boolean) ?? true,
      enableTrainingIntegration:
        (flags['integration.training.enabled'] as boolean) ?? true,
      enableVendorsIntegration:
        (flags['integration.vendors.enabled'] as boolean) ?? true,
      enableWinsIntegration:
        (flags['integration.wins.enabled'] as boolean) ?? true,

      // Feature rollout flags
      enableRealtimeSync:
        (flags['integration.realtime-sync.enabled'] as boolean) ?? false,
      enableAdvancedAnalytics:
        (flags['integration.advanced-analytics.enabled'] as boolean) ?? false,
      enableAIInsights:
        (flags['integration.ai-insights.enabled'] as boolean) ?? false,
      enableCircuitBreaker:
        (flags['integration.circuit-breaker.enabled'] as boolean) ?? true,

      // Performance flags
      maxSyncBatchSize: (flags['integration.sync.batch-size'] as number) ?? 100,
      syncTimeoutMs: (flags['integration.sync.timeout'] as number) ?? 30000,
      cacheEnabled: (flags['integration.cache.enabled'] as boolean) ?? true,
      cacheTTLSeconds: (flags['integration.cache.ttl'] as number) ?? 300,

      // Rollout percentages
      rolloutPercentages: {
        eConnect: (flags['integration.e-connect.rollout'] as number) ?? 100,
        lighthouse: (flags['integration.lighthouse.rollout'] as number) ?? 100,
        training: (flags['integration.training.rollout'] as number) ?? 100,
        vendors: (flags['integration.vendors.rollout'] as number) ?? 100,
        wins: (flags['integration.wins.rollout'] as number) ?? 100,
      },
    };
  }

  /**
   * Update a feature flag (admin only)
   */
  async updateFlag(flagKey: string, config: FeatureFlagConfig): Promise<void> {
    if (!this.provider.updateFlag) {
      throw new Error('Feature flag provider does not support updates');
    }

    await this.provider.updateFlag(flagKey, config);

    // Clear cache for this flag
    this.clearFlagCache(flagKey);

    // Emit update event
    this.eventEmitter.emit('feature-flag.updated', {
      flagKey,
      config,
    });
  }

  /**
   * Create a new feature flag (admin only)
   */
  async createFlag(flag: FeatureFlag): Promise<void> {
    if (!this.provider.createFlag) {
      throw new Error('Feature flag provider does not support creation');
    }

    await this.provider.createFlag(flag);

    this.eventEmitter.emit('feature-flag.created', {
      flag,
    });
  }

  /**
   * Delete a feature flag (admin only)
   */
  async deleteFlag(flagKey: string): Promise<void> {
    if (!this.provider.deleteFlag) {
      throw new Error('Feature flag provider does not support deletion');
    }

    await this.provider.deleteFlag(flagKey);

    // Clear all cache entries for this flag
    this.clearFlagCache(flagKey);

    this.eventEmitter.emit('feature-flag.deleted', {
      flagKey,
    });
  }

  /**
   * Handle flag updates from provider
   */
  private handleFlagUpdates(flags: Record<string, FeatureFlagValue>) {
    // Clear cache for updated flags
    for (const flagKey of Object.keys(flags)) {
      this.clearFlagCache(flagKey);
    }

    this.eventEmitter.emit('feature-flags.updated', {
      flags,
      timestamp: new Date(),
    });
  }

  /**
   * Track feature flag usage
   */
  private trackUsage(
    flagKey: string,
    user: FeatureFlagUser | undefined,
    evaluation: FeatureFlagEvaluation,
  ) {
    this.eventEmitter.emit('feature-flag.usage', {
      flagKey,
      user,
      value: evaluation.value,
      variation: evaluation.variation,
      timestamp: new Date(),
    });
  }

  /**
   * Get cache key for a flag evaluation
   */
  private getCacheKey(flagKey: string, user?: FeatureFlagUser): string {
    if (!user) {
      return `flag:${flagKey}:default`;
    }
    return `flag:${flagKey}:user:${user.id}`;
  }

  /**
   * Clear cache for a specific flag
   */
  private clearFlagCache(flagKey: string) {
    const keysToDelete = Array.from(this.cache.keys()).filter((key) =>
      key.startsWith(`flag:${flagKey}:`),
    );

    keysToDelete.forEach((key) => this.cache.delete(key));
  }

  /**
   * Shutdown the service
   */
  async onModuleDestroy() {
    if (this.provider.close) {
      await this.provider.close();
    }
  }
}

/**
 * Integration-specific feature flags
 */
export interface IntegrationFeatureFlags {
  // Application toggles
  enableEConnectIntegration: boolean;
  enableLighthouseIntegration: boolean;
  enableTrainingIntegration: boolean;
  enableVendorsIntegration: boolean;
  enableWinsIntegration: boolean;

  // Feature toggles
  enableRealtimeSync: boolean;
  enableAdvancedAnalytics: boolean;
  enableAIInsights: boolean;
  enableCircuitBreaker: boolean;

  // Performance settings
  maxSyncBatchSize: number;
  syncTimeoutMs: number;
  cacheEnabled: boolean;
  cacheTTLSeconds: number;

  // Rollout percentages
  rolloutPercentages: {
    eConnect: number;
    lighthouse: number;
    training: number;
    vendors: number;
    wins: number;
  };
}
