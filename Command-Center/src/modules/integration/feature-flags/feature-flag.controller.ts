import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from '../guards/roles.guard';
import { Roles } from '../decorators/roles.decorator';
import { FeatureFlagService } from './feature-flag.service';
import {
  FeatureFlag,
  FeatureFlagConfig,
} from './interfaces/feature-flag.interface';

@ApiTags('Feature Flags')
@Controller('api/integration/feature-flags')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class FeatureFlagController {
  constructor(private readonly featureFlagService: FeatureFlagService) {}

  @Get()
  @ApiOperation({ summary: 'Get all feature flags for current user' })
  @ApiResponse({
    status: 200,
    description: 'Feature flags retrieved',
    schema: {
      type: 'object',
      additionalProperties: {
        oneOf: [
          { type: 'boolean' },
          { type: 'string' },
          { type: 'number' },
          { type: 'object' },
        ],
      },
    },
  })
  async getAllFlags(@Request() req: any) {
    const user = {
      id: req.user.id,
      email: req.user.email,
      roles: req.user.roles,
      attributes: req.user.attributes,
    };

    return this.featureFlagService.getAllFlags(user);
  }

  @Get('integration')
  @ApiOperation({ summary: 'Get integration-specific feature flags' })
  async getIntegrationFlags(@Request() req: any) {
    const user = {
      id: req.user.id,
      email: req.user.email,
      roles: req.user.roles,
    };

    return this.featureFlagService.getIntegrationFlags(user);
  }

  @Get(':flagKey')
  @ApiOperation({ summary: 'Evaluate a specific feature flag' })
  async evaluateFlag(@Param('flagKey') flagKey: string, @Request() req: any) {
    const user = {
      id: req.user.id,
      email: req.user.email,
      roles: req.user.roles,
    };

    const evaluation = await this.featureFlagService.evaluate(flagKey, user);

    return {
      flagKey,
      value: evaluation.value,
      variation: evaluation.variation,
      reason: evaluation.reason,
    };
  }

  @Post()
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new feature flag (Admin only)' })
  async createFlag(@Body() flag: FeatureFlag) {
    await this.featureFlagService.createFlag(flag);
    return { message: 'Feature flag created successfully' };
  }

  @Put(':flagKey')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Update a feature flag (Admin only)' })
  async updateFlag(
    @Param('flagKey') flagKey: string,
    @Body() config: FeatureFlagConfig,
  ) {
    await this.featureFlagService.updateFlag(flagKey, config);
    return { message: 'Feature flag updated successfully' };
  }

  @Delete(':flagKey')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Delete a feature flag (Admin only)' })
  async deleteFlag(@Param('flagKey') flagKey: string) {
    await this.featureFlagService.deleteFlag(flagKey);
    return { message: 'Feature flag deleted successfully' };
  }
}
