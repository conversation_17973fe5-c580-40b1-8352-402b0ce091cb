export type FeatureFlagValue = boolean | string | number | Record<string, any>;

export interface FeatureFlagUser {
  id: string;
  email?: string;
  name?: string;
  roles?: string[];
  groups?: string[];
  attributes?: Record<string, any>;
}

export interface FeatureFlag {
  key: string;
  name: string;
  description?: string;
  defaultValue: FeatureFlagValue;
  variations?: FeatureFlagVariation[];
  rules?: FeatureFlagRule[];
  targets?: FeatureFlagTarget[];
  rollout?: FeatureFlagRollout;
  tags?: string[];
  enabled: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface FeatureFlagVariation {
  id: string;
  value: FeatureFlagValue;
  name?: string;
  description?: string;
}

export interface FeatureFlagRule {
  id: string;
  clauses: FeatureFlagClause[];
  variation: string;
  rollout?: FeatureFlagRollout;
}

export interface FeatureFlagClause {
  attribute: string;
  operator: FeatureFlagOperator;
  values: any[];
  negate?: boolean;
}

export enum FeatureFlagOperator {
  IN = 'in',
  NOT_IN = 'notIn',
  EQUALS = 'equals',
  NOT_EQUALS = 'notEquals',
  GREATER_THAN = 'greaterThan',
  LESS_THAN = 'lessThan',
  CONTAINS = 'contains',
  STARTS_WITH = 'startsWith',
  ENDS_WITH = 'endsWith',
  MATCHES = 'matches',
  BEFORE = 'before',
  AFTER = 'after',
}

export interface FeatureFlagTarget {
  variation: string;
  users?: string[];
  groups?: string[];
}

export interface FeatureFlagRollout {
  variations: Array<{
    variation: string;
    weight: number;
  }>;
  bucketBy?: string;
}

export interface FeatureFlagEvaluation {
  value: FeatureFlagValue;
  variation?: string;
  reason?: FeatureFlagEvaluationReason;
  timestamp: number;
}

export interface FeatureFlagEvaluationReason {
  kind:
    | 'OFF'
    | 'FALLTHROUGH'
    | 'TARGET_MATCH'
    | 'RULE_MATCH'
    | 'PREREQUISITE_FAILED'
    | 'ERROR';
  ruleIndex?: number;
  ruleId?: string;
  prerequisiteKey?: string;
  errorKind?: string;
}

export interface FeatureFlagConfig {
  enabled?: boolean;
  defaultValue?: FeatureFlagValue;
  variations?: FeatureFlagVariation[];
  rules?: FeatureFlagRule[];
  targets?: FeatureFlagTarget[];
  rollout?: FeatureFlagRollout;
}
