import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/role.enum';
import {
  LearningPlatformsService,
  ExternalCourse,
  EnrollmentRequest,
} from '../common/external-apis/learning-platforms.service';
import { VendorIntelligenceService } from '../common/external-apis/vendor-intelligence.service';
import {
  CommunicationPlatformsService,
  NotificationRequest,
  WinsAnnouncement,
  TrainingNotification,
  VendorAlert,
} from '../common/external-apis/communication-platforms.service';
import { DataSynchronizationService } from '../common/external-apis/data-synchronization.service';
import { ErrorHandlingService } from '../common/external-apis/error-handling.service';

@ApiTags('External APIs')
@Controller('external-apis')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ExternalApisController {
  constructor(
    private readonly learningPlatformsService: LearningPlatformsService,
    private readonly vendorIntelligenceService: VendorIntelligenceService,
    private readonly communicationPlatformsService: CommunicationPlatformsService,
    private readonly dataSynchronizationService: DataSynchronizationService,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  // Learning Platforms Endpoints
  @Get('learning-platforms/courses/search')
  @ApiOperation({ summary: 'Search external learning platforms for courses' })
  @ApiResponse({ status: 200, description: 'Courses found successfully' })
  @Roles(Role.ADMIN, Role.MANAGER, Role.EMPLOYEE)
  async searchCourses(
    @Query('query') query: string,
    @Query('category') category?: string,
    @Query('level') level?: string,
    @Query('maxPrice') maxPrice?: number,
    @Query('provider') provider?: string,
    @Query('minRating') minRating?: number,
  ): Promise<ExternalCourse[]> {
    return this.learningPlatformsService.searchCourses(query, {
      category,
      level,
      maxPrice,
      provider,
      minRating,
    });
  }

  @Post('learning-platforms/courses/enroll')
  @ApiOperation({ summary: 'Enroll user in external course' })
  @ApiResponse({ status: 201, description: 'Enrollment successful' })
  @Roles(Role.ADMIN, Role.MANAGER, Role.EMPLOYEE)
  async enrollInCourse(@Body() enrollmentRequest: EnrollmentRequest) {
    return this.learningPlatformsService.enrollInCourse(enrollmentRequest);
  }

  // Vendor Intelligence Endpoints
  @Get('vendor-intelligence/risk-assessment/:vendorId')
  @ApiOperation({ summary: 'Get vendor risk assessment' })
  @ApiResponse({
    status: 200,
    description: 'Risk assessment retrieved successfully',
  })
  @Roles(Role.ADMIN, Role.MANAGER)
  async getVendorRiskAssessment(
    @Param('vendorId') vendorId: string,
    @Query('taxId') taxId?: string,
    @Query('companyName') companyName?: string,
  ) {
    return this.vendorIntelligenceService.getVendorRiskAssessment(
      vendorId,
      taxId,
      companyName,
    );
  }

  @Get('vendor-intelligence/financial/:vendorId')
  @ApiOperation({ summary: 'Get vendor financial data' })
  @ApiResponse({
    status: 200,
    description: 'Financial data retrieved successfully',
  })
  @Roles(Role.ADMIN, Role.MANAGER)
  async getVendorFinancialData(
    @Param('vendorId') vendorId: string,
    @Query('taxId') taxId?: string,
  ) {
    return this.vendorIntelligenceService.getVendorFinancialData(
      vendorId,
      taxId,
    );
  }

  @Get('vendor-intelligence/compliance/:vendorId')
  @ApiOperation({ summary: 'Get vendor compliance data' })
  @ApiResponse({
    status: 200,
    description: 'Compliance data retrieved successfully',
  })
  @Roles(Role.ADMIN, Role.MANAGER)
  async getVendorComplianceData(@Param('vendorId') vendorId: string) {
    return this.vendorIntelligenceService.getVendorComplianceData(vendorId);
  }

  @Get('vendor-intelligence/market/:vendorId')
  @ApiOperation({ summary: 'Get vendor market data' })
  @ApiResponse({
    status: 200,
    description: 'Market data retrieved successfully',
  })
  @Roles(Role.ADMIN, Role.MANAGER)
  async getVendorMarketData(@Param('vendorId') vendorId: string) {
    return this.vendorIntelligenceService.getVendorMarketData(vendorId);
  }

  // Communication Platforms Endpoints
  @Post('communication/notifications')
  @ApiOperation({ summary: 'Send notification via communication platforms' })
  @ApiResponse({ status: 201, description: 'Notification sent successfully' })
  @Roles(Role.ADMIN, Role.MANAGER)
  async sendNotification(@Body() notificationRequest: NotificationRequest) {
    return this.communicationPlatformsService.sendNotification(
      notificationRequest,
    );
  }

  @Post('communication/wins/announce')
  @ApiOperation({ summary: 'Announce a win across communication platforms' })
  @ApiResponse({ status: 201, description: 'Win announced successfully' })
  @Roles(Role.ADMIN, Role.MANAGER)
  async announceWin(
    @Body() announcement: WinsAnnouncement,
    @Query('channels') channels?: string[],
  ) {
    return this.communicationPlatformsService.announceWin(
      announcement,
      channels,
    );
  }

  @Post('communication/training/notify')
  @ApiOperation({ summary: 'Send training notification' })
  @ApiResponse({
    status: 201,
    description: 'Training notification sent successfully',
  })
  @Roles(Role.ADMIN, Role.MANAGER)
  async sendTrainingNotification(
    @Body() notification: TrainingNotification,
    @Body('recipients') recipients: string[],
    @Query('channels') channels?: string[],
  ) {
    return this.communicationPlatformsService.sendTrainingNotification(
      notification,
      recipients,
      channels,
    );
  }

  @Post('communication/vendor/alert')
  @ApiOperation({ summary: 'Send vendor alert' })
  @ApiResponse({ status: 201, description: 'Vendor alert sent successfully' })
  @Roles(Role.ADMIN, Role.MANAGER)
  async sendVendorAlert(
    @Body() alert: VendorAlert,
    @Body('recipients') recipients: string[],
    @Query('channels') channels?: string[],
  ) {
    return this.communicationPlatformsService.sendVendorAlert(
      alert,
      recipients,
      channels,
    );
  }

  // Data Synchronization Endpoints
  @Get('sync/jobs')
  @ApiOperation({ summary: 'Get all sync jobs' })
  @ApiResponse({ status: 200, description: 'Sync jobs retrieved successfully' })
  @Roles(Role.ADMIN)
  async getSyncJobs() {
    return this.dataSynchronizationService.getSyncJobs();
  }

  @Get('sync/jobs/:jobId')
  @ApiOperation({ summary: 'Get specific sync job' })
  @ApiResponse({ status: 200, description: 'Sync job retrieved successfully' })
  @Roles(Role.ADMIN)
  async getSyncJob(@Param('jobId') jobId: string) {
    return this.dataSynchronizationService.getSyncJob(jobId);
  }

  @Post('sync/jobs/:jobId/run')
  @ApiOperation({ summary: 'Run sync job manually' })
  @ApiResponse({ status: 200, description: 'Sync job executed successfully' })
  @Roles(Role.ADMIN)
  async runSyncJob(@Param('jobId') jobId: string) {
    return this.dataSynchronizationService.runSyncJob(jobId);
  }

  @Get('sync/jobs/:jobId/results')
  @ApiOperation({ summary: 'Get sync job results' })
  @ApiResponse({
    status: 200,
    description: 'Sync job results retrieved successfully',
  })
  @Roles(Role.ADMIN)
  async getSyncJobResults(@Param('jobId') jobId: string) {
    return this.dataSynchronizationService.getSyncJobResults(jobId);
  }

  // Error Handling Endpoints
  @Get('errors/:service')
  @ApiOperation({ summary: 'Get service errors' })
  @ApiResponse({
    status: 200,
    description: 'Service errors retrieved successfully',
  })
  @Roles(Role.ADMIN)
  async getServiceErrors(
    @Param('service') service: string,
    @Query('limit') limit?: number,
  ) {
    return this.errorHandlingService.getServiceErrors(service, limit);
  }

  @Get('health/:service')
  @ApiOperation({ summary: 'Get service health status' })
  @ApiResponse({
    status: 200,
    description: 'Service health retrieved successfully',
  })
  @Roles(Role.ADMIN, Role.MANAGER)
  async getServiceHealth(@Param('service') service: string) {
    return this.errorHandlingService.getServiceHealth(service);
  }

  @Post('errors/:errorId/resolve')
  @ApiOperation({ summary: 'Mark error as resolved' })
  @ApiResponse({ status: 200, description: 'Error marked as resolved' })
  @Roles(Role.ADMIN)
  async resolveError(@Param('errorId') errorId: string) {
    return this.errorHandlingService.resolveError(errorId);
  }

  // Health Check Endpoints
  @Get('health')
  @ApiOperation({ summary: 'Get overall external APIs health' })
  @ApiResponse({
    status: 200,
    description: 'Health status retrieved successfully',
  })
  @Roles(Role.ADMIN, Role.MANAGER)
  async getOverallHealth() {
    const services = [
      'learning-platforms',
      'vendor-intelligence',
      'communication-platforms',
      'data-synchronization',
    ];

    const healthChecks = await Promise.all(
      services.map(async (service) => ({
        service,
        ...(await this.errorHandlingService.getServiceHealth(service)),
      })),
    );

    const overall = healthChecks.every((check) => check.status === 'healthy')
      ? 'healthy'
      : healthChecks.some((check) => check.status === 'unhealthy')
        ? 'unhealthy'
        : 'degraded';

    return {
      overall,
      services: healthChecks,
      timestamp: new Date().toISOString(),
    };
  }
}
