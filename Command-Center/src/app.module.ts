import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CommonModule } from './common/common.module';
import { LAndDModule } from './modules/l-and-d/l-and-d.module';
import { NotificationModule } from './modules/notifications/notification.module';
import { SyncModule } from './modules/sync/sync.module';
import { HealthModule } from './modules/health/health.module';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { FilesModule } from './modules/files/files.module';
import { RedisModule } from './modules/redis/redis.module';
import { EnhancedCacheModule } from './modules/cache/cache.module';
import { QueueModule } from './modules/queue/queue.module';
import { VectorModule } from './modules/vector/vector.module';
import { PrismaModule } from './database/prisma/prisma.module';
import { TypeOrmConfigService } from './database/typeorm/typeorm-config.service';
import { PerformanceModule } from './modules/performance/performance.module';
import { ScalabilityModule } from './modules/scalability/scalability.module';
import { TestingModule } from './modules/testing/testing.module';
import { DeploymentModule } from './modules/deployment/deployment.module';
import { AmnaModule } from './modules/amna/amna.module';
import { TrainingModule } from './modules/training/training.module';
import { VendorModule } from './modules/vendor/vendor.module';
import { WinsModule } from './modules/wins/wins.module';
import { EmailModule } from './modules/email/email.module';
import { SystemMonitoringModule } from './modules/system-monitoring/system-monitoring.module';
import configuration from './config/configuration';
import { getDatabaseConfig } from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useClass: TypeOrmConfigService,
    }),
    PrismaModule,
    RedisModule,
    EnhancedCacheModule,
    QueueModule,
    VectorModule,
    PerformanceModule,
    ScalabilityModule,
    TestingModule,
    DeploymentModule,
    CommonModule,
    LAndDModule,
    NotificationModule,
    SyncModule,
    HealthModule,
    UsersModule,
    AuthModule,
    DocumentsModule,
    FilesModule,
    AmnaModule,
    TrainingModule,
    VendorModule,
    WinsModule,
    EmailModule,
    SystemMonitoringModule,
  ],
  controllers: [AppController],
  providers: [AppService, TypeOrmConfigService],
})
export class AppModule {}
