# Application
NODE_ENV=development
PORT=3000
APP_NAME=command-center
CORS_ORIGIN=http://localhost:3000

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres123!
DATABASE_NAME=command_center

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=30d
JWT_ISSUER=command-center
JWT_AUDIENCE=command-center-api

# Security
SESSION_SECRET=your-session-secret-change-in-production
FILE_ENCRYPTION_KEY=your-256-bit-hex-key-here
DEV_USERNAME=developer
DEV_PASSWORD=dev123456

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Qdrant Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=
QDRANT_TIMEOUT=30000

# OpenAI (for embeddings)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=8191

# HuggingFace (alternative embedding provider)
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# Embeddings Configuration
EMBEDDINGS_PROVIDER=openai
EMBEDDINGS_DIMENSIONS=1536
EMBEDDINGS_BATCH_SIZE=100

# File Storage Configuration
FILE_STORAGE_PROVIDER=local
MAX_FILE_SIZE=104857600
LOCAL_UPLOAD_PATH=./uploads
LOCAL_PUBLIC_PATH=./public
LOCAL_THUMBNAIL_PATH=./uploads/thumbnails
LOCAL_TEMP_PATH=./uploads/temp

# MinIO Configuration
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=files
MINIO_REGION=us-east-1

# File Processing Configuration
ENABLE_IMAGE_PROCESSING=true
ENABLE_VIDEO_PROCESSING=true
ENABLE_DOCUMENT_PROCESSING=true
ENABLE_AUDIO_PROCESSING=true
ENABLE_VIRUS_SCANNING=true

# Image Processing
IMAGE_QUALITY=85
IMAGE_FORMAT=jpeg
STRIP_IMAGE_METADATA=true

# Video Processing
VIDEO_THUMBNAIL_COUNT=3
VIDEO_THUMBNAIL_INTERVAL=10
MAX_VIDEO_DURATION=3600
VIDEO_COMPRESSION_LEVEL=medium
VIDEO_OUTPUT_FORMATS=mp4

# Document Processing
ENABLE_OCR=true
ENABLE_TEXT_EXTRACTION=true
PDF_COMPRESSION_LEVEL=medium

# Audio Processing
ENABLE_AUDIO_TRANSCRIPTION=false
AUDIO_OUTPUT_FORMATS=mp3
AUDIO_BITRATE=128k

# Virus Scanning
QUARANTINE_ON_VIRUS_DETECTION=true
DELETE_ON_VIRUS_DETECTION=false
VIRUS_SCAN_TIMEOUT=30000

# Elasticsearch
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=changeme
ELASTICSEARCH_INDEX_PREFIX=command-center

# RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=admin
RABBITMQ_PASSWORD=admin
RABBITMQ_VHOST=command-center
RABBITMQ_QUEUE_PREFIX=cc

# Jaeger Tracing
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6831
JAEGER_SERVICE_NAME=command-center
JAEGER_SAMPLER_TYPE=const
JAEGER_SAMPLER_PARAM=1

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9464
ENABLE_TRACING=true
ENABLE_LOGGING=true
LOG_LEVEL=info

# Grafana
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin

# Development Tools
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin

# Ollama LLM Configuration
OLLAMA_HOST=localhost
OLLAMA_PORT=11434
OLLAMA_MODEL=llama2

# Email Configuration (for notifications)
EMAIL_ENABLED=false
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Gmail OAuth2 Configuration (for email integration)
GMAIL_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GMAIL_CLIENT_SECRET=your-google-client-secret
GMAIL_REDIRECT_URI=http://localhost:5174/oauth-callback

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 3 * * *
BACKUP_RETENTION_DAYS=7
AWS_S3_BUCKET=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1