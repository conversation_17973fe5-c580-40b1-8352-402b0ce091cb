import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Clean existing data (be careful with this in production)
  console.log('🧹 Cleaning existing data...')
  await prisma.workflowExecution.deleteMany()
  await prisma.workflow.deleteMany()
  await prisma.trainingEnrollment.deleteMany()
  await prisma.trainingRecommendation.deleteMany()
  await prisma.skillGap.deleteMany()
  await prisma.assessmentResponse.deleteMany()
  await prisma.assessment.deleteMany()
  await prisma.userSkill.deleteMany()
  await prisma.learningActivity.deleteMany()
  await prisma.weeklySubmission.deleteMany()
  await prisma.budgetAllocation.deleteMany()
  await prisma.review.deleteMany()
  await prisma.proposal.deleteMany()
  await prisma.vendor.deleteMany()
  await prisma.user.deleteMany()
  await prisma.trainingCourse.deleteMany()
  await prisma.learningPath.deleteMany()
  await prisma.skill.deleteMany()
  await prisma.role.deleteMany()
  await prisma.department.deleteMany()
  await prisma.competencyFramework.deleteMany()
  await prisma.apiKey.deleteMany()
  await prisma.featureFlag.deleteMany()
  await prisma.report.deleteMany()
  await prisma.notification.deleteMany()
  await prisma.auditLog.deleteMany()
  await prisma.searchIndex.deleteMany()
  await prisma.analytics.deleteMany()

  // 1. Create Departments
  console.log('👥 Creating departments...')
  const departments = await Promise.all([
    prisma.department.create({
      data: {
        name: 'Engineering',
        manager: 'John Smith',
        employeeCount: 25,
      },
    }),
    prisma.department.create({
      data: {
        name: 'Product Management',
        manager: 'Sarah Johnson',
        employeeCount: 8,
      },
    }),
    prisma.department.create({
      data: {
        name: 'Marketing',
        manager: 'Michael Brown',
        employeeCount: 12,
      },
    }),
    prisma.department.create({
      data: {
        name: 'Sales',
        manager: 'Lisa Davis',
        employeeCount: 15,
      },
    }),
    prisma.department.create({
      data: {
        name: 'Human Resources',
        manager: 'David Wilson',
        employeeCount: 6,
      },
    }),
  ])

  // 2. Create Skills
  console.log('🎯 Creating skills...')
  const skills = await Promise.all([
    // Technical Skills
    prisma.skill.create({
      data: {
        name: 'JavaScript',
        category: 'Programming',
        description: 'JavaScript programming language proficiency',
        levels: [
          { level: 1, name: 'Beginner', description: 'Basic syntax and concepts' },
          { level: 2, name: 'Intermediate', description: 'ES6+, async/await, DOM manipulation' },
          { level: 3, name: 'Advanced', description: 'Complex patterns, performance optimization' },
          { level: 4, name: 'Expert', description: 'Language internals, engine optimization' },
        ],
      },
    }),
    prisma.skill.create({
      data: {
        name: 'TypeScript',
        category: 'Programming',
        description: 'TypeScript programming language proficiency',
        levels: [
          { level: 1, name: 'Beginner', description: 'Basic types and interfaces' },
          { level: 2, name: 'Intermediate', description: 'Generics, utility types' },
          { level: 3, name: 'Advanced', description: 'Advanced types, decorators' },
          { level: 4, name: 'Expert', description: 'Compiler API, type gymnastics' },
        ],
      },
    }),
    prisma.skill.create({
      data: {
        name: 'React',
        category: 'Frontend',
        description: 'React.js library proficiency',
        levels: [
          { level: 1, name: 'Beginner', description: 'Components, props, state' },
          { level: 2, name: 'Intermediate', description: 'Hooks, context, lifecycle' },
          { level: 3, name: 'Advanced', description: 'Performance optimization, patterns' },
          { level: 4, name: 'Expert', description: 'Internals, custom hooks, testing' },
        ],
      },
    }),
    prisma.skill.create({
      data: {
        name: 'Node.js',
        category: 'Backend',
        description: 'Node.js runtime proficiency',
        levels: [
          { level: 1, name: 'Beginner', description: 'Basic API development' },
          { level: 2, name: 'Intermediate', description: 'Express, middleware, async' },
          { level: 3, name: 'Advanced', description: 'Performance, streams, clusters' },
          { level: 4, name: 'Expert', description: 'Event loop, C++ addons' },
        ],
      },
    }),
    // Soft Skills
    prisma.skill.create({
      data: {
        name: 'Leadership',
        category: 'Soft Skills',
        description: 'Team leadership and management skills',
        levels: [
          { level: 1, name: 'Beginner', description: 'Basic delegation and guidance' },
          { level: 2, name: 'Intermediate', description: 'Team motivation and conflict resolution' },
          { level: 3, name: 'Advanced', description: 'Strategic thinking and vision setting' },
          { level: 4, name: 'Expert', description: 'Organizational transformation' },
        ],
      },
    }),
    prisma.skill.create({
      data: {
        name: 'Communication',
        category: 'Soft Skills',
        description: 'Verbal and written communication skills',
        levels: [
          { level: 1, name: 'Beginner', description: 'Clear basic communication' },
          { level: 2, name: 'Intermediate', description: 'Presentation and documentation' },
          { level: 3, name: 'Advanced', description: 'Stakeholder management' },
          { level: 4, name: 'Expert', description: 'Public speaking and thought leadership' },
        ],
      },
    }),
  ])

  // 3. Create Roles
  console.log('💼 Creating roles...')
  const roles = await Promise.all([
    // Engineering Roles
    prisma.role.create({
      data: {
        title: 'Junior Software Engineer',
        departmentId: departments[0].id,
        level: 'entry',
        requiredSkills: ['JavaScript', 'Basic Programming'],
      },
    }),
    prisma.role.create({
      data: {
        title: 'Senior Software Engineer',
        departmentId: departments[0].id,
        level: 'senior',
        requiredSkills: ['JavaScript', 'TypeScript', 'React', 'Node.js'],
      },
    }),
    prisma.role.create({
      data: {
        title: 'Engineering Manager',
        departmentId: departments[0].id,
        level: 'lead',
        requiredSkills: ['Leadership', 'Technical Architecture', 'Team Management'],
      },
    }),
    // Product Management Roles
    prisma.role.create({
      data: {
        title: 'Product Manager',
        departmentId: departments[1].id,
        level: 'mid',
        requiredSkills: ['Product Strategy', 'User Research', 'Data Analysis'],
      },
    }),
    prisma.role.create({
      data: {
        title: 'Senior Product Manager',
        departmentId: departments[1].id,
        level: 'senior',
        requiredSkills: ['Product Strategy', 'Leadership', 'Market Analysis'],
      },
    }),
    // Marketing Roles
    prisma.role.create({
      data: {
        title: 'Marketing Specialist',
        departmentId: departments[2].id,
        level: 'entry',
        requiredSkills: ['Digital Marketing', 'Content Creation'],
      },
    }),
    prisma.role.create({
      data: {
        title: 'Marketing Manager',
        departmentId: departments[2].id,
        level: 'mid',
        requiredSkills: ['Digital Marketing', 'Leadership', 'Strategy'],
      },
    }),
  ])

  // 4. Create Users
  console.log('👤 Creating users...')
  const users = await Promise.all([
    // Engineering Team
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Alice Johnson',
        departmentId: departments[0].id,
        roleId: roles[1].id, // Senior Software Engineer
        status: 'active',
        joinDate: new Date('2022-01-15'),
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Bob Smith',
        departmentId: departments[0].id,
        roleId: roles[0].id, // Junior Software Engineer
        status: 'active',
        joinDate: new Date('2023-06-01'),
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Charlie Brown',
        departmentId: departments[0].id,
        roleId: roles[2].id, // Engineering Manager
        status: 'active',
        joinDate: new Date('2021-03-10'),
      },
    }),
    // Product Team
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Diana Prince',
        departmentId: departments[1].id,
        roleId: roles[3].id, // Product Manager
        status: 'active',
        joinDate: new Date('2022-08-20'),
      },
    }),
    // Marketing Team
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Eve Adams',
        departmentId: departments[2].id,
        roleId: roles[5].id, // Marketing Specialist
        status: 'active',
        joinDate: new Date('2023-02-14'),
      },
    }),
  ])

  // Set up manager relationships
  await prisma.user.update({
    where: { id: users[1].id }, // Bob (Junior)
    data: { managerId: users[0].id }, // Alice (Senior)
  })
  
  await prisma.user.update({
    where: { id: users[0].id }, // Alice (Senior)
    data: { managerId: users[2].id }, // Charlie (Manager)
  })

  // 5. Create User Skills
  console.log('🎯 Creating user skills...')
  await Promise.all([
    // Alice's skills (Senior Engineer)
    prisma.userSkill.create({
      data: {
        userId: users[0].id,
        skillId: skills[0].id, // JavaScript
        currentLevel: 3,
        assessedDate: new Date('2024-01-15'),
        source: 'manager-review',
      },
    }),
    prisma.userSkill.create({
      data: {
        userId: users[0].id,
        skillId: skills[1].id, // TypeScript
        currentLevel: 3,
        assessedDate: new Date('2024-01-15'),
        source: 'manager-review',
      },
    }),
    prisma.userSkill.create({
      data: {
        userId: users[0].id,
        skillId: skills[2].id, // React
        currentLevel: 4,
        assessedDate: new Date('2024-01-15'),
        source: 'peer-review',
      },
    }),
    // Bob's skills (Junior Engineer)
    prisma.userSkill.create({
      data: {
        userId: users[1].id,
        skillId: skills[0].id, // JavaScript
        currentLevel: 2,
        assessedDate: new Date('2024-01-10'),
        source: 'self-assessment',
      },
    }),
    prisma.userSkill.create({
      data: {
        userId: users[1].id,
        skillId: skills[1].id, // TypeScript
        currentLevel: 1,
        assessedDate: new Date('2024-01-10'),
        source: 'self-assessment',
      },
    }),
    // Charlie's skills (Manager)
    prisma.userSkill.create({
      data: {
        userId: users[2].id,
        skillId: skills[4].id, // Leadership
        currentLevel: 3,
        assessedDate: new Date('2024-01-05'),
        source: '360-review',
      },
    }),
    prisma.userSkill.create({
      data: {
        userId: users[2].id,
        skillId: skills[5].id, // Communication
        currentLevel: 4,
        assessedDate: new Date('2024-01-05'),
        source: '360-review',
      },
    }),
  ])

  // 6. Create Training Courses
  console.log('📚 Creating training courses...')
  const courses = await Promise.all([
    prisma.trainingCourse.create({
      data: {
        title: 'Advanced JavaScript Patterns',
        description: 'Master advanced JavaScript patterns and best practices',
        provider: 'TechEd Solutions',
        type: 'online',
        duration: 20,
        cost: 299.99,
        capacity: 50,
        prerequisites: ['Basic JavaScript knowledge'],
        difficulty: 'advanced',
        rating: 4.7,
        enrollments: 15,
        status: 'active',
        skills: {
          connect: [{ id: skills[0].id }], // JavaScript
        },
      },
    }),
    prisma.trainingCourse.create({
      data: {
        title: 'TypeScript Fundamentals',
        description: 'Learn TypeScript from basics to advanced concepts',
        provider: 'CodeAcademy Pro',
        type: 'online',
        duration: 15,
        cost: 199.99,
        capacity: 100,
        prerequisites: ['JavaScript basics'],
        difficulty: 'intermediate',
        rating: 4.5,
        enrollments: 32,
        status: 'active',
        skills: {
          connect: [{ id: skills[1].id }], // TypeScript
        },
      },
    }),
    prisma.trainingCourse.create({
      data: {
        title: 'React Performance Optimization',
        description: 'Advanced React performance optimization techniques',
        provider: 'Frontend Masters',
        type: 'online',
        duration: 12,
        cost: 250.00,
        capacity: 30,
        prerequisites: ['React intermediate knowledge'],
        difficulty: 'advanced',
        rating: 4.9,
        enrollments: 8,
        status: 'active',
        skills: {
          connect: [{ id: skills[2].id }], // React
        },
      },
    }),
    prisma.trainingCourse.create({
      data: {
        title: 'Leadership Essentials',
        description: 'Essential leadership skills for new managers',
        provider: 'Leadership Institute',
        type: 'workshop',
        duration: 16,
        cost: 450.00,
        capacity: 20,
        prerequisites: [],
        difficulty: 'intermediate',
        rating: 4.6,
        enrollments: 12,
        status: 'active',
        skills: {
          connect: [{ id: skills[4].id }], // Leadership
        },
      },
    }),
  ])

  // 7. Create Learning Paths
  console.log('🛤️ Creating learning paths...')
  const learningPaths = await Promise.all([
    prisma.learningPath.create({
      data: {
        title: 'Frontend Engineer Career Path',
        description: 'Complete path from junior to senior frontend engineer',
        roleId: roles[1].id, // Senior Software Engineer
        estimatedDuration: 120,
        difficulty: 'intermediate',
        completionCriteria: ['Complete all courses', 'Pass final assessment', 'Complete project'],
        courses: {
          connect: [
            { id: courses[0].id }, // Advanced JavaScript
            { id: courses[1].id }, // TypeScript
            { id: courses[2].id }, // React Performance
          ],
        },
        skillsAcquired: {
          connect: [
            { id: skills[0].id }, // JavaScript
            { id: skills[1].id }, // TypeScript
            { id: skills[2].id }, // React
          ],
        },
      },
    }),
    prisma.learningPath.create({
      data: {
        title: 'Engineering Leadership Path',
        description: 'Transition from IC to engineering leadership',
        roleId: roles[2].id, // Engineering Manager
        estimatedDuration: 80,
        difficulty: 'advanced',
        completionCriteria: ['Complete leadership training', 'Mentorship program', '360 feedback'],
        courses: {
          connect: [{ id: courses[3].id }], // Leadership Essentials
        },
        skillsAcquired: {
          connect: [
            { id: skills[4].id }, // Leadership
            { id: skills[5].id }, // Communication
          ],
        },
      },
    }),
  ])

  // 8. Create Skill Gaps
  console.log('📊 Creating skill gaps...')
  await Promise.all([
    // Bob needs to improve TypeScript
    prisma.skillGap.create({
      data: {
        userId: users[1].id, // Bob
        skillId: skills[1].id, // TypeScript
        currentLevel: 1,
        requiredLevel: 3,
        gap: 2,
        priority: 'high',
        impact: 'high',
        urgency: 'short-term',
      },
    }),
    // Bob needs to learn React
    prisma.skillGap.create({
      data: {
        userId: users[1].id, // Bob
        skillId: skills[2].id, // React
        currentLevel: 0,
        requiredLevel: 2,
        gap: 2,
        priority: 'critical',
        impact: 'high',
        urgency: 'immediate',
      },
    }),
  ])

  // 9. Create Training Recommendations
  console.log('💡 Creating training recommendations...')
  const skillGaps = await prisma.skillGap.findMany()
  await Promise.all([
    prisma.trainingRecommendation.create({
      data: {
        userId: users[1].id, // Bob
        courseId: courses[1].id, // TypeScript Fundamentals
        skillGapId: skillGaps[0].id,
        priority: 1,
        reasoning: 'TypeScript is essential for modern development and will significantly improve code quality',
        expectedImpact: 'high',
        estimatedCompletion: new Date('2024-03-15'),
        status: 'pending',
      },
    }),
  ])

  // 10. Create Assessments
  console.log('📝 Creating assessments...')
  const assessments = await Promise.all([
    prisma.assessment.create({
      data: {
        title: 'Q1 2024 Technical Skills Assessment',
        description: 'Quarterly assessment for engineering team technical skills',
        departmentId: departments[0].id, // Engineering
        createdBy: users[2].id, // Charlie (Manager)
        status: 'active',
        skillsAssessed: ['JavaScript', 'TypeScript', 'React', 'Node.js'],
        dueDate: new Date('2024-03-31'),
      },
    }),
    prisma.assessment.create({
      data: {
        title: 'Leadership 360 Review',
        description: '360-degree leadership assessment for managers',
        roleId: roles[2].id, // Engineering Manager
        createdBy: users[2].id, // Charlie
        status: 'completed',
        skillsAssessed: ['Leadership', 'Communication', 'Decision Making'],
        dueDate: new Date('2024-02-15'),
      },
    }),
  ])

  // 11. Create Assessment Responses
  console.log('✅ Creating assessment responses...')
  await Promise.all([
    prisma.assessmentResponse.create({
      data: {
        assessmentId: assessments[0].id,
        userId: users[0].id, // Alice
        completedDate: new Date('2024-01-20'),
        status: 'completed',
        skillRatings: [
          { skillName: 'JavaScript', rating: 4, comments: 'Strong expertise in modern JS' },
          { skillName: 'TypeScript', rating: 4, comments: 'Excellent type design skills' },
          { skillName: 'React', rating: 5, comments: 'Outstanding React knowledge' },
          { skillName: 'Node.js', rating: 3, comments: 'Good backend development skills' },
        ],
      },
    }),
    prisma.assessmentResponse.create({
      data: {
        assessmentId: assessments[0].id,
        userId: users[1].id, // Bob
        completedDate: new Date('2024-01-25'),
        status: 'completed',
        skillRatings: [
          { skillName: 'JavaScript', rating: 3, comments: 'Solid foundation, needs advanced concepts' },
          { skillName: 'TypeScript', rating: 2, comments: 'Basic understanding, needs improvement' },
          { skillName: 'React', rating: 1, comments: 'Very basic, requires significant training' },
          { skillName: 'Node.js', rating: 2, comments: 'Limited backend experience' },
        ],
      },
    }),
  ])

  // 12. Create Training Enrollments
  console.log('🎓 Creating training enrollments...')
  await Promise.all([
    prisma.trainingEnrollment.create({
      data: {
        userId: users[1].id, // Bob
        courseId: courses[1].id, // TypeScript Fundamentals
        enrolledDate: new Date('2024-02-01'),
        startDate: new Date('2024-02-05'),
        status: 'in-progress',
        progress: 0.4, // 40% complete
      },
    }),
    prisma.trainingEnrollment.create({
      data: {
        userId: users[0].id, // Alice
        courseId: courses[2].id, // React Performance
        enrolledDate: new Date('2024-01-15'),
        startDate: new Date('2024-01-20'),
        completedDate: new Date('2024-02-10'),
        status: 'completed',
        progress: 1.0,
        score: 95.5,
      },
    }),
  ])

  // 13. Create Learning Activities
  console.log('📈 Creating learning activities...')
  await Promise.all([
    prisma.learningActivity.create({
      data: {
        type: 'training-started',
        userId: users[1].id, // Bob
        description: 'Started TypeScript Fundamentals course',
        date: new Date('2024-02-05'),
        metadata: { courseId: courses[1].id, courseName: 'TypeScript Fundamentals' },
      },
    }),
    prisma.learningActivity.create({
      data: {
        type: 'assessment-completed',
        userId: users[0].id, // Alice
        description: 'Completed Q1 2024 Technical Skills Assessment',
        date: new Date('2024-01-20'),
        metadata: { assessmentId: assessments[0].id, score: 'Excellent' },
      },
    }),
    prisma.learningActivity.create({
      data: {
        type: 'training-completed',
        userId: users[0].id, // Alice
        description: 'Completed React Performance Optimization course',
        date: new Date('2024-02-10'),
        metadata: { courseId: courses[2].id, score: 95.5 },
      },
    }),
  ])

  // 14. Create Budget Allocations
  console.log('💰 Creating budget allocations...')
  await Promise.all([
    prisma.budgetAllocation.create({
      data: {
        departmentId: departments[0].id, // Engineering
        year: 2024,
        quarter: 1,
        totalBudget: 50000.00,
        allocatedBudget: 35000.00,
        spentBudget: 12500.00,
        trainingCategories: [
          { category: 'Technical Training', allocated: 20000, spent: 8000 },
          { category: 'Leadership Development', allocated: 10000, spent: 3000 },
          { category: 'Conferences', allocated: 5000, spent: 1500 },
        ],
      },
    }),
    prisma.budgetAllocation.create({
      data: {
        departmentId: departments[1].id, // Product
        year: 2024,
        quarter: 1,
        totalBudget: 25000.00,
        allocatedBudget: 18000.00,
        spentBudget: 5000.00,
        trainingCategories: [
          { category: 'Product Management', allocated: 12000, spent: 3000 },
          { category: 'Analytics Training', allocated: 6000, spent: 2000 },
        ],
      },
    }),
  ])

  // 15. Create Vendors
  console.log('🏢 Creating vendors...')
  const vendors = await Promise.all([
    prisma.vendor.create({
      data: {
        companyName: 'TechEd Solutions',
        contactPerson: 'John Williams',
        phoneNumbers: [
          { type: 'office', number: '******-0123' },
          { type: 'mobile', number: '******-0124' },
        ],
        email: '<EMAIL>',
        website: 'https://teched-solutions.com',
        category: 'Training',
        status: 'active',
        rating: 4.7,
        certifications: ['ISO 9001', 'SCORM Certified'],
      },
    }),
    prisma.vendor.create({
      data: {
        companyName: 'Leadership Institute',
        contactPerson: 'Sarah Thompson',
        phoneNumbers: [{ type: 'office', number: '******-0200' }],
        email: '<EMAIL>',
        website: 'https://leadership-institute.com',
        category: 'Training',
        status: 'active',
        rating: 4.6,
        certifications: ['ICF Accredited'],
      },
    }),
  ])

  // 16. Create Proposals
  console.log('📋 Creating proposals...')
  const proposals = await Promise.all([
    prisma.proposal.create({
      data: {
        vendorId: vendors[0].id,
        title: 'Custom JavaScript Training Program',
        description: 'Tailored JavaScript training program for engineering team',
        costs: [
          { item: 'Course Development', cost: 5000 },
          { item: 'Instructor Fees', cost: 3000 },
          { item: 'Materials', cost: 500 },
        ],
        totalCost: 8500.00,
        status: 'approved',
        attachments: ['training-outline.pdf', 'instructor-bios.pdf'],
        validUntil: new Date('2024-06-30'),
        submittedAt: new Date('2024-01-10'),
      },
    }),
  ])

  // 17. Create Reviews
  console.log('⭐ Creating reviews...')
  await Promise.all([
    prisma.review.create({
      data: {
        vendorId: vendors[0].id,
        proposalId: proposals[0].id,
        rating: 5,
        reviewerName: 'Charlie Brown',
        reviewerRole: 'Engineering Manager',
        comment: 'Excellent training quality and instructor expertise',
        strengths: ['Expert instructors', 'Practical examples', 'Good support'],
        improvements: ['Could use more hands-on exercises'],
      },
    }),
    prisma.review.create({
      data: {
        vendorId: vendors[1].id,
        rating: 4,
        reviewerName: 'Diana Prince',
        reviewerRole: 'Product Manager',
        comment: 'Good leadership training, very practical approach',
        strengths: ['Real-world scenarios', 'Interactive sessions'],
        improvements: ['More follow-up support needed'],
      },
    }),
  ])

  // 18. Create Weekly Submissions
  console.log('📅 Creating weekly submissions...')
  await Promise.all([
    prisma.weeklySubmission.create({
      data: {
        userId: users[0].id, // Alice
        weekStartDate: new Date('2024-02-05'),
        weekEndDate: new Date('2024-02-11'),
        submissionDate: new Date('2024-02-12'),
        achievements: [
          {
            title: 'React Performance Optimization',
            description: 'Completed advanced React training and implemented optimizations',
            impact: 'Improved app performance by 30%',
          },
        ],
        recognitions: [
          {
            type: 'Peer Recognition',
            from: 'Bob Smith',
            description: 'Helped me understand complex React patterns',
          },
        ],
        costInitiatives: [
          {
            title: 'Bundle Size Optimization',
            description: 'Reduced bundle size by implementing code splitting',
            estimatedSavings: 500,
            timeframe: 'monthly',
          },
        ],
        trainingIdeas: [
          {
            title: 'Advanced Testing Workshop',
            description: 'Team could benefit from advanced testing strategies',
            priority: 'medium',
          },
        ],
        progressUpdates: [
          {
            project: 'Frontend Modernization',
            status: 'On Track',
            completion: 75,
            nextMilestone: 'Performance testing',
          },
        ],
        weeklyHighlight: 'Successfully implemented React performance optimizations',
        challenges: 'Some legacy code integration challenges',
        nextWeekFocus: 'Focus on testing implementation and code review',
        status: 'submitted',
      },
    }),
  ])

  // 19. Create Competency Framework
  console.log('🎯 Creating competency framework...')
  await prisma.competencyFramework.create({
    data: {
      name: 'Engineering Competency Framework 2024',
      description: 'Comprehensive competency framework for engineering roles',
      skills: skills.map(skill => skill.id),
      roles: roles.slice(0, 3).map(role => role.id), // Engineering roles
      assessmentCriteria: [
        'Technical proficiency demonstration',
        'Code quality and best practices',
        'Problem-solving approach',
        'Collaboration and communication',
        'Continuous learning mindset',
      ],
    },
  })

  // 20. Create Analytics and System Data
  console.log('📊 Creating analytics and system data...')
  
  // Feature Flags
  await Promise.all([
    prisma.featureFlag.create({
      data: {
        name: 'advanced_analytics',
        description: 'Enable advanced learning analytics dashboard',
        enabled: true,
        rules: { departments: ['Engineering', 'Product Management'] },
        metadata: { version: '1.0', rollout: 'gradual' },
      },
    }),
    prisma.featureFlag.create({
      data: {
        name: 'ai_recommendations',
        description: 'Enable AI-powered training recommendations',
        enabled: false,
        rules: { beta_users: true },
        metadata: { version: '0.8', testing: true },
      },
    }),
  ])

  // API Keys
  await prisma.apiKey.create({
    data: {
      name: 'Mobile App Integration',
      key: 'sk_mobile_app_2024',
      hashedKey: 'hashed_key_mobile_app',
      scopes: ['read:training', 'write:enrollment'],
      expiresAt: new Date('2024-12-31'),
      createdBy: users[2].id, // Charlie
    },
  })

  // Analytics Events
  await Promise.all([
    prisma.analytics.create({
      data: {
        event: 'training_started',
        properties: {
          courseId: courses[1].id,
          courseName: 'TypeScript Fundamentals',
          userId: users[1].id,
          department: 'Engineering',
        },
        userId: users[1].id,
        sessionId: 'session_123',
      },
    }),
    prisma.analytics.create({
      data: {
        event: 'assessment_completed',
        properties: {
          assessmentId: assessments[0].id,
          score: 95.5,
          duration: 1800, // 30 minutes
        },
        userId: users[0].id,
        sessionId: 'session_456',
      },
    }),
  ])

  // Search Index
  await Promise.all([
    prisma.searchIndex.create({
      data: {
        entityType: 'course',
        entityId: courses[0].id,
        title: 'Advanced JavaScript Patterns',
        content: 'Master advanced JavaScript patterns and best practices. Learn about closures, prototypes, async patterns, and modern ES6+ features.',
        metadata: { difficulty: 'advanced', rating: 4.7 },
        vector: [0.1, 0.2, 0.3, 0.4, 0.5], // Mock embedding vector
      },
    }),
    prisma.searchIndex.create({
      data: {
        entityType: 'skill',
        entityId: skills[0].id,
        title: 'JavaScript',
        content: 'JavaScript programming language proficiency. From basic syntax to advanced concepts like closures and async programming.',
        metadata: { category: 'Programming' },
        vector: [0.2, 0.3, 0.4, 0.5, 0.6], // Mock embedding vector
      },
    }),
  ])

  // Audit Logs
  await Promise.all([
    prisma.auditLog.create({
      data: {
        action: 'CREATE',
        entityType: 'TrainingEnrollment',
        entityId: 'enrollment_123',
        userId: users[1].id,
        changes: {
          before: null,
          after: { courseId: courses[1].id, status: 'enrolled' },
        },
        metadata: { source: 'web_app' },
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      },
    }),
    prisma.auditLog.create({
      data: {
        action: 'UPDATE',
        entityType: 'User',
        entityId: users[1].id,
        userId: users[2].id, // Charlie updated Bob's info
        changes: {
          before: { roleId: roles[0].id },
          after: { roleId: roles[0].id, managerId: users[0].id },
        },
        metadata: { reason: 'manager_assignment' },
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      },
    }),
  ])

  // Notifications
  await Promise.all([
    prisma.notification.create({
      data: {
        userId: users[1].id, // Bob
        type: 'training_reminder',
        title: 'Course Deadline Approaching',
        message: 'Your TypeScript Fundamentals course is due in 5 days. Complete remaining modules to stay on track.',
        data: { courseId: courses[1].id, dueDate: '2024-03-15' },
        read: false,
      },
    }),
    prisma.notification.create({
      data: {
        userId: users[0].id, // Alice
        type: 'achievement',
        title: 'Congratulations!',
        message: 'You have successfully completed the React Performance Optimization course with a score of 95.5%',
        data: { courseId: courses[2].id, score: 95.5 },
        read: true,
        readAt: new Date('2024-02-11'),
      },
    }),
  ])

  // Reports
  await prisma.report.create({
    data: {
      name: 'Monthly Training Report',
      type: 'training_analytics',
      parameters: {
        period: 'monthly',
        departments: ['Engineering', 'Product Management'],
        metrics: ['enrollments', 'completions', 'satisfaction'],
      },
      schedule: '0 0 1 * *', // First day of every month
      lastRun: new Date('2024-02-01'),
      nextRun: new Date('2024-03-01'),
      status: 'active',
      results: {
        totalEnrollments: 45,
        completionRate: 0.78,
        averageSatisfaction: 4.6,
      },
      createdBy: users[2].id, // Charlie
    },
  })

  // Workflows
  const workflow = await prisma.workflow.create({
    data: {
      name: 'New Employee Onboarding',
      description: 'Automated workflow for new employee L&D onboarding',
      definition: {
        steps: [
          { id: 1, name: 'Send Welcome Email', type: 'email', config: { template: 'welcome' } },
          { id: 2, name: 'Assign Basic Training', type: 'training_assignment', config: { courses: ['orientation'] } },
          { id: 3, name: 'Schedule Manager Meeting', type: 'calendar', config: { duration: 30 } },
        ],
        triggers: [{ event: 'user_created', conditions: { status: 'active' } }],
      },
      status: 'active',
      version: 1,
      createdBy: users[2].id, // Charlie
    },
  })

  // Workflow Execution
  await prisma.workflowExecution.create({
    data: {
      workflowId: workflow.id,
      status: 'completed',
      input: { userId: users[1].id, trigger: 'user_created' },
      output: {
        steps_completed: 3,
        emails_sent: 1,
        trainings_assigned: 2,
        meetings_scheduled: 1,
      },
      startedAt: new Date('2024-02-01T09:00:00Z'),
      completedAt: new Date('2024-02-01T09:15:00Z'),
    },
  })

  console.log('✅ Database seeded successfully!')
  
  // Print summary
  const summary = {
    departments: await prisma.department.count(),
    roles: await prisma.role.count(),
    skills: await prisma.skill.count(),
    users: await prisma.user.count(),
    courses: await prisma.trainingCourse.count(),
    learningPaths: await prisma.learningPath.count(),
    assessments: await prisma.assessment.count(),
    skillGaps: await prisma.skillGap.count(),
    enrollments: await prisma.trainingEnrollment.count(),
    vendors: await prisma.vendor.count(),
    analytics: await prisma.analytics.count(),
  }
  
  console.log('\n📊 Seeding Summary:')
  console.table(summary)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })