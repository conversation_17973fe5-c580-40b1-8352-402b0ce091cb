-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "prisma";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "public";

-- CreateTable
CREATE TABLE "prisma"."Analytics" (
    "id" TEXT NOT NULL,
    "event" TEXT NOT NULL,
    "properties" JSONB NOT NULL,
    "userId" TEXT,
    "sessionId" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."SearchIndex" (
    "id" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "metadata" JSON<PERSON>,
    "vector" DOUBLE PRECISION[] DEFAULT ARRAY[]::DOUBLE PRECISION[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SearchIndex_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."AuditLog" (
    "id" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "changes" JSONB,
    "metadata" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Notification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "data" JSONB,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Report" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "parameters" JSONB NOT NULL,
    "schedule" TEXT,
    "lastRun" TIMESTAMP(3),
    "nextRun" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'active',
    "results" JSONB,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Report_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Workflow" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "definition" JSONB NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "version" INTEGER NOT NULL DEFAULT 1,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Workflow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."WorkflowExecution" (
    "id" TEXT NOT NULL,
    "workflowId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "input" JSONB,
    "output" JSONB,
    "error" JSONB,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "WorkflowExecution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."FeatureFlag" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "enabled" BOOLEAN NOT NULL DEFAULT false,
    "rules" JSONB,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FeatureFlag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."ApiKey" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "hashedKey" TEXT NOT NULL,
    "scopes" TEXT[],
    "expiresAt" TIMESTAMP(3),
    "lastUsedAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "revokedAt" TIMESTAMP(3),

    CONSTRAINT "ApiKey_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "departmentId" TEXT,
    "roleId" TEXT,
    "managerId" TEXT,
    "joinDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'active',
    "profilePicture" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Department" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "manager" TEXT NOT NULL,
    "employeeCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Department_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Role" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "departmentId" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "requiredSkills" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Skill" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "levels" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Skill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."UserSkill" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "skillId" TEXT NOT NULL,
    "currentLevel" INTEGER NOT NULL,
    "assessedDate" TIMESTAMP(3) NOT NULL,
    "source" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserSkill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Assessment" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "departmentId" TEXT,
    "roleId" TEXT,
    "createdBy" TEXT NOT NULL,
    "createdDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "skillsAssessed" TEXT[],
    "dueDate" TIMESTAMP(3),
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Assessment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."AssessmentResponse" (
    "id" TEXT NOT NULL,
    "assessmentId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "completedDate" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'not-started',
    "skillRatings" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AssessmentResponse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."SkillGap" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "skillId" TEXT NOT NULL,
    "currentLevel" INTEGER NOT NULL,
    "requiredLevel" INTEGER NOT NULL,
    "gap" INTEGER NOT NULL,
    "priority" TEXT NOT NULL,
    "impact" TEXT NOT NULL,
    "urgency" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SkillGap_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."TrainingCourse" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "cost" DOUBLE PRECISION NOT NULL,
    "capacity" INTEGER,
    "prerequisites" TEXT[],
    "difficulty" TEXT NOT NULL,
    "rating" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "enrollments" INTEGER NOT NULL DEFAULT 0,
    "status" TEXT NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TrainingCourse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."LearningPath" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "roleId" TEXT,
    "estimatedDuration" INTEGER NOT NULL,
    "difficulty" TEXT NOT NULL,
    "completionCriteria" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LearningPath_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."TrainingRecommendation" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "courseId" TEXT NOT NULL,
    "skillGapId" TEXT NOT NULL,
    "priority" INTEGER NOT NULL,
    "reasoning" TEXT NOT NULL,
    "expectedImpact" TEXT NOT NULL,
    "estimatedCompletion" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TrainingRecommendation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."TrainingEnrollment" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "courseId" TEXT NOT NULL,
    "enrolledDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "startDate" TIMESTAMP(3),
    "completedDate" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'enrolled',
    "progress" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "score" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TrainingEnrollment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."LearningActivity" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LearningActivity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."BudgetAllocation" (
    "id" TEXT NOT NULL,
    "departmentId" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "quarter" INTEGER,
    "totalBudget" DOUBLE PRECISION NOT NULL,
    "allocatedBudget" DOUBLE PRECISION NOT NULL,
    "spentBudget" DOUBLE PRECISION NOT NULL,
    "trainingCategories" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BudgetAllocation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Vendor" (
    "id" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "contactPerson" TEXT NOT NULL,
    "phoneNumbers" JSONB NOT NULL,
    "email" TEXT NOT NULL,
    "website" TEXT,
    "category" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "rating" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "certifications" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Vendor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Proposal" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "costs" JSONB NOT NULL,
    "totalCost" DOUBLE PRECISION NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "attachments" TEXT[],
    "validUntil" TIMESTAMP(3) NOT NULL,
    "submittedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Proposal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."Review" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "proposalId" TEXT,
    "rating" INTEGER NOT NULL,
    "reviewerName" TEXT NOT NULL,
    "reviewerRole" TEXT NOT NULL,
    "comment" TEXT NOT NULL,
    "strengths" TEXT[],
    "improvements" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Review_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."WeeklySubmission" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "weekStartDate" TIMESTAMP(3) NOT NULL,
    "weekEndDate" TIMESTAMP(3) NOT NULL,
    "submissionDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "achievements" JSONB NOT NULL,
    "recognitions" JSONB NOT NULL,
    "costInitiatives" JSONB NOT NULL,
    "trainingIdeas" JSONB NOT NULL,
    "progressUpdates" JSONB NOT NULL,
    "weeklyHighlight" TEXT,
    "challenges" TEXT,
    "nextWeekFocus" TEXT,
    "attachments" JSONB,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WeeklySubmission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."CompetencyFramework" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "skills" JSONB NOT NULL,
    "roles" JSONB NOT NULL,
    "assessmentCriteria" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CompetencyFramework_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "prisma"."_CourseSkills" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "prisma"."_PathCourses" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "prisma"."_PathSkills" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE INDEX "Analytics_event_timestamp_idx" ON "prisma"."Analytics"("event", "timestamp");

-- CreateIndex
CREATE INDEX "Analytics_userId_idx" ON "prisma"."Analytics"("userId");

-- CreateIndex
CREATE INDEX "Analytics_sessionId_idx" ON "prisma"."Analytics"("sessionId");

-- CreateIndex
CREATE INDEX "SearchIndex_entityType_entityId_idx" ON "prisma"."SearchIndex"("entityType", "entityId");

-- CreateIndex
CREATE INDEX "SearchIndex_title_idx" ON "prisma"."SearchIndex"("title");

-- CreateIndex
CREATE INDEX "AuditLog_action_timestamp_idx" ON "prisma"."AuditLog"("action", "timestamp");

-- CreateIndex
CREATE INDEX "AuditLog_entityType_entityId_idx" ON "prisma"."AuditLog"("entityType", "entityId");

-- CreateIndex
CREATE INDEX "AuditLog_userId_idx" ON "prisma"."AuditLog"("userId");

-- CreateIndex
CREATE INDEX "Notification_userId_read_idx" ON "prisma"."Notification"("userId", "read");

-- CreateIndex
CREATE INDEX "Notification_type_idx" ON "prisma"."Notification"("type");

-- CreateIndex
CREATE INDEX "Report_type_status_idx" ON "prisma"."Report"("type", "status");

-- CreateIndex
CREATE INDEX "Report_createdBy_idx" ON "prisma"."Report"("createdBy");

-- CreateIndex
CREATE INDEX "Workflow_status_idx" ON "prisma"."Workflow"("status");

-- CreateIndex
CREATE INDEX "Workflow_createdBy_idx" ON "prisma"."Workflow"("createdBy");

-- CreateIndex
CREATE INDEX "WorkflowExecution_workflowId_status_idx" ON "prisma"."WorkflowExecution"("workflowId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureFlag_name_key" ON "prisma"."FeatureFlag"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ApiKey_key_key" ON "prisma"."ApiKey"("key");

-- CreateIndex
CREATE INDEX "ApiKey_key_idx" ON "prisma"."ApiKey"("key");

-- CreateIndex
CREATE INDEX "ApiKey_createdBy_idx" ON "prisma"."ApiKey"("createdBy");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "prisma"."User"("email");

-- CreateIndex
CREATE INDEX "User_departmentId_idx" ON "prisma"."User"("departmentId");

-- CreateIndex
CREATE INDEX "User_roleId_idx" ON "prisma"."User"("roleId");

-- CreateIndex
CREATE INDEX "User_managerId_idx" ON "prisma"."User"("managerId");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "prisma"."User"("email");

-- CreateIndex
CREATE INDEX "Department_name_idx" ON "prisma"."Department"("name");

-- CreateIndex
CREATE INDEX "Role_departmentId_idx" ON "prisma"."Role"("departmentId");

-- CreateIndex
CREATE INDEX "Role_level_idx" ON "prisma"."Role"("level");

-- CreateIndex
CREATE INDEX "Skill_category_idx" ON "prisma"."Skill"("category");

-- CreateIndex
CREATE INDEX "Skill_name_idx" ON "prisma"."Skill"("name");

-- CreateIndex
CREATE INDEX "UserSkill_userId_idx" ON "prisma"."UserSkill"("userId");

-- CreateIndex
CREATE INDEX "UserSkill_skillId_idx" ON "prisma"."UserSkill"("skillId");

-- CreateIndex
CREATE UNIQUE INDEX "UserSkill_userId_skillId_key" ON "prisma"."UserSkill"("userId", "skillId");

-- CreateIndex
CREATE INDEX "Assessment_departmentId_idx" ON "prisma"."Assessment"("departmentId");

-- CreateIndex
CREATE INDEX "Assessment_roleId_idx" ON "prisma"."Assessment"("roleId");

-- CreateIndex
CREATE INDEX "Assessment_status_idx" ON "prisma"."Assessment"("status");

-- CreateIndex
CREATE INDEX "AssessmentResponse_assessmentId_idx" ON "prisma"."AssessmentResponse"("assessmentId");

-- CreateIndex
CREATE INDEX "AssessmentResponse_userId_idx" ON "prisma"."AssessmentResponse"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "AssessmentResponse_assessmentId_userId_key" ON "prisma"."AssessmentResponse"("assessmentId", "userId");

-- CreateIndex
CREATE INDEX "SkillGap_userId_idx" ON "prisma"."SkillGap"("userId");

-- CreateIndex
CREATE INDEX "SkillGap_skillId_idx" ON "prisma"."SkillGap"("skillId");

-- CreateIndex
CREATE INDEX "SkillGap_priority_idx" ON "prisma"."SkillGap"("priority");

-- CreateIndex
CREATE UNIQUE INDEX "SkillGap_userId_skillId_key" ON "prisma"."SkillGap"("userId", "skillId");

-- CreateIndex
CREATE INDEX "TrainingCourse_provider_idx" ON "prisma"."TrainingCourse"("provider");

-- CreateIndex
CREATE INDEX "TrainingCourse_type_idx" ON "prisma"."TrainingCourse"("type");

-- CreateIndex
CREATE INDEX "TrainingCourse_status_idx" ON "prisma"."TrainingCourse"("status");

-- CreateIndex
CREATE INDEX "LearningPath_roleId_idx" ON "prisma"."LearningPath"("roleId");

-- CreateIndex
CREATE INDEX "TrainingRecommendation_userId_idx" ON "prisma"."TrainingRecommendation"("userId");

-- CreateIndex
CREATE INDEX "TrainingRecommendation_courseId_idx" ON "prisma"."TrainingRecommendation"("courseId");

-- CreateIndex
CREATE INDEX "TrainingRecommendation_skillGapId_idx" ON "prisma"."TrainingRecommendation"("skillGapId");

-- CreateIndex
CREATE INDEX "TrainingEnrollment_userId_idx" ON "prisma"."TrainingEnrollment"("userId");

-- CreateIndex
CREATE INDEX "TrainingEnrollment_courseId_idx" ON "prisma"."TrainingEnrollment"("courseId");

-- CreateIndex
CREATE INDEX "TrainingEnrollment_status_idx" ON "prisma"."TrainingEnrollment"("status");

-- CreateIndex
CREATE UNIQUE INDEX "TrainingEnrollment_userId_courseId_key" ON "prisma"."TrainingEnrollment"("userId", "courseId");

-- CreateIndex
CREATE INDEX "LearningActivity_userId_idx" ON "prisma"."LearningActivity"("userId");

-- CreateIndex
CREATE INDEX "LearningActivity_type_idx" ON "prisma"."LearningActivity"("type");

-- CreateIndex
CREATE INDEX "LearningActivity_date_idx" ON "prisma"."LearningActivity"("date");

-- CreateIndex
CREATE INDEX "BudgetAllocation_departmentId_idx" ON "prisma"."BudgetAllocation"("departmentId");

-- CreateIndex
CREATE INDEX "BudgetAllocation_year_idx" ON "prisma"."BudgetAllocation"("year");

-- CreateIndex
CREATE UNIQUE INDEX "BudgetAllocation_departmentId_year_quarter_key" ON "prisma"."BudgetAllocation"("departmentId", "year", "quarter");

-- CreateIndex
CREATE INDEX "Vendor_category_idx" ON "prisma"."Vendor"("category");

-- CreateIndex
CREATE INDEX "Vendor_status_idx" ON "prisma"."Vendor"("status");

-- CreateIndex
CREATE INDEX "Vendor_rating_idx" ON "prisma"."Vendor"("rating");

-- CreateIndex
CREATE INDEX "Proposal_vendorId_idx" ON "prisma"."Proposal"("vendorId");

-- CreateIndex
CREATE INDEX "Proposal_status_idx" ON "prisma"."Proposal"("status");

-- CreateIndex
CREATE INDEX "Proposal_totalCost_idx" ON "prisma"."Proposal"("totalCost");

-- CreateIndex
CREATE INDEX "Review_vendorId_idx" ON "prisma"."Review"("vendorId");

-- CreateIndex
CREATE INDEX "Review_proposalId_idx" ON "prisma"."Review"("proposalId");

-- CreateIndex
CREATE INDEX "Review_rating_idx" ON "prisma"."Review"("rating");

-- CreateIndex
CREATE INDEX "WeeklySubmission_userId_idx" ON "prisma"."WeeklySubmission"("userId");

-- CreateIndex
CREATE INDEX "WeeklySubmission_weekStartDate_idx" ON "prisma"."WeeklySubmission"("weekStartDate");

-- CreateIndex
CREATE INDEX "WeeklySubmission_status_idx" ON "prisma"."WeeklySubmission"("status");

-- CreateIndex
CREATE UNIQUE INDEX "WeeklySubmission_userId_weekStartDate_key" ON "prisma"."WeeklySubmission"("userId", "weekStartDate");

-- CreateIndex
CREATE INDEX "CompetencyFramework_name_idx" ON "prisma"."CompetencyFramework"("name");

-- CreateIndex
CREATE UNIQUE INDEX "_CourseSkills_AB_unique" ON "prisma"."_CourseSkills"("A", "B");

-- CreateIndex
CREATE INDEX "_CourseSkills_B_index" ON "prisma"."_CourseSkills"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_PathCourses_AB_unique" ON "prisma"."_PathCourses"("A", "B");

-- CreateIndex
CREATE INDEX "_PathCourses_B_index" ON "prisma"."_PathCourses"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_PathSkills_AB_unique" ON "prisma"."_PathSkills"("A", "B");

-- CreateIndex
CREATE INDEX "_PathSkills_B_index" ON "prisma"."_PathSkills"("B");

-- AddForeignKey
ALTER TABLE "prisma"."WorkflowExecution" ADD CONSTRAINT "WorkflowExecution_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "prisma"."Workflow"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."User" ADD CONSTRAINT "User_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "prisma"."Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."User" ADD CONSTRAINT "User_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "prisma"."Role"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."User" ADD CONSTRAINT "User_managerId_fkey" FOREIGN KEY ("managerId") REFERENCES "prisma"."User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Role" ADD CONSTRAINT "Role_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "prisma"."Department"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."UserSkill" ADD CONSTRAINT "UserSkill_userId_fkey" FOREIGN KEY ("userId") REFERENCES "prisma"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."UserSkill" ADD CONSTRAINT "UserSkill_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "prisma"."Skill"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Assessment" ADD CONSTRAINT "Assessment_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "prisma"."Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Assessment" ADD CONSTRAINT "Assessment_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "prisma"."Role"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."AssessmentResponse" ADD CONSTRAINT "AssessmentResponse_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "prisma"."Assessment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."AssessmentResponse" ADD CONSTRAINT "AssessmentResponse_userId_fkey" FOREIGN KEY ("userId") REFERENCES "prisma"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."SkillGap" ADD CONSTRAINT "SkillGapUser" FOREIGN KEY ("userId") REFERENCES "prisma"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."SkillGap" ADD CONSTRAINT "SkillGap_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "prisma"."Skill"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."LearningPath" ADD CONSTRAINT "LearningPath_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "prisma"."Role"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."TrainingRecommendation" ADD CONSTRAINT "TrainingRecommendation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "prisma"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."TrainingRecommendation" ADD CONSTRAINT "TrainingRecommendation_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "prisma"."TrainingCourse"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."TrainingRecommendation" ADD CONSTRAINT "TrainingRecommendation_skillGapId_fkey" FOREIGN KEY ("skillGapId") REFERENCES "prisma"."SkillGap"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."TrainingEnrollment" ADD CONSTRAINT "TrainingEnrollment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "prisma"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."TrainingEnrollment" ADD CONSTRAINT "TrainingEnrollment_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "prisma"."TrainingCourse"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."LearningActivity" ADD CONSTRAINT "LearningActivity_userId_fkey" FOREIGN KEY ("userId") REFERENCES "prisma"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."BudgetAllocation" ADD CONSTRAINT "BudgetAllocation_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "prisma"."Department"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Proposal" ADD CONSTRAINT "Proposal_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "prisma"."Vendor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Review" ADD CONSTRAINT "Review_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "prisma"."Vendor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."Review" ADD CONSTRAINT "Review_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "prisma"."Proposal"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."WeeklySubmission" ADD CONSTRAINT "WeeklySubmission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "prisma"."User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."_CourseSkills" ADD CONSTRAINT "_CourseSkills_A_fkey" FOREIGN KEY ("A") REFERENCES "prisma"."Skill"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."_CourseSkills" ADD CONSTRAINT "_CourseSkills_B_fkey" FOREIGN KEY ("B") REFERENCES "prisma"."TrainingCourse"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."_PathCourses" ADD CONSTRAINT "_PathCourses_A_fkey" FOREIGN KEY ("A") REFERENCES "prisma"."LearningPath"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."_PathCourses" ADD CONSTRAINT "_PathCourses_B_fkey" FOREIGN KEY ("B") REFERENCES "prisma"."TrainingCourse"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."_PathSkills" ADD CONSTRAINT "_PathSkills_A_fkey" FOREIGN KEY ("A") REFERENCES "prisma"."LearningPath"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "prisma"."_PathSkills" ADD CONSTRAINT "_PathSkills_B_fkey" FOREIGN KEY ("B") REFERENCES "prisma"."Skill"("id") ON DELETE CASCADE ON UPDATE CASCADE;

