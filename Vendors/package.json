{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@faker-js/faker": "^9.9.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-router": "^1.116.0", "@tanstack/react-router-devtools": "^1.116.0", "@tanstack/react-start": "^1.116.1", "@tanstack/react-table": "^8.21.3", "@tanstack/router-generator": "^1.124.0", "@tanstack/start": "^1.116.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lucide-react": "^0.488.0", "msw": "^2.10.3", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "recharts": "^3.0.2", "@luminar/shared-ui": "file:../shared-ui", "@luminar/shared-auth": "file:../shared-auth", "zustand": "^5.0.0", "axios": "^1.6.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vite": "^6.0.0", "zod": "^3.25.71"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "happy-dom": "^15.11.6", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8"}, "msw": {"workerDirectory": ["public"]}}