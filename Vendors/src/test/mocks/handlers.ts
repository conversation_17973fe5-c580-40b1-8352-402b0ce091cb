import { http, HttpResponse } from 'msw'

// Define request handlers for vendor API endpoints
export const handlers = [
  // Vendors endpoints
  http.get('/api/vendors', () => {
    return HttpResponse.json({
      vendors: [
        {
          id: '1',
          name: 'Tech Solutions Inc.',
          category: 'IT Services',
          status: 'active',
          rating: 4.5,
          contractValue: 150000,
          email: '<EMAIL>',
          phone: '******-0123',
          location: 'New York, NY',
        },
        {
          id: '2',
          name: 'Global Consulting Partners',
          category: 'Consulting',
          status: 'active',
          rating: 4.8,
          contractValue: 280000,
          email: '<EMAIL>',
          phone: '******-0456',
          location: 'San Francisco, CA',
        },
      ],
      total: 2,
    })
  }),

  // Single vendor endpoint
  http.get('/api/vendors/:id', ({ params }) => {
    return HttpResponse.json({
      id: params.id,
      name: 'Tech Solutions Inc.',
      category: 'IT Services',
      status: 'active',
      rating: 4.5,
      contractValue: 150000,
      email: '<EMAIL>',
      phone: '******-0123',
      location: 'New York, NY',
      description: 'Leading IT services provider',
      contracts: [
        {
          id: 'c1',
          title: 'Software Development',
          value: 80000,
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          status: 'active',
        },
      ],
      reviews: [
        {
          id: 'r1',
          rating: 5,
          comment: 'Excellent service',
          author: 'John Doe',
          date: '2024-01-15',
        },
      ],
    })
  }),

  // Proposals endpoints
  http.get('/api/proposals', () => {
    return HttpResponse.json({
      proposals: [
        {
          id: 'p1',
          vendorId: '1',
          vendorName: 'Tech Solutions Inc.',
          title: 'Cloud Migration Proposal',
          value: 75000,
          status: 'under_review',
          submittedAt: '2024-01-10',
          category: 'IT Infrastructure',
        },
        {
          id: 'p2',
          vendorId: '2',
          vendorName: 'Global Consulting Partners',
          title: 'Digital Transformation Strategy',
          value: 120000,
          status: 'approved',
          submittedAt: '2024-01-05',
          category: 'Consulting',
        },
      ],
    })
  }),

  // Reviews endpoints
  http.get('/api/reviews', () => {
    return HttpResponse.json({
      reviews: [
        {
          id: 'r1',
          vendorId: '1',
          vendorName: 'Tech Solutions Inc.',
          rating: 5,
          comment: 'Excellent service and timely delivery',
          author: 'Jane Smith',
          date: '2024-01-15',
          helpful: 12,
        },
      ],
    })
  }),

  // Create vendor endpoint
  http.post('/api/vendors', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      id: '3',
      ...body,
      status: 'pending',
      rating: 0,
      contractValue: 0,
    })
  }),

  // Update vendor endpoint
  http.put('/api/vendors/:id', async ({ params, request }) => {
    const body = await request.json()
    return HttpResponse.json({
      id: params.id,
      ...body,
    })
  }),

  // Create review endpoint
  http.post('/api/reviews', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      id: 'r2',
      ...body,
      date: new Date().toISOString(),
      helpful: 0,
    })
  }),

  // Analytics endpoint
  http.get('/api/vendors/analytics', () => {
    return HttpResponse.json({
      totalVendors: 45,
      activeContracts: 28,
      totalSpend: 3250000,
      averageRating: 4.3,
      vendorsByCategory: {
        'IT Services': 15,
        'Consulting': 10,
        'Marketing': 8,
        'Legal': 5,
        'Other': 7,
      },
      spendByMonth: [
        { month: 'Jan', spend: 280000 },
        { month: 'Feb', spend: 310000 },
        { month: 'Mar', spend: 295000 },
      ],
    })
  }),
]