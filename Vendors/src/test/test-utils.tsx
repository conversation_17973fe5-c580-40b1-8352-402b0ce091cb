import { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { RouterProvider, createMemoryRouter } from '@tanstack/react-router'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import userEvent from '@testing-library/user-event'

// Create a custom render function that includes providers
export function customRender(
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  // Create a memory router for testing
  const router = createMemoryRouter({
    routeTree: {
      children: [
        {
          path: '/',
          component: () => ui,
        },
      ],
    },
  })

  // Create a query client for testing
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  const AllProviders = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  )

  return {
    user: userEvent.setup(),
    ...render(<AllProviders>{ui}</AllProviders>, options),
  }
}

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }