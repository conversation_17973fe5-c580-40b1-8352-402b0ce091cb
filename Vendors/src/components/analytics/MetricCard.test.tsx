import { describe, it, expect, vi } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { MetricCard } from './MetricCard'

describe('MetricCard', () => {
  const defaultProps = {
    title: 'Total Vendors',
    value: '125',
    icon: '🏢',
    trend: 12.5,
    description: 'Active vendors this month',
  }

  it('renders metric title and value', () => {
    render(<MetricCard {...defaultProps} />)

    expect(screen.getByText('Total Vendors')).toBeInTheDocument()
    expect(screen.getByText('125')).toBeInTheDocument()
  })

  it('renders icon when provided', () => {
    render(<MetricCard {...defaultProps} />)

    expect(screen.getByText('🏢')).toBeInTheDocument()
  })

  it('renders description when provided', () => {
    render(<MetricCard {...defaultProps} />)

    expect(screen.getByText('Active vendors this month')).toBeInTheDocument()
  })

  it('shows positive trend with up arrow', () => {
    render(<MetricCard {...defaultProps} trend={15.5} />)

    expect(screen.getByText('↑ 15.5%')).toBeInTheDocument()
    expect(screen.getByText('↑ 15.5%')).toHaveClass('text-green-600')
  })

  it('shows negative trend with down arrow', () => {
    render(<MetricCard {...defaultProps} trend={-8.3} />)

    expect(screen.getByText('↓ 8.3%')).toBeInTheDocument()
    expect(screen.getByText('↓ 8.3%')).toHaveClass('text-red-600')
  })

  it('shows neutral trend for zero', () => {
    render(<MetricCard {...defaultProps} trend={0} />)

    expect(screen.getByText('→ 0%')).toBeInTheDocument()
    expect(screen.getByText('→ 0%')).toHaveClass('text-gray-600')
  })

  it('does not show trend when not provided', () => {
    const { trend, ...propsWithoutTrend } = defaultProps
    render(<MetricCard {...propsWithoutTrend} />)

    expect(screen.queryByText(/↑|↓|→/)).not.toBeInTheDocument()
  })

  it('handles numeric values', () => {
    render(<MetricCard {...defaultProps} value={1234567} />)

    expect(screen.getByText('1234567')).toBeInTheDocument()
  })

  it('renders without icon', () => {
    const { icon, ...propsWithoutIcon } = defaultProps
    render(<MetricCard {...propsWithoutIcon} />)

    expect(screen.queryByText('🏢')).not.toBeInTheDocument()
  })

  it('renders without description', () => {
    const { description, ...propsWithoutDescription } = defaultProps
    render(<MetricCard {...propsWithoutDescription} />)

    expect(screen.queryByText('Active vendors this month')).not.toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<MetricCard {...defaultProps} className="custom-class" />)

    const card = screen.getByRole('article')
    expect(card).toHaveClass('custom-class')
  })

  it('renders with loading state', () => {
    render(<MetricCard {...defaultProps} isLoading={true} />)

    expect(screen.getByTestId('metric-skeleton')).toBeInTheDocument()
    expect(screen.queryByText('125')).not.toBeInTheDocument()
  })

  it('handles onClick callback', async () => {
    const handleClick = vi.fn()
    const { user } = render(<MetricCard {...defaultProps} onClick={handleClick} />)

    const card = screen.getByRole('article')
    await user.click(card)

    expect(handleClick).toHaveBeenCalled()
  })

  it('shows hover effect when clickable', () => {
    const handleClick = vi.fn()
    render(<MetricCard {...defaultProps} onClick={handleClick} />)

    const card = screen.getByRole('article')
    expect(card).toHaveClass('cursor-pointer', 'hover:shadow-md')
  })

  it('formats large numbers with commas', () => {
    render(<MetricCard {...defaultProps} value="1234567" format="number" />)

    expect(screen.getByText('1,234,567')).toBeInTheDocument()
  })

  it('formats currency values', () => {
    render(<MetricCard {...defaultProps} value="1234567" format="currency" />)

    expect(screen.getByText('$1,234,567')).toBeInTheDocument()
  })

  it('formats percentage values', () => {
    render(<MetricCard {...defaultProps} value="85.5" format="percentage" />)

    expect(screen.getByText('85.5%')).toBeInTheDocument()
  })

  it('shows comparison value', () => {
    render(<MetricCard 
      {...defaultProps} 
      comparisonValue="100" 
      comparisonLabel="last month" 
    />)

    expect(screen.getByText('vs 100 last month')).toBeInTheDocument()
  })

  it('renders with different color schemes', () => {
    render(<MetricCard {...defaultProps} colorScheme="blue" />)

    const valueElement = screen.getByText('125')
    expect(valueElement).toHaveClass('text-blue-600')
  })

  it('shows subtitle when provided', () => {
    render(<MetricCard {...defaultProps} subtitle="Q4 2024" />)

    expect(screen.getByText('Q4 2024')).toBeInTheDocument()
  })

  it('renders action button when provided', async () => {
    const handleAction = vi.fn()
    const { user } = render(
      <MetricCard 
        {...defaultProps} 
        action={{ label: 'View Details', onClick: handleAction }}
      />
    )

    const actionButton = screen.getByRole('button', { name: /view details/i })
    await user.click(actionButton)

    expect(handleAction).toHaveBeenCalled()
  })

  it('shows tooltip on hover when provided', async () => {
    const { user } = render(
      <MetricCard {...defaultProps} tooltip="Click to view vendor list" />
    )

    const card = screen.getByRole('article')
    await user.hover(card)

    expect(screen.getByRole('tooltip')).toHaveTextContent('Click to view vendor list')
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<MetricCard {...defaultProps} size="small" />)
    expect(screen.getByText('125')).toHaveClass('text-2xl')

    rerender(<MetricCard {...defaultProps} size="medium" />)
    expect(screen.getByText('125')).toHaveClass('text-3xl')

    rerender(<MetricCard {...defaultProps} size="large" />)
    expect(screen.getByText('125')).toHaveClass('text-4xl')
  })

  it('shows error state', () => {
    render(<MetricCard {...defaultProps} error="Failed to load data" />)

    expect(screen.getByText('Failed to load data')).toBeInTheDocument()
    expect(screen.queryByText('125')).not.toBeInTheDocument()
  })

  it('renders with custom icon component', () => {
    const CustomIcon = () => <svg data-testid="custom-icon" />
    render(<MetricCard {...defaultProps} icon={<CustomIcon />} />)

    expect(screen.getByTestId('custom-icon')).toBeInTheDocument()
  })

  it('shows trend period label', () => {
    render(<MetricCard {...defaultProps} trend={10} trendPeriod="vs last week" />)

    expect(screen.getByText('↑ 10% vs last week')).toBeInTheDocument()
  })

  it('handles null or undefined values gracefully', () => {
    render(<MetricCard {...defaultProps} value={null} />)
    expect(screen.getByText('--')).toBeInTheDocument()

    render(<MetricCard {...defaultProps} value={undefined} />)
    expect(screen.getByText('--')).toBeInTheDocument()
  })

  it('shows sparkline chart when data provided', () => {
    const sparklineData = [10, 15, 12, 18, 20, 22]
    render(<MetricCard {...defaultProps} sparkline={sparklineData} />)

    expect(screen.getByTestId('sparkline-chart')).toBeInTheDocument()
  })

  it('applies animation on value change', async () => {
    const { rerender } = render(<MetricCard {...defaultProps} value="100" />)
    
    rerender(<MetricCard {...defaultProps} value="125" animate={true} />)

    const valueElement = screen.getByText('125')
    expect(valueElement).toHaveClass('transition-all')
  })
})</function_results>

Finally, let me create one last test file for Wins-of-Week reports component:

<function_calls>
<invoke name="mcp__filesystem__list_directory">
<parameter name="path">/Users/<USER>/Luminar/Wins-of-Week/src/components/reports