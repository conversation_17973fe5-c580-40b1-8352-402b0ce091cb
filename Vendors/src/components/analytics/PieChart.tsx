import { LuminarPieChart, type PieChartData } from '@luminar/shared-ui'
import { cn } from '~/lib/utils'

interface PieChartProps {
  data: Array<{
    name: string
    value: number
    [key: string]: any
  }>
  height?: number
  colors?: string[]
  innerRadius?: number
  outerRadius?: number
  showLegend?: boolean
  showLabels?: boolean
  customTooltip?: React.ComponentType<any>
  className?: string
  formatValue?: (value: number) => string
  interactive?: boolean
}

const defaultColors = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#8b5cf6', // violet-500
  '#ec4899', // pink-500
  '#06b6d4', // cyan-500
  '#84cc16', // lime-500
  '#f97316', // orange-500
  '#6366f1', // indigo-500
]

export function PieChart({
  data,
  height = 300,
  colors = defaultColors,
  innerRadius = 0,
  outerRadius = 80,
  showLegend = true,
  showLabels = false,
  customTooltip,
  className,
  formatValue = (value) => value.toLocaleString(),
  interactive = true
}: PieChartProps) {
  // Transform data to LuminarPieChart format
  const chartData: PieChartData[] = data.map((item, index) => ({
    label: item.name,
    value: item.value,
    color: colors[index % colors.length]
  }))
  
  return (
    <LuminarPieChart
      data={chartData}
      height={height}
      donut={innerRadius > 0}
      glass={true}
      animated={true}
      showPercentages={showLabels}
      showLegend={showLegend}
      interactive={interactive}
      className={cn("w-full", className)}
    />
  )
}