import { describe, it, expect, vi } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { VendorPerformanceChart } from './VendorPerformanceChart'

// Mock recharts components
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  LineChart: ({ children, data }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Line: ({ dataKey }: any) => <div data-testid={`line-${dataKey}`} />,
  XAxis: ({ dataKey }: any) => <div data-testid="x-axis" data-key={dataKey} />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
}))

describe('VendorPerformanceChart', () => {
  const mockData = [
    {
      month: 'Jan',
      performance: 85,
      contracts: 12,
      spend: 150000,
    },
    {
      month: 'Feb',
      performance: 87,
      contracts: 15,
      spend: 180000,
    },
    {
      month: 'Mar',
      performance: 82,
      contracts: 13,
      spend: 165000,
    },
    {
      month: 'Apr',
      performance: 90,
      contracts: 18,
      spend: 200000,
    },
  ]

  const defaultProps = {
    data: mockData,
    title: 'Vendor Performance Trends',
    height: 400,
  }

  it('renders chart with title', () => {
    render(<VendorPerformanceChart {...defaultProps} />)

    expect(screen.getByText('Vendor Performance Trends')).toBeInTheDocument()
  })

  it('renders chart components', () => {
    render(<VendorPerformanceChart {...defaultProps} />)

    expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
    expect(screen.getByTestId('line-chart')).toBeInTheDocument()
    expect(screen.getByTestId('x-axis')).toBeInTheDocument()
    expect(screen.getByTestId('y-axis')).toBeInTheDocument()
    expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument()
    expect(screen.getByTestId('tooltip')).toBeInTheDocument()
    expect(screen.getByTestId('legend')).toBeInTheDocument()
  })

  it('passes data to chart', () => {
    render(<VendorPerformanceChart {...defaultProps} />)

    const lineChart = screen.getByTestId('line-chart')
    const chartData = JSON.parse(lineChart.getAttribute('data-chart-data') || '[]')

    expect(chartData).toEqual(mockData)
  })

  it('renders performance line', () => {
    render(<VendorPerformanceChart {...defaultProps} />)

    expect(screen.getByTestId('line-performance')).toBeInTheDocument()
  })

  it('renders contracts line when showContracts is true', () => {
    render(<VendorPerformanceChart {...defaultProps} showContracts={true} />)

    expect(screen.getByTestId('line-contracts')).toBeInTheDocument()
  })

  it('does not render contracts line when showContracts is false', () => {
    render(<VendorPerformanceChart {...defaultProps} showContracts={false} />)

    expect(screen.queryByTestId('line-contracts')).not.toBeInTheDocument()
  })

  it('renders spend line when showSpend is true', () => {
    render(<VendorPerformanceChart {...defaultProps} showSpend={true} />)

    expect(screen.getByTestId('line-spend')).toBeInTheDocument()
  })

  it('uses month as x-axis data key', () => {
    render(<VendorPerformanceChart {...defaultProps} />)

    const xAxis = screen.getByTestId('x-axis')
    expect(xAxis).toHaveAttribute('data-key', 'month')
  })

  it('handles empty data gracefully', () => {
    render(<VendorPerformanceChart {...defaultProps} data={[]} />)

    expect(screen.getByTestId('line-chart')).toBeInTheDocument()
    expect(screen.getByText('No data available')).toBeInTheDocument()
  })

  it('displays loading state', () => {
    render(<VendorPerformanceChart {...defaultProps} isLoading={true} />)

    expect(screen.getByRole('status')).toBeInTheDocument()
    expect(screen.queryByTestId('line-chart')).not.toBeInTheDocument()
  })

  it('applies custom height', () => {
    render(<VendorPerformanceChart {...defaultProps} height={300} />)

    const container = screen.getByTestId('responsive-container')
    expect(container.parentElement).toHaveStyle({ height: '300px' })
  })

  it('shows all metrics by default', () => {
    render(<VendorPerformanceChart {...defaultProps} />)

    expect(screen.getByTestId('line-performance')).toBeInTheDocument()
    expect(screen.getByTestId('line-contracts')).toBeInTheDocument()
    expect(screen.getByTestId('line-spend')).toBeInTheDocument()
  })

  it('handles date range prop', () => {
    const dateRange = { start: '2024-01-01', end: '2024-04-30' }
    render(<VendorPerformanceChart {...defaultProps} dateRange={dateRange} />)

    // Chart should render with date range applied
    expect(screen.getByTestId('line-chart')).toBeInTheDocument()
  })

  it('displays export button when onExport is provided', async () => {
    const onExport = vi.fn()
    const { user } = render(<VendorPerformanceChart {...defaultProps} onExport={onExport} />)

    const exportButton = screen.getByRole('button', { name: /export/i })
    expect(exportButton).toBeInTheDocument()

    await user.click(exportButton)
    expect(onExport).toHaveBeenCalledWith(mockData)
  })

  it('formats performance values as percentages', () => {
    render(<VendorPerformanceChart {...defaultProps} />)

    // Check that performance line is configured for percentage display
    const performanceLine = screen.getByTestId('line-performance')
    expect(performanceLine).toBeInTheDocument()
  })

  it('formats spend values as currency', () => {
    render(<VendorPerformanceChart {...defaultProps} showSpend={true} />)

    // Check that spend line is configured for currency display
    const spendLine = screen.getByTestId('line-spend')
    expect(spendLine).toBeInTheDocument()
  })

  it('allows customization of line colors', () => {
    const customColors = {
      performance: '#ff0000',
      contracts: '#00ff00',
      spend: '#0000ff',
    }

    render(<VendorPerformanceChart {...defaultProps} colors={customColors} />)

    // Lines should use custom colors
    expect(screen.getByTestId('line-performance')).toBeInTheDocument()
  })

  it('supports different time periods', () => {
    const weeklyData = [
      { week: 'Week 1', performance: 85 },
      { week: 'Week 2', performance: 87 },
    ]

    render(
      <VendorPerformanceChart
        data={weeklyData}
        xAxisKey="week"
        title="Weekly Performance"
      />
    )

    const xAxis = screen.getByTestId('x-axis')
    expect(xAxis).toHaveAttribute('data-key', 'week')
  })

  it('handles error state', () => {
    render(<VendorPerformanceChart {...defaultProps} error="Failed to load data" />)

    expect(screen.getByText('Failed to load data')).toBeInTheDocument()
    expect(screen.queryByTestId('line-chart')).not.toBeInTheDocument()
  })

  it('supports comparison mode', () => {
    const comparisonData = mockData.map(item => ({
      ...item,
      performancePrevious: item.performance - 5,
    }))

    render(
      <VendorPerformanceChart
        data={comparisonData}
        title="Performance Comparison"
        comparison={true}
      />
    )

    expect(screen.getByTestId('line-performance')).toBeInTheDocument()
    expect(screen.getByTestId('line-performancePrevious')).toBeInTheDocument()
  })

  it('displays trend indicators', () => {
    render(<VendorPerformanceChart {...defaultProps} showTrend={true} />)

    // Should show trend arrow or indicator
    const latestPerformance = mockData[mockData.length - 1].performance
    const previousPerformance = mockData[mockData.length - 2].performance
    const trend = latestPerformance > previousPerformance ? 'up' : 'down'

    expect(screen.getByTestId(`trend-${trend}`)).toBeInTheDocument()
  })

  it('supports data point click interactions', async () => {
    const onDataPointClick = vi.fn()
    const { user } = render(
      <VendorPerformanceChart {...defaultProps} onDataPointClick={onDataPointClick} />
    )

    // Simulate clicking on a data point
    const dataPoint = screen.getByTestId('data-point-0')
    await user.click(dataPoint)

    expect(onDataPointClick).toHaveBeenCalledWith(mockData[0])
  })

  it('renders with responsive behavior', () => {
    render(<VendorPerformanceChart {...defaultProps} responsive={true} />)

    const container = screen.getByTestId('responsive-container')
    expect(container).toBeInTheDocument()
  })
})