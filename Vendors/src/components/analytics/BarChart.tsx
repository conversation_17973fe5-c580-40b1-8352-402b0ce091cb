import { LuminarBarChart, type BarChartData } from '@luminar/shared-ui'
import { cn } from '~/lib/utils'

interface BarChartProps {
  data: any[]
  dataKey: string | string[]
  xAxisKey: string
  height?: number
  colors?: string[]
  showGrid?: boolean
  showLegend?: boolean
  customTooltip?: React.ComponentType<any>
  formatXAxis?: (value: any) => string
  formatYAxis?: (value: any) => string
  className?: string
  stacked?: boolean
  layout?: 'horizontal' | 'vertical'
  barSize?: number
}

const defaultColors = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#8b5cf6', // violet-500
  '#ec4899', // pink-500
  '#06b6d4', // cyan-500
  '#84cc16', // lime-500
]

export function BarChart({
  data,
  dataKey,
  xAxisKey,
  height = 300,
  colors = defaultColors,
  showGrid = true,
  showLegend = true,
  customTooltip,
  formatXAxis,
  formatYAxis,
  className,
  stacked = false,
  layout = 'horizontal',
  barSize
}: BarChartProps) {
  // Transform data to LuminarBarChart format
  const chartData: BarChartData[] = data.map((item, index) => {
    const dataKeys = Array.isArray(dataKey) ? dataKey : [dataKey]
    const value = typeof item[dataKeys[0]] === 'number' ? item[dataKeys[0]] : 0
    
    return {
      label: formatXAxis ? formatXAxis(item[xAxisKey]) : String(item[xAxisKey]),
      value,
      color: colors[index % colors.length]
    }
  })
  
  return (
    <LuminarBarChart
      data={chartData}
      height={height}
      showGrid={showGrid}
      showValues={true}
      glass={true}
      animated={true}
      orientation={layout === 'vertical' ? 'horizontal' : 'vertical'}
      className={cn("w-full", className)}
    />
  )
}