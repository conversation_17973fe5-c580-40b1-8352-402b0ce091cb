import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { VendorEditForm } from './VendorEditForm'

describe('VendorEditForm', () => {
  const mockVendor = {
    id: '1',
    name: 'Tech Solutions Inc.',
    category: 'IT Services',
    email: '<EMAIL>',
    phone: '******-0123',
    address: '123 Tech Street, New York, NY 10001',
    website: 'https://techsolutions.com',
    taxId: '12-3456789',
    description: 'Leading IT services provider',
    status: 'active' as const,
    qualificationStatus: 'qualified' as const,
    riskLevel: 'low' as const,
    rating: 4.5,
  }

  const defaultProps = {
    vendor: mockVendor,
    onSubmit: vi.fn(),
    onCancel: vi.fn(),
    isLoading: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders form with vendor data', () => {
    render(<VendorEditForm {...defaultProps} />)

    expect(screen.getByDisplayValue('Tech Solutions Inc.')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByDisplayValue('******-0123')).toBeInTheDocument()
    expect(screen.getByDisplayValue('123 Tech Street, New York, NY 10001')).toBeInTheDocument()
    expect(screen.getByDisplayValue('https://techsolutions.com')).toBeInTheDocument()
    expect(screen.getByDisplayValue('12-3456789')).toBeInTheDocument()
  })

  it('displays category dropdown with correct value', () => {
    render(<VendorEditForm {...defaultProps} />)

    const categorySelect = screen.getByLabelText(/category/i)
    expect(categorySelect).toHaveValue('IT Services')
  })

  it('updates form fields when edited', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const nameInput = screen.getByLabelText(/vendor name/i)
    await user.clear(nameInput)
    await user.type(nameInput, 'New Tech Solutions')

    expect(nameInput).toHaveValue('New Tech Solutions')
  })

  it('validates required fields', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    // Clear required field
    const nameInput = screen.getByLabelText(/vendor name/i)
    await user.clear(nameInput)

    // Try to submit
    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    expect(screen.getByText(/vendor name is required/i)).toBeInTheDocument()
    expect(defaultProps.onSubmit).not.toHaveBeenCalled()
  })

  it('validates email format', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const emailInput = screen.getByLabelText(/email/i)
    await user.clear(emailInput)
    await user.type(emailInput, 'invalid-email')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    expect(screen.getByText(/invalid email address/i)).toBeInTheDocument()
  })

  it('validates phone number format', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const phoneInput = screen.getByLabelText(/phone/i)
    await user.clear(phoneInput)
    await user.type(phoneInput, '123')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    expect(screen.getByText(/invalid phone number/i)).toBeInTheDocument()
  })

  it('validates website URL format', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const websiteInput = screen.getByLabelText(/website/i)
    await user.clear(websiteInput)
    await user.type(websiteInput, 'not-a-url')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    expect(screen.getByText(/invalid url/i)).toBeInTheDocument()
  })

  it('submits form with valid data', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const nameInput = screen.getByLabelText(/vendor name/i)
    await user.clear(nameInput)
    await user.type(nameInput, 'Updated Tech Solutions')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith({
        ...mockVendor,
        name: 'Updated Tech Solutions',
      })
    })
  })

  it('calls onCancel when cancel button is clicked', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const cancelButton = screen.getByRole('button', { name: /cancel/i })
    await user.click(cancelButton)

    expect(defaultProps.onCancel).toHaveBeenCalled()
  })

  it('disables form when loading', () => {
    render(<VendorEditForm {...defaultProps} isLoading={true} />)

    const nameInput = screen.getByLabelText(/vendor name/i)
    const submitButton = screen.getByRole('button', { name: /saving/i })

    expect(nameInput).toBeDisabled()
    expect(submitButton).toBeDisabled()
  })

  it('shows loading text on submit button when loading', () => {
    render(<VendorEditForm {...defaultProps} isLoading={true} />)

    expect(screen.getByRole('button', { name: /saving/i })).toBeInTheDocument()
  })

  it('handles status selection', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const statusSelect = screen.getByLabelText(/status/i)
    await user.selectOptions(statusSelect, 'inactive')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'inactive',
        })
      )
    })
  })

  it('handles qualification status selection', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const qualificationSelect = screen.getByLabelText(/qualification status/i)
    await user.selectOptions(qualificationSelect, 'pending')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          qualificationStatus: 'pending',
        })
      )
    })
  })

  it('handles risk level selection', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const riskSelect = screen.getByLabelText(/risk level/i)
    await user.selectOptions(riskSelect, 'high')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          riskLevel: 'high',
        })
      )
    })
  })

  it('renders in create mode when no vendor is provided', () => {
    render(<VendorEditForm onSubmit={vi.fn()} onCancel={vi.fn()} />)

    expect(screen.getByText(/add new vendor/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create vendor/i })).toBeInTheDocument()
  })

  it('clears validation errors when field is corrected', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    // Clear and submit to trigger error
    const nameInput = screen.getByLabelText(/vendor name/i)
    await user.clear(nameInput)
    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    expect(screen.getByText(/vendor name is required/i)).toBeInTheDocument()

    // Type valid value
    await user.type(nameInput, 'Valid Name')

    // Error should disappear
    expect(screen.queryByText(/vendor name is required/i)).not.toBeInTheDocument()
  })

  it('handles category change', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const categorySelect = screen.getByLabelText(/category/i)
    await user.selectOptions(categorySelect, 'Consulting')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          category: 'Consulting',
        })
      )
    })
  })

  it('preserves other fields when updating one field', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const descriptionInput = screen.getByLabelText(/description/i)
    await user.clear(descriptionInput)
    await user.type(descriptionInput, 'Updated description')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith({
        ...mockVendor,
        description: 'Updated description',
      })
    })
  })

  it('trims whitespace from input values', async () => {
    const { user } = render(<VendorEditForm {...defaultProps} />)

    const nameInput = screen.getByLabelText(/vendor name/i)
    await user.clear(nameInput)
    await user.type(nameInput, '  Trimmed Name  ')

    const submitButton = screen.getByRole('button', { name: /save changes/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Trimmed Name',
        })
      )
    })
  })
})