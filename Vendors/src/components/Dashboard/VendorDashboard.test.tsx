import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { VendorDashboard } from './VendorDashboard'
import { useAuthStore } from '@luminar/shared-auth'
import { useVendorStore } from '../../stores/vendorStore'

// Mock the stores
vi.mock('@luminar/shared-auth', () => ({
  useAuthStore: vi.fn(),
}))

vi.mock('../../stores/vendorStore', () => ({
  useVendorStore: vi.fn(),
}))

// Mock child components
vi.mock('../../components/analytics/VendorPerformanceChart', () => ({
  VendorPerformanceChart: () => <div data-testid="performance-chart">Performance Chart</div>,
}))

vi.mock('../../components/analytics/VendorDistributionChart', () => ({
  VendorDistributionChart: () => <div data-testid="distribution-chart">Distribution Chart</div>,
}))

describe('VendorDashboard', () => {
  const mockUser = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  }

  const mockVendors = [
    {
      id: 'v1',
      name: 'Tech Solutions Inc.',
      category: 'IT Services',
      qualificationStatus: 'qualified',
      riskLevel: 'low',
      status: 'active',
    },
    {
      id: 'v2',
      name: 'Global Consulting',
      category: 'Consulting',
      qualificationStatus: 'qualified',
      riskLevel: 'high',
      status: 'active',
    },
    {
      id: 'v3',
      name: 'Marketing Pro',
      category: 'Marketing',
      qualificationStatus: 'pending',
      riskLevel: 'medium',
      status: 'active',
    },
  ]

  const mockContracts = [
    {
      id: 'c1',
      vendorId: 'v1',
      value: 150000,
      status: 'active',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
    },
    {
      id: 'c2',
      vendorId: 'v2',
      value: 200000,
      status: 'active',
      startDate: '2024-01-15',
      endDate: '2024-02-15', // Expiring soon
    },
    {
      id: 'c3',
      vendorId: 'v3',
      value: 50000,
      status: 'completed',
      startDate: '2023-01-01',
      endDate: '2023-12-31',
    },
  ]

  const mockEvaluations = [
    {
      id: 'e1',
      vendorId: 'v1',
      overallScore: 90,
      status: 'completed',
      completedAt: '2024-01-10',
      createdAt: '2024-01-05',
    },
    {
      id: 'e2',
      vendorId: 'v2',
      overallScore: 75,
      status: 'completed',
      completedAt: '2024-01-08',
      createdAt: '2024-01-03',
    },
    {
      id: 'e3',
      vendorId: 'v3',
      overallScore: 0,
      status: 'pending',
      createdAt: '2024-01-12',
    },
  ]

  const mockVendorStore = {
    vendors: mockVendors,
    contracts: mockContracts,
    evaluations: mockEvaluations,
    analytics: {},
    fetchVendors: vi.fn().mockResolvedValue(undefined),
    fetchContracts: vi.fn().mockResolvedValue(undefined),
    fetchEvaluations: vi.fn().mockResolvedValue(undefined),
    fetchAnalytics: vi.fn().mockResolvedValue(undefined),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAuthStore as any).mockReturnValue({ user: mockUser })
    ;(useVendorStore as any).mockReturnValue(mockVendorStore)
  })

  it('renders loading state initially', () => {
    render(<VendorDashboard />)
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('fetches all data on mount', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      expect(mockVendorStore.fetchVendors).toHaveBeenCalled()
      expect(mockVendorStore.fetchContracts).toHaveBeenCalled()
      expect(mockVendorStore.fetchEvaluations).toHaveBeenCalled()
      expect(mockVendorStore.fetchAnalytics).toHaveBeenCalled()
    })
  })

  it('displays vendor statistics correctly', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // Total vendors
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('Total Vendors')).toBeInTheDocument()

      // Active contracts
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('Active Contracts')).toBeInTheDocument()

      // Pending evaluations
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('Pending Evaluations')).toBeInTheDocument()
    })
  })

  it('calculates total contract value correctly', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // Only active contracts: 150000 + 200000 = 350000
      expect(screen.getByText('$350,000')).toBeInTheDocument()
      expect(screen.getByText('Total Contract Value')).toBeInTheDocument()
    })
  })

  it('calculates average performance score correctly', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // Average of completed evaluations: (90 + 75) / 2 = 82.5, rounded to 83
      expect(screen.getByText('83%')).toBeInTheDocument()
      expect(screen.getByText('Avg Performance')).toBeInTheDocument()
    })
  })

  it('identifies contracts expiring soon', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // Contract c2 expires in less than 30 days
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('Expiring Soon')).toBeInTheDocument()
    })
  })

  it('counts qualified vendors correctly', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // v1 and v2 are qualified
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('Qualified Vendors')).toBeInTheDocument()
    })
  })

  it('identifies high-risk vendors', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // v2 has high risk level
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('Risk Vendors')).toBeInTheDocument()
    })
  })

  it('displays vendor distribution by category', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      expect(screen.getByTestId('distribution-chart')).toBeInTheDocument()
    })
  })

  it('shows performance trends chart', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      expect(screen.getByTestId('performance-chart')).toBeInTheDocument()
    })
  })

  it('displays top performing vendors', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // v1 has highest score (90)
      expect(screen.getByText('Tech Solutions Inc.')).toBeInTheDocument()
      expect(screen.getByText('90')).toBeInTheDocument()
    })
  })

  it('shows recent activity', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      expect(screen.getByText(/recent contracts/i)).toBeInTheDocument()
      expect(screen.getByText(/recent evaluations/i)).toBeInTheDocument()
    })
  })

  it('handles error state gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    mockVendorStore.fetchVendors.mockRejectedValueOnce(new Error('API Error'))

    render(<VendorDashboard />)

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error loading dashboard data:',
        expect.any(Error)
      )
    })

    consoleErrorSpy.mockRestore()
  })

  it('handles empty data state', async () => {
    ;(useVendorStore as any).mockReturnValue({
      ...mockVendorStore,
      vendors: [],
      contracts: [],
      evaluations: [],
    })

    render(<VendorDashboard />)

    await waitFor(() => {
      expect(screen.getAllByText('0')[0]).toBeInTheDocument()
      expect(screen.getByText('Total Vendors')).toBeInTheDocument()
    })
  })

  it('formats currency values correctly', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // Check for formatted currency
      expect(screen.getByText('$350,000')).toBeInTheDocument()
    })
  })

  it('calculates vendor distribution percentages', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // Each category has 1 vendor out of 3 total = 33%
      // The component should calculate and display these percentages
      expect(screen.getByTestId('distribution-chart')).toBeInTheDocument()
    })
  })

  it('sorts top vendors by average score', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      const vendorElements = screen.getAllByRole('heading', { level: 4 })
      // First vendor should be Tech Solutions Inc. with score 90
      expect(vendorElements[0]).toHaveTextContent('Tech Solutions Inc.')
    })
  })

  it('filters only active contracts for statistics', async () => {
    render(<VendorDashboard />)

    await waitFor(() => {
      // Should show 2 active contracts, not 3 total
      expect(screen.getByText('2')).toBeInTheDocument()
      expect(screen.getByText('Active Contracts')).toBeInTheDocument()
    })
  })

  it('updates statistics when data changes', async () => {
    const { rerender } = render(<VendorDashboard />)

    await waitFor(() => {
      expect(screen.getByText('3')).toBeInTheDocument()
    })

    // Update vendor store with new data
    const updatedStore = {
      ...mockVendorStore,
      vendors: [...mockVendors, { id: 'v4', name: 'New Vendor', category: 'Other' }],
    }
    ;(useVendorStore as any).mockReturnValue(updatedStore)

    rerender(<VendorDashboard />)

    await waitFor(() => {
      expect(screen.getByText('4')).toBeInTheDocument()
    })
  })
})