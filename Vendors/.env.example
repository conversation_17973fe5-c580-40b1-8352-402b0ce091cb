# ===================================================================
# Vendors (Vendor Management Platform) - Environment Configuration
# ===================================================================
# Vendors is a React-based vendor management system for proposal
# management, vendor evaluation, and procurement workflows.

# ===================================================================
# Application Configuration
# ===================================================================
VITE_APP_TITLE=Vendor Management Platform
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=development

# ===================================================================
# API Configuration
# ===================================================================
# Main backend API endpoint (Command-Center)
VITE_API_BASE_URL=http://localhost:3000/api

# Vendor-specific endpoints
VITE_VENDORS_API_URL=http://localhost:3000/api/vendors
VITE_PROPOSALS_API_URL=http://localhost:3000/api/proposals
VITE_CONTRACTS_API_URL=http://localhost:3000/api/contracts
VITE_REVIEWS_API_URL=http://localhost:3000/api/reviews

# API settings
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3
VITE_API_RATE_LIMIT=200

# ===================================================================
# Authentication Configuration
# ===================================================================
# User authentication
VITE_AUTH_TOKEN_KEY=vendors_auth_token
VITE_SESSION_TIMEOUT=60
VITE_ENABLE_SSO=true

# Role-based access
VITE_ENABLE_ROLE_BASED_ACCESS=true
VITE_DEFAULT_USER_ROLE=viewer
VITE_ADMIN_ROLES=admin,procurement_manager,vendor_manager

# ===================================================================
# Vendor Management
# ===================================================================
# Vendor registration
VITE_ENABLE_VENDOR_REGISTRATION=true
VITE_REQUIRE_VENDOR_APPROVAL=true
VITE_ENABLE_VENDOR_VERIFICATION=true

# Vendor categories
VITE_ENABLE_VENDOR_CATEGORIZATION=true
VITE_MAX_CATEGORIES_PER_VENDOR=5
VITE_ENABLE_CUSTOM_CATEGORIES=true

# Vendor profiles
VITE_ENABLE_VENDOR_PROFILES=true
VITE_ENABLE_VENDOR_DOCUMENTS=true
VITE_ENABLE_VENDOR_CERTIFICATIONS=true
VITE_ENABLE_VENDOR_RATINGS=true

# ===================================================================
# Proposal Management
# ===================================================================
# RFP (Request for Proposal) process
VITE_ENABLE_RFP_CREATION=true
VITE_ENABLE_RFP_TEMPLATES=true
VITE_MAX_RFP_DURATION_DAYS=90

# Proposal submission
VITE_ENABLE_PROPOSAL_SUBMISSION=true
VITE_MAX_PROPOSAL_SIZE_MB=50
VITE_ALLOWED_PROPOSAL_FORMATS=pdf,doc,docx,xls,xlsx,ppt,pptx

# Proposal evaluation
VITE_ENABLE_PROPOSAL_SCORING=true
VITE_ENABLE_MULTI_CRITERIA_EVALUATION=true
VITE_ENABLE_BLIND_EVALUATION=true

# ===================================================================
# Contract Management
# ===================================================================
# Contract lifecycle
VITE_ENABLE_CONTRACT_MANAGEMENT=true
VITE_ENABLE_CONTRACT_TEMPLATES=true
VITE_ENABLE_DIGITAL_SIGNATURES=false

# Contract tracking
VITE_ENABLE_CONTRACT_MILESTONES=true
VITE_ENABLE_PAYMENT_TRACKING=true
VITE_ENABLE_RENEWAL_REMINDERS=true

# Compliance
VITE_ENABLE_COMPLIANCE_TRACKING=true
VITE_ENABLE_SLA_MONITORING=true
VITE_ENABLE_PERFORMANCE_METRICS=true

# ===================================================================
# Vendor Evaluation and Reviews
# ===================================================================
# Performance reviews
VITE_ENABLE_VENDOR_REVIEWS=true
VITE_ENABLE_PERIODIC_REVIEWS=true
VITE_REVIEW_FREQUENCY_MONTHS=6

# Rating system
VITE_RATING_SCALE=5
VITE_ENABLE_WEIGHTED_RATINGS=true
VITE_RATING_CRITERIA=quality,delivery,cost,support

# Feedback management
VITE_ENABLE_VENDOR_FEEDBACK=true
VITE_ENABLE_ANONYMOUS_FEEDBACK=true
VITE_ENABLE_IMPROVEMENT_PLANS=true

# ===================================================================
# Procurement Workflows
# ===================================================================
# Approval workflows
VITE_ENABLE_APPROVAL_WORKFLOWS=true
VITE_MAX_APPROVAL_LEVELS=5
VITE_ENABLE_PARALLEL_APPROVALS=true

# Purchase orders
VITE_ENABLE_PURCHASE_ORDERS=true
VITE_ENABLE_PO_TEMPLATES=true
VITE_ENABLE_PO_TRACKING=true

# Budget management
VITE_ENABLE_BUDGET_TRACKING=true
VITE_ENABLE_BUDGET_ALERTS=true
VITE_BUDGET_ALERT_THRESHOLD=80

# ===================================================================
# Analytics and Reporting
# ===================================================================
# Vendor analytics
VITE_ENABLE_VENDOR_ANALYTICS=true
VITE_ENABLE_SPEND_ANALYSIS=true
VITE_ENABLE_PERFORMANCE_DASHBOARDS=true

# Reports
VITE_ENABLE_VENDOR_REPORTS=true
VITE_ENABLE_PROPOSAL_REPORTS=true
VITE_ENABLE_CONTRACT_REPORTS=true
VITE_ENABLE_COMPLIANCE_REPORTS=true

# Export options
VITE_ENABLE_REPORT_EXPORT=true
VITE_EXPORT_FORMATS=pdf,excel,csv

# ===================================================================
# Communication Management
# ===================================================================
# Vendor communication
VITE_ENABLE_VENDOR_PORTAL=true
VITE_ENABLE_MESSAGING_SYSTEM=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true

# Announcements
VITE_ENABLE_ANNOUNCEMENTS=true
VITE_ENABLE_TENDER_ANNOUNCEMENTS=true
VITE_ENABLE_POLICY_UPDATES=true

# ===================================================================
# Document Management
# ===================================================================
# Document handling
VITE_ENABLE_DOCUMENT_MANAGEMENT=true
VITE_MAX_DOCUMENT_SIZE_MB=25
VITE_ALLOWED_DOCUMENT_TYPES=pdf,doc,docx,xls,xlsx,ppt,pptx,jpg,png

# Version control
VITE_ENABLE_DOCUMENT_VERSIONING=true
VITE_ENABLE_DOCUMENT_APPROVAL=true
VITE_ENABLE_DOCUMENT_SHARING=true

# ===================================================================
# Risk Management
# ===================================================================
# Risk assessment
VITE_ENABLE_RISK_ASSESSMENT=true
VITE_ENABLE_VENDOR_RISK_SCORING=true
VITE_RISK_CATEGORIES=financial,operational,compliance,security

# Risk monitoring
VITE_ENABLE_RISK_MONITORING=true
VITE_RISK_REVIEW_FREQUENCY_MONTHS=3
VITE_ENABLE_RISK_ALERTS=true

# ===================================================================
# UI/UX Configuration
# ===================================================================
# Theme and appearance
VITE_DEFAULT_THEME=light
VITE_ENABLE_THEME_SWITCHING=true
VITE_ENABLE_DARK_MODE=true

# Dashboard configuration
VITE_DEFAULT_DASHBOARD=overview
VITE_ENABLE_DASHBOARD_CUSTOMIZATION=true
VITE_WIDGET_REFRESH_INTERVAL=300000

# Data tables
VITE_ITEMS_PER_PAGE=25
VITE_ENABLE_ADVANCED_FILTERING=true
VITE_ENABLE_BULK_OPERATIONS=true

# ===================================================================
# Notification Configuration
# ===================================================================
# System notifications
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true
VITE_ENABLE_SMS_NOTIFICATIONS=false

# Workflow notifications
VITE_ENABLE_APPROVAL_NOTIFICATIONS=true
VITE_ENABLE_DEADLINE_NOTIFICATIONS=true
VITE_ENABLE_MILESTONE_NOTIFICATIONS=true

# ===================================================================
# Integration Configuration
# ===================================================================
# ERP integration
VITE_ENABLE_ERP_INTEGRATION=false
VITE_ERP_SYSTEM=sap
VITE_ERP_API_URL=

# Financial system integration
VITE_ENABLE_FINANCIAL_INTEGRATION=false
VITE_FINANCIAL_SYSTEM_API_URL=

# Email integration
VITE_ENABLE_EMAIL_INTEGRATION=true
VITE_EMAIL_PROVIDER=smtp

# ===================================================================
# Security Configuration
# ===================================================================
# Data protection
VITE_ENABLE_DATA_ENCRYPTION=true
VITE_ENABLE_SECURE_STORAGE=true
VITE_ENABLE_AUDIT_LOGGING=true

# Access control
VITE_ENABLE_IP_RESTRICTIONS=false
VITE_ENABLE_2FA=false
VITE_SESSION_TIMEOUT_WARNING_MINUTES=5

# ===================================================================
# External Services
# ===================================================================
# Error tracking
VITE_SENTRY_DSN=

# Analytics
VITE_GOOGLE_ANALYTICS_ID=
VITE_ENABLE_USER_ANALYTICS=true

# Support
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_HELP_DESK_URL=

# ===================================================================
# Cross-App Integration
# ===================================================================
# Integration with other Luminar applications
VITE_AMNA_URL=http://localhost:5173
VITE_ECONNECT_URL=http://localhost:5174
VITE_LIGHTHOUSE_URL=http://localhost:5175
VITE_DASHBOARD_URL=http://localhost:5176
VITE_TRAINING_URL=http://localhost:5177
VITE_WINS_URL=http://localhost:5179

# Shared services
VITE_SHARED_AUTH_ENABLED=true
VITE_SHARED_AUTH_DOMAIN=localhost
VITE_CROSS_APP_NAVIGATION=true

# ===================================================================
# Development Configuration
# ===================================================================
# Development server
VITE_DEV_PORT=5178
VITE_HMR_PORT=5178

# Development tools
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_ROUTER_DEVTOOLS=true

# Debug settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
VITE_ENABLE_MOCK_DATA=false

# ===================================================================
# Testing Configuration
# ===================================================================
# Test environment
VITE_TEST_MODE=false
VITE_ENABLE_MSW=false
VITE_TEST_API_URL=http://localhost:3001/api

# Test data
VITE_USE_MOCK_VENDORS=false
VITE_MOCK_VENDORS_COUNT=20
VITE_MOCK_PROPOSALS_COUNT=10

# ===================================================================
# Performance Configuration
# ===================================================================
# Caching
VITE_CACHE_STRATEGY=memory
VITE_CACHE_DURATION=1800000
VITE_ENABLE_OFFLINE_MODE=false

# Loading optimization
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_PAGINATION_SIZE=25

# ===================================================================
# Build Configuration
# ===================================================================
# Build settings
VITE_BUILD_OUTPUT_DIR=dist
VITE_PUBLIC_PATH=/

# Optimization
VITE_ENABLE_MINIFICATION=true
VITE_ENABLE_COMPRESSION=true
VITE_GENERATE_SOURCE_MAPS=true

# Bundle analysis
VITE_BUNDLE_ANALYZER=false
VITE_CHUNK_SIZE_WARNING_LIMIT=500