#!/bin/bash

# Luminar Full Stack Validation Script
# This script validates the Docker Compose configuration and performs health checks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.full-stack.yml"
TIMEOUT=300  # 5 minutes timeout for services to start
CHECK_INTERVAL=10

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✓${NC} $1"
}

warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

error() {
    echo -e "${RED}✗${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
        exit 1
    fi
    success "Docker found: $(docker --version)"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    success "Docker Compose found: $(docker-compose --version)"
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running"
        exit 1
    fi
    success "Docker daemon is running"
    
    # Check available disk space (minimum 10GB)
    available_space=$(df . | awk 'NR==2 {print $4}')
    if [ $available_space -lt 10485760 ]; then  # 10GB in KB
        warning "Low disk space detected. Recommended: 10GB+ available"
    else
        success "Sufficient disk space available"
    fi
    
    # Check available memory (minimum 8GB)
    available_memory=$(free -m | awk 'NR==2{print $7}')
    if [ $available_memory -lt 8192 ]; then  # 8GB in MB
        warning "Low memory detected. Recommended: 8GB+ available RAM"
    else
        success "Sufficient memory available"
    fi
}

# Validate Docker Compose file
validate_compose_file() {
    log "Validating Docker Compose file..."
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Docker Compose file not found: $COMPOSE_FILE"
        exit 1
    fi
    
    # Validate syntax
    if docker-compose -f "$COMPOSE_FILE" config > /dev/null 2>&1; then
        success "Docker Compose file syntax is valid"
    else
        error "Docker Compose file has syntax errors"
        docker-compose -f "$COMPOSE_FILE" config
        exit 1
    fi
    
    # Check for required environment variables
    if [ ! -f .env ]; then
        warning "No .env file found. Using default values"
        if [ -f .env.full-stack.example ]; then
            log "Copying example environment file..."
            cp .env.full-stack.example .env
            success "Created .env from example file"
        fi
    else
        success "Environment file found"
    fi
}

# Check port availability
check_ports() {
    log "Checking port availability..."
    
    ports=(80 443 3000 3001 3002 3003 3004 3005 3006 3007 5432 6379 9200 5672 15672 6333 11434 9000 9001 9090 3100 16686)
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            warning "Port $port is already in use"
        else
            success "Port $port is available"
        fi
    done
}

# Create required directories
create_directories() {
    log "Creating required directories..."
    
    directories=(
        "data/postgres"
        "data/redis" 
        "data/elasticsearch"
        "data/rabbitmq"
        "data/qdrant"
        "data/ollama"
        "data/minio"
        "data/prometheus"
        "data/grafana"
        "nginx/ssl"
        "Command-Center/logs"
        "Command-Center/uploads"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            success "Created directory: $dir"
        else
            success "Directory exists: $dir"
        fi
    done
    
    # Set proper permissions
    chmod -R 755 data/ 2>/dev/null || true
    success "Set directory permissions"
}

# Build services
build_services() {
    log "Building Docker images..."
    
    if docker-compose -f "$COMPOSE_FILE" build --no-cache; then
        success "All Docker images built successfully"
    else
        error "Failed to build Docker images"
        exit 1
    fi
}

# Start infrastructure services first
start_infrastructure() {
    log "Starting infrastructure services..."
    
    infrastructure_services=(
        "postgres"
        "redis"
        "elasticsearch"
        "rabbitmq"
        "qdrant"
        "ollama"
        "minio"
    )
    
    for service in "${infrastructure_services[@]}"; do
        log "Starting $service..."
        docker-compose -f "$COMPOSE_FILE" up -d "$service"
    done
    
    log "Waiting for infrastructure services to become healthy..."
    wait_for_services "${infrastructure_services[@]}"
}

# Start application services
start_applications() {
    log "Starting application services..."
    
    # Start backend
    docker-compose -f "$COMPOSE_FILE" up -d command-center
    wait_for_services "command-center"
    
    # Start frontend applications
    frontend_services=(
        "amna-frontend"
        "e-connect-frontend"
        "lighthouse-frontend"
        "luminar-dashboard"
        "training-need-analysis"
        "vendors-frontend"
        "wins-of-week"
    )
    
    for service in "${frontend_services[@]}"; do
        log "Starting $service..."
        docker-compose -f "$COMPOSE_FILE" up -d "$service"
    done
    
    wait_for_services "${frontend_services[@]}"
}

# Start monitoring services
start_monitoring() {
    log "Starting monitoring services..."
    
    monitoring_services=(
        "prometheus"
        "grafana"
        "jaeger"
    )
    
    for service in "${monitoring_services[@]}"; do
        docker-compose -f "$COMPOSE_FILE" up -d "$service"
    done
    
    wait_for_services "${monitoring_services[@]}"
}

# Start proxy
start_proxy() {
    log "Starting reverse proxy..."
    docker-compose -f "$COMPOSE_FILE" up -d nginx
    wait_for_services "nginx"
}

# Wait for services to become healthy
wait_for_services() {
    local services=("$@")
    local start_time=$(date +%s)
    
    for service in "${services[@]}"; do
        log "Waiting for $service to become healthy..."
        
        while true; do
            local current_time=$(date +%s)
            local elapsed=$((current_time - start_time))
            
            if [ $elapsed -gt $TIMEOUT ]; then
                error "Timeout waiting for $service to become healthy"
                docker-compose -f "$COMPOSE_FILE" logs "$service"
                exit 1
            fi
            
            local status=$(docker-compose -f "$COMPOSE_FILE" ps -q "$service" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "no-health-check")
            
            if [ "$status" = "healthy" ] || [ "$status" = "no-health-check" ]; then
                # For services without health check, check if container is running
                if [ "$status" = "no-health-check" ]; then
                    local running=$(docker-compose -f "$COMPOSE_FILE" ps -q "$service" | xargs docker inspect --format='{{.State.Running}}' 2>/dev/null || echo "false")
                    if [ "$running" = "true" ]; then
                        success "$service is running"
                        break
                    fi
                else
                    success "$service is healthy"
                    break
                fi
            elif [ "$status" = "unhealthy" ]; then
                error "$service is unhealthy"
                docker-compose -f "$COMPOSE_FILE" logs "$service"
                exit 1
            fi
            
            sleep $CHECK_INTERVAL
        done
    done
}

# Perform health checks
health_checks() {
    log "Performing application health checks..."
    
    # Check API endpoint
    if curl -sf http://localhost:3000/health >/dev/null 2>&1; then
        success "Command Center API is responding"
    else
        warning "Command Center API health check failed"
    fi
    
    # Check frontend applications
    frontends=(
        "3001:AMNA"
        "3002:E-Connect"
        "3003:Lighthouse"
        "3004:Dashboard"
        "3005:Training"
        "3006:Vendors"
        "3007:Wins"
    )
    
    for frontend in "${frontends[@]}"; do
        port=$(echo $frontend | cut -d: -f1)
        name=$(echo $frontend | cut -d: -f2)
        
        if curl -sf "http://localhost:$port/health" >/dev/null 2>&1; then
            success "$name frontend is responding"
        else
            warning "$name frontend health check failed"
        fi
    done
    
    # Check database connectivity
    if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
        success "PostgreSQL is ready"
    else
        warning "PostgreSQL connectivity check failed"
    fi
    
    # Check Redis connectivity
    if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping >/dev/null 2>&1; then
        success "Redis is responding"
    else
        warning "Redis connectivity check failed"
    fi
    
    # Check Elasticsearch
    if curl -sf http://localhost:9200/_cluster/health >/dev/null 2>&1; then
        success "Elasticsearch is responding"
    else
        warning "Elasticsearch health check failed"
    fi
}

# Display service status
show_status() {
    log "Service Status Summary:"
    echo
    docker-compose -f "$COMPOSE_FILE" ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    echo
    
    log "Access URLs:"
    echo -e "${GREEN}Main Portal:${NC}     http://localhost"
    echo -e "${GREEN}API Gateway:${NC}     http://localhost:3000"
    echo -e "${GREEN}AMNA:${NC}            http://localhost:3001"
    echo -e "${GREEN}E-Connect:${NC}       http://localhost:3002"
    echo -e "${GREEN}Lighthouse:${NC}      http://localhost:3003"
    echo -e "${GREEN}Dashboard:${NC}       http://localhost:3004"
    echo -e "${GREEN}Training:${NC}        http://localhost:3005"
    echo -e "${GREEN}Vendors:${NC}         http://localhost:3006"
    echo -e "${GREEN}Wins:${NC}            http://localhost:3007"
    echo -e "${GREEN}Grafana:${NC}         http://localhost:3100"
    echo -e "${GREEN}Jaeger:${NC}          http://localhost:16686"
    echo -e "${GREEN}RabbitMQ:${NC}        http://localhost:15672"
    echo
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        error "Validation failed. Cleaning up..."
        docker-compose -f "$COMPOSE_FILE" down --remove-orphans
    fi
}

# Main execution
main() {
    log "Starting Luminar Full Stack Validation"
    echo
    
    # Set cleanup trap
    trap cleanup EXIT
    
    # Run validation steps
    check_prerequisites
    validate_compose_file
    check_ports
    create_directories
    
    # Build and start services
    build_services
    start_infrastructure
    start_applications
    start_monitoring
    start_proxy
    
    # Perform health checks
    health_checks
    
    # Show final status
    show_status
    
    success "Luminar Full Stack deployment completed successfully!"
    log "All services are running and healthy"
}

# Script options
case "${1:-}" in
    "check")
        check_prerequisites
        validate_compose_file
        check_ports
        ;;
    "build")
        build_services
        ;;
    "start")
        start_infrastructure
        start_applications
        start_monitoring
        start_proxy
        ;;
    "health")
        health_checks
        ;;
    "status")
        show_status
        ;;
    "clean")
        log "Stopping and removing all services..."
        docker-compose -f "$COMPOSE_FILE" down -v --remove-orphans
        docker system prune -f
        success "Cleanup completed"
        ;;
    *)
        main
        ;;
esac