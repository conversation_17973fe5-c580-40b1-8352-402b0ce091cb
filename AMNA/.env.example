# ===================================================================
# AMNA (AI-Powered Chat Assistant) - Environment Configuration
# ===================================================================
# AMNA is a React-based AI chat application with real-time messaging,
# integration capabilities, and offline support.

# ===================================================================
# Application Configuration
# ===================================================================
VITE_APP_TITLE=AMNA AI Assistant
VITE_APP_VERSION=1.0.0
VITE_ENV=development

# ===================================================================
# API Configuration  
# ===================================================================
# Main backend API endpoint (Command-Center)
VITE_API_BASE_URL=http://localhost:3000/api

# WebSocket endpoint for real-time chat
VITE_WS_URL=ws://localhost:3000/ws/chat

# API timeout settings (in milliseconds)
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# ===================================================================
# Authentication Configuration
# ===================================================================
# JWT token storage key
VITE_AUTH_TOKEN_KEY=amna_auth_token

# Session timeout (in minutes)
VITE_SESSION_TIMEOUT=30

# Enable SSO integration
VITE_ENABLE_SSO=true

# Auth redirect URLs
VITE_AUTH_REDIRECT_URL=http://localhost:5173/auth/callback
VITE_AUTH_LOGOUT_URL=http://localhost:5173/auth/logout

# ===================================================================
# Feature Flags
# ===================================================================
# Use mock API for development/testing
VITE_USE_MOCK_API=false

# Enable WebSocket real-time features
VITE_ENABLE_WEBSOCKET=true

# Enable offline capabilities
VITE_ENABLE_OFFLINE=true

# Enable file upload functionality
VITE_ENABLE_FILE_UPLOAD=true

# Enable voice messages
VITE_ENABLE_VOICE_MESSAGES=false

# Enable chat export functionality
VITE_ENABLE_CHAT_EXPORT=true

# Enable personalization features
VITE_ENABLE_PERSONALIZATION=true

# Enable integration with other Luminar apps
VITE_ENABLE_CROSS_APP_INTEGRATION=true

# ===================================================================
# UI/UX Configuration
# ===================================================================
# Theme configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_THEME_SWITCHING=true

# Chat interface settings
VITE_MAX_MESSAGE_LENGTH=2000
VITE_MESSAGES_PER_PAGE=50
VITE_ENABLE_MESSAGE_REACTIONS=true
VITE_ENABLE_MESSAGE_THREADS=true

# File upload limits
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/*,text/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# ===================================================================
# External Services
# ===================================================================
# Sentry for error tracking
VITE_SENTRY_DSN=

# Analytics (Google Analytics, Mixpanel, etc.)
VITE_ANALYTICS_ID=
VITE_ENABLE_ANALYTICS=false

# Support contact
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_SUPPORT_CHAT_URL=

# ===================================================================
# Cross-App Integration
# ===================================================================
# Integration endpoints for other Luminar applications
VITE_ECONNECT_URL=http://localhost:5174
VITE_LIGHTHOUSE_URL=http://localhost:5175
VITE_DASHBOARD_URL=http://localhost:5176
VITE_TRAINING_URL=http://localhost:5177
VITE_VENDORS_URL=http://localhost:5178
VITE_WINS_URL=http://localhost:5179

# Cross-app authentication
VITE_CROSS_APP_AUTH_ENABLED=true
VITE_SHARED_AUTH_DOMAIN=localhost

# ===================================================================
# Development Configuration
# ===================================================================
# Hot reload settings
VITE_HMR_PORT=5173

# Development tools
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_QUERY_DEVTOOLS=true

# Debug settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info

# ===================================================================
# Performance Configuration
# ===================================================================
# Caching settings
VITE_CACHE_DURATION=3600000
VITE_ENABLE_SERVICE_WORKER=true

# Lazy loading
VITE_ENABLE_LAZY_LOADING=true

# Bundle optimization
VITE_ENABLE_TREE_SHAKING=true

# ===================================================================
# Security Configuration
# ===================================================================
# Content Security Policy
VITE_CSP_ENABLED=true

# CORS configuration
VITE_CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# XSS Protection
VITE_XSS_PROTECTION=true

# ===================================================================
# Testing Configuration
# ===================================================================
# Enable test mode features
VITE_TEST_MODE=false

# Mock service worker
VITE_ENABLE_MSW=false

# Test API endpoints
VITE_TEST_API_URL=http://localhost:3001/api

# ===================================================================
# Build Configuration
# ===================================================================
# Build output directory
VITE_BUILD_OUTPUT_DIR=dist

# Asset optimization
VITE_ENABLE_ASSET_OPTIMIZATION=true

# Source maps
VITE_GENERATE_SOURCE_MAPS=true