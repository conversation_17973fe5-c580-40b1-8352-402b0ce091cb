import React from 'react'
import { Routes, Route, useLocation } from 'react-router-dom'
import { MessageSquare, Search, Code, Settings, ChevronLeft, ChevronRight, Download } from 'lucide-react'
import { ScrollToTop } from './components/ui/scroll-to-top'
import { cn } from './lib/utils'
import { Button, AMNAWidget, ThemeProvider, IntegrationProvider } from '@luminar/shared-ui'
import { ChatContainer } from './components/chat/ChatContainer'
import { ChatMessages } from './components/chat/ChatMessages'
import { ChatInput, type UploadedFile } from './components/chat/ChatInput'
import { ChatEmptyState } from './components/chat/ChatEmptyState'
import { MessageSearch } from './components/chat/MessageSearch'
import { ChatTemplates } from './components/chat/ChatTemplates'
import { QuickResponses } from './components/chat/QuickResponses'
import { PersonalizationPanel } from './components/chat/PersonalizationPanel'
import { UserPresence, UserPresenceList, TypingIndicator, useUserPresence } from './components/chat/UserPresence'
import { ExportChat } from './components/chat/ExportChat'
import { useChat } from './hooks/useChat'
import { usePersonalization } from './hooks/usePersonalization'
import {
  PageTransition,
  AnimatePresence,
} from './lib/animations'


const ChatPage = React.memo(function ChatPage() {
  const {
    messages,
    threads,
    sendMessage,
    addReaction,
    removeReaction,
    replyToMessage,
    replyToThread,
    toggleThread,
    scrollToMessage,
    editMessage,
    deleteMessage,
    lastMessage,
    isLoading,
    error,
    clearError,
  } = useChat()
  const { settings, updateSettings } = usePersonalization()
  const [input, setInput] = React.useState('')
  const [isSearchOpen, setIsSearchOpen] = React.useState(false)
  const [isTemplatesOpen, setIsTemplatesOpen] = React.useState(false)
  const [isPersonalizationOpen, setIsPersonalizationOpen] = React.useState(false)
  const [isExportOpen, setIsExportOpen] = React.useState(false)
  const [showQuickResponses, setShowQuickResponses] = React.useState(false)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = React.useState(true)
  const [isMobile, setIsMobile] = React.useState(false)
  
  // User presence
  const { presence, typingUsers, setTyping } = useUserPresence('current-user')
  
  // Mock online users for demo
  const [onlineUsers] = React.useState([
    { id: '1', name: 'AI Assistant', status: 'online' as const },
    { id: '2', name: 'Support Agent', status: 'away' as const },
  ])

  const handleSend = React.useCallback(
    (message: string) => {
      sendMessage(message)
    },
    [sendMessage],
  )

  const handleInputChange = React.useCallback((value: string) => {
    setInput(value)
  }, [])

  const handleInputSend = React.useCallback(
    (message: string, files?: UploadedFile[], voiceNote?: Blob) => {
      // TODO: Handle files and voice notes when implementing file upload and voice messages in useChat
      handleSend(message)
      setInput('')
    },
    [handleSend],
  )

  const handleSearchMessage = React.useCallback((messageId: string) => {
    scrollToMessage(messageId)
  }, [scrollToMessage])

  const handleOpenSearch = React.useCallback(() => {
    setIsSearchOpen(true)
  }, [])

  const handleCloseSearch = React.useCallback(() => {
    setIsSearchOpen(false)
  }, [])

  const handleOpenTemplates = React.useCallback(() => {
    setIsTemplatesOpen(true)
  }, [])

  const handleCloseTemplates = React.useCallback(() => {
    setIsTemplatesOpen(false)
  }, [])

  const handleOpenPersonalization = React.useCallback(() => {
    setIsPersonalizationOpen(true)
  }, [])

  const handleClosePersonalization = React.useCallback(() => {
    setIsPersonalizationOpen(false)
  }, [])

  const handleOpenExport = React.useCallback(() => {
    setIsExportOpen(true)
  }, [])

  const handleCloseExport = React.useCallback(() => {
    setIsExportOpen(false)
  }, [])

  const handleSelectTemplate = React.useCallback((template: any) => {
    let content = template.content

    // Replace placeholders with user input or sensible defaults
    content = content.replace(/\{selection\}/g, 'this')
    content = content.replace(/\{topic\}/g, 'this topic')
    content = content.replace(/\{concept\}/g, 'this concept')
    content = content.replace(/\{project\/topic\}/g, 'my project')
    content = content.replace(/\{type of writing\}/g, 'content')
    content = content.replace(/\{business topic\}/g, 'this business area')
    content = content.replace(/\{language\}/g, 'javascript')
    content = content.replace(/\{code\}/g, '// Your code here')

    setInput(content)
    setIsTemplatesOpen(false)
  }, [])

  const handleSelectQuickResponse = React.useCallback((response: string) => {
    setInput(response)
  }, [])

  // Show quick responses when there are messages and not loading (respect settings)
  React.useEffect(() => {
    if (messages.length > 0 && !isLoading && settings.interface.quickResponses) {
      setShowQuickResponses(true)
    } else {
      setShowQuickResponses(false)
    }
  }, [messages.length, isLoading, settings.interface.quickResponses])

  // Mobile detection and responsive behavior
  React.useEffect(() => {
    const checkMobile = () => {
      const isMobileScreen = window.innerWidth < 768
      setIsMobile(isMobileScreen)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsSearchOpen(true)
      }
      if ((e.metaKey || e.ctrlKey) && e.key === 't') {
        e.preventDefault()
        setIsTemplatesOpen(true)
      }
      if ((e.metaKey || e.ctrlKey) && e.key === ',') {
        e.preventDefault()
        setIsPersonalizationOpen(true)
      }
      if ((e.metaKey || e.ctrlKey) && e.key === 'b') {
        e.preventDefault()
        setIsSidebarCollapsed(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  // Touch/swipe gesture handling for mobile
  const touchStartX = React.useRef(0)
  const touchStartY = React.useRef(0)
  const touchEndX = React.useRef(0)
  const touchEndY = React.useRef(0)

  const handleTouchStart = React.useCallback((e: React.TouchEvent) => {
    if (!isMobile) {return}

    touchStartX.current = e.changedTouches[0].clientX
    touchStartY.current = e.changedTouches[0].clientY
  }, [isMobile])

  const handleTouchEnd = React.useCallback((e: React.TouchEvent) => {
    if (!isMobile) {return}

    touchEndX.current = e.changedTouches[0].clientX
    touchEndY.current = e.changedTouches[0].clientY

    const deltaX = touchEndX.current - touchStartX.current
    const deltaY = touchEndY.current - touchStartY.current

    // Check if it's a horizontal swipe (more horizontal than vertical)
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      // Swipe from left edge to open sidebar
      if (deltaX > 0 && touchStartX.current < 80 && isSidebarCollapsed) {
        setIsSidebarCollapsed(false)
      }
      // Swipe right to close sidebar  
      else if (deltaX < 0 && !isSidebarCollapsed) {
        setIsSidebarCollapsed(true)
      }
    }
  }, [isMobile, isSidebarCollapsed])

  const sidebarFeatures = [
    { icon: Search, label: 'Web Search' },
    { icon: Code, label: 'Component Analysis' },
  ]

  return (
    <PageTransition
      className="flex h-screen bg-background"
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {/* Mobile Overlay */}
      {isMobile && !isSidebarCollapsed && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden animate-in fade-in duration-200"
          onClick={() => setIsSidebarCollapsed(true)}
        />
      )}

      {/* Bookmark Tab - shown when sidebar is collapsed */}
      {isSidebarCollapsed && (
        <button
          onClick={() => setIsSidebarCollapsed(false)}
          className="fixed left-0 top-1/2 -translate-y-1/2 z-50 bg-primary text-primary-foreground px-3 py-4 rounded-r-lg shadow-lg hover:bg-primary/90 transition-all duration-300 hover:px-4 group"
          title="Open AI Chat Panel"
        >
          <div className="flex flex-col items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            <div className="text-xs font-medium transform -rotate-90 whitespace-nowrap">
              AI Chat
            </div>
          </div>
        </button>
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'border-r border-border bg-card relative z-50 transition-all duration-300 ease-out',
          isMobile && !isSidebarCollapsed && 'fixed left-0 top-0 h-full shadow-xl',
          isSidebarCollapsed ? 'w-0 overflow-hidden' : 'w-56 md:w-64 lg:w-72 xl:w-80',
        )}
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-6 w-6 text-primary transition-transform hover:rotate-12" />
                {!isSidebarCollapsed && (
                  <h1 className="text-xl font-semibold transition-all duration-200">
                    AI Chat
                  </h1>
                )}
              </div>
              {!isSidebarCollapsed && (
                <div className="mt-2">
                  <UserPresenceList users={onlineUsers} />
                </div>
              )}
            </div>
            <button
              onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
              className="p-1 hover:bg-accent rounded-md transition-colors hover:scale-105"
            >
              {isSidebarCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </button>
          </div>

          <div className="space-y-2">
            <button
              className={cn(
                'flex items-center gap-2 p-2 rounded-md hover:bg-accent text-sm bg-accent w-full transition-all duration-200',
                isSidebarCollapsed ? 'justify-center' : 'justify-start',
              )}
              onClick={() => {}}
              title={isSidebarCollapsed ? 'New Chat' : undefined}
            >
              <MessageSquare className="h-4 w-4" />
              {!isSidebarCollapsed && <span>New Chat</span>}
            </button>

            <button
              className={cn(
                'flex items-center gap-2 p-2 rounded-md hover:bg-accent text-sm w-full transition-all duration-200',
                isSidebarCollapsed ? 'justify-center' : 'justify-start',
              )}
              onClick={handleOpenSearch}
              title={isSidebarCollapsed ? 'Search Messages (⌘K)' : undefined}
            >
              <Search className="h-4 w-4" />
              {!isSidebarCollapsed && (
                <>
                  <span className="flex-1">Search Messages</span>
                  <span className="text-xs text-muted-foreground">⌘K</span>
                </>
              )}
            </button>

            <button
              className={cn(
                'flex items-center gap-2 p-2 rounded-md hover:bg-accent text-sm w-full transition-all duration-200',
                isSidebarCollapsed ? 'justify-center' : 'justify-start',
              )}
              onClick={handleOpenTemplates}
              title={isSidebarCollapsed ? 'Chat Templates (⌘T)' : undefined}
            >
              <MessageSquare className="h-4 w-4" />
              {!isSidebarCollapsed && (
                <>
                  <span className="flex-1">Chat Templates</span>
                  <span className="text-xs text-muted-foreground">⌘T</span>
                </>
              )}
            </button>

            <button
              className={cn(
                'flex items-center gap-2 p-2 rounded-md hover:bg-accent text-sm w-full transition-all duration-200',
                isSidebarCollapsed ? 'justify-center' : 'justify-start',
              )}
              onClick={handleOpenPersonalization}
              title={isSidebarCollapsed ? 'Personalization (⌘,)' : undefined}
            >
              <Settings className="h-4 w-4" />
              {!isSidebarCollapsed && (
                <>
                  <span className="flex-1">Personalization</span>
                  <span className="text-xs text-muted-foreground">⌘,</span>
                </>
              )}
            </button>

            <button
              className={cn(
                'flex items-center gap-2 p-2 rounded-md hover:bg-accent text-sm w-full transition-all duration-200',
                isSidebarCollapsed ? 'justify-center' : 'justify-start',
              )}
              onClick={handleOpenExport}
              title={isSidebarCollapsed ? 'Export Chat' : undefined}
            >
              <Download className="h-4 w-4" />
              {!isSidebarCollapsed && (
                <span className="flex-1">Export Chat</span>
              )}
            </button>

            <div className="pt-4">
              {!isSidebarCollapsed && (
                <h3 className="text-xs font-medium text-muted-foreground mb-2 uppercase tracking-wide">
                  Features
                </h3>
              )}
              <div className="space-y-1 text-sm text-muted-foreground">
                {sidebarFeatures.map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <div
                      key={index}
                      className={cn(
                        'flex items-center gap-2 p-1 rounded cursor-pointer transition-all duration-200 hover:text-primary',
                        isSidebarCollapsed ? 'justify-center' : 'hover:translate-x-1',
                      )}
                      title={isSidebarCollapsed ? feature.label : undefined}
                    >
                      <Icon className="h-3 w-3" />
                      {!isSidebarCollapsed && <span>{feature.label}</span>}
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div
        className={cn(
          'flex-1 flex flex-col min-w-0 transition-all duration-300',
          isMobile && !isSidebarCollapsed && 'pointer-events-none md:pointer-events-auto',
          isSidebarCollapsed ? 'ml-0' : 'ml-0',
        )}
      >
        <div className="flex-1 flex flex-col min-h-0">
        <ChatContainer>
          {messages.length === 0 ? (
            <div className="animate-in fade-in duration-300">
              <ChatEmptyState onSend={handleSend} />
            </div>
          ) : (
            <div className="flex-1 overflow-y-auto animate-in fade-in duration-300">
              <ChatMessages
                messages={messages}
                threads={threads}
                isLoading={isLoading}
                onAddReaction={addReaction}
                onRemoveReaction={removeReaction}
                onReply={replyToMessage}
                onReplyToThread={replyToThread}
                onToggleThread={toggleThread}
                onEdit={editMessage}
                onDelete={deleteMessage}
              />
              
              {/* Typing Indicator */}
              {typingUsers.length > 0 && (
                <div className="px-4 py-2">
                  <TypingIndicator users={typingUsers} />
                </div>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mx-4 mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg animate-in slide-in-from-top-2 fade-in duration-300">
              <div className="flex items-center justify-between">
                <p className="text-sm text-destructive">{error}</p>
                <button
                  onClick={clearError}
                  className="text-destructive hover:text-destructive/80 text-sm underline transition-colors"
                >
                  Dismiss
                </button>
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t">
            <ChatInput
              value={input}
              onChange={handleInputChange}
              onSend={handleInputSend}
              disabled={isLoading}
              loading={isLoading}
            />
          </div>

          {/* Quick Responses */}
          <AnimatePresence>
            {showQuickResponses && (
              <QuickResponses
                isVisible={showQuickResponses}
                onSelectResponse={handleSelectQuickResponse}
                onOpenTemplates={handleOpenTemplates}
                context={{
                  lastMessage: lastMessage?.content,
                  messageType: lastMessage?.messageType,
                  hasCode: lastMessage?.content.includes('```'),
                  hasError: lastMessage?.content.toLowerCase().includes('error'),
                }}
              />
            )}
          </AnimatePresence>
        </ChatContainer>
        </div>

        {/* Message Search Modal */}
        <AnimatePresence>
          {isSearchOpen && (
            <MessageSearch
              messages={messages}
              isOpen={isSearchOpen}
              onClose={handleCloseSearch}
              onMessageSelect={handleSearchMessage}
            />
          )}
        </AnimatePresence>

        {/* Chat Templates Modal */}
        <AnimatePresence>
          {isTemplatesOpen && (
            <ChatTemplates
              isOpen={isTemplatesOpen}
              onClose={handleCloseTemplates}
              onSelectTemplate={handleSelectTemplate}
            />
          )}
        </AnimatePresence>

        {/* Personalization Panel */}
        <AnimatePresence>
          {isPersonalizationOpen && (
            <PersonalizationPanel
              isOpen={isPersonalizationOpen}
              onClose={handleClosePersonalization}
              settings={settings}
              onSettingsChange={updateSettings}
            />
          )}
        </AnimatePresence>

        {/* Export Chat Modal */}
        <AnimatePresence>
          {isExportOpen && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={handleCloseExport}
            >
              <motion.div
                className="bg-background border border-border rounded-lg shadow-lg p-6 max-w-md w-full"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                <h2 className="text-lg font-semibold mb-4">Export Chat History</h2>
                <ExportChat messages={messages} chatTitle="AI Chat Session" />
                <Button
                  variant="outline"
                  className="w-full mt-4"
                  onClick={handleCloseExport}
                >
                  Close
                </Button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </PageTransition>
  )
})

const NotFoundPage = React.memo(function NotFoundPage() {
  return (
    <PageTransition className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto text-center">
        <div className="animate-in zoom-in duration-500">
          <h1 className="text-6xl font-bold mb-4 text-primary animate-pulse">
            404
          </h1>
          <p className="text-xl text-muted-foreground mb-8">Page not found</p>
          <button
            className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            onClick={() => window.location.href = '/'}
          >
            Go Home
          </button>
        </div>
      </div>
    </PageTransition>
  )
})

function App() {
  const location = useLocation()

  return (
    <ThemeProvider>
      <IntegrationProvider>
        <div className="min-h-screen bg-background">

      {/* Chat routes (full screen) with animations */}
      <AnimatePresence mode="wait" initial={false}>
        <Routes location={location} key={location.pathname}>
          <Route path="/" element={<ChatPage />} />
          <Route path="/chat/*" element={<ChatPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </AnimatePresence>


          <div className="animate-in slide-in-from-right-4 fade-in duration-400 delay-1200">
            <ScrollToTop />
          </div>

          {/* AMNAWidget for cross-app integration */}
          <AMNAWidget
            position="bottom-right"
            context={{ app: 'amna', environment: 'production' }}
            showBadge={true}
          />
        </div>
      </IntegrationProvider>
    </ThemeProvider>
  )
}

export default App
