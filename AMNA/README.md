# AMNA - AI-Powered Chat Assistant

## Overview

AMNA (AI-Powered Management & Navigation Assistant) is an intelligent chat interface that provides instant access to information, answers questions, and assists with various tasks across the Luminar L&D platform. Built with advanced AI capabilities, AMNA serves as your personal assistant for navigating the platform, understanding features, and getting work done efficiently.

## Key Features

### 🤖 Intelligent Conversations
- **Natural Language Processing**: Communicate naturally as you would with a colleague
- **Context Awareness**: AMNA remembers conversation history for relevant responses
- **Multi-turn Dialogues**: Engage in complex conversations with follow-up questions
- **Smart Suggestions**: Receive proactive suggestions based on your queries

### 🔍 Advanced Search & Discovery
- **Web Search Integration**: Access up-to-date information from the internet
- **Platform Navigation**: Find features and functions across all L&D applications
- **Document Analysis**: Upload and analyze documents for insights
- **Code Understanding**: Get help with technical implementations and code

### 💬 Rich Communication Features
- **Message Threading**: Organize conversations with reply threads
- **Reaction System**: React to messages with emojis for quick feedback
- **File Attachments**: Share documents, images, and other files
- **Voice Notes**: Record and send voice messages for hands-free communication
- **Message Search**: Find past conversations and specific information quickly

### 🎨 Personalization & Customization
- **Appearance Settings**: Choose themes and customize the interface
- **Response Preferences**: Set tone, detail level, and response style
- **Quick Responses**: Access frequently used prompts and templates
- **Chat Templates**: Use pre-built templates for common queries
- **Keyboard Shortcuts**: Navigate efficiently with keyboard commands

## User Guide

### Getting Started

1. **Access AMNA**: Click the AI Chat icon or navigate to the AMNA application
2. **Start Chatting**: Type your question or request in the input field
3. **Explore Features**: Use the sidebar to access templates, search, and settings

### Basic Conversations

#### Asking Questions
- Type naturally as you would ask a colleague
- Be specific for more accurate responses
- Use follow-up questions to dive deeper
- Examples:
  - "How do I create a training request?"
  - "What courses are available for Python programming?"
  - "Show me the vendor approval process"

#### Using Commands
- **@search**: Search for specific information
- **@help**: Get help with using AMNA
- **@navigate**: Find your way to specific features
- **@analyze**: Analyze uploaded documents

### Advanced Features

#### File Uploads
1. Click the attachment icon in the input field
2. Select files to upload (documents, images, spreadsheets)
3. Add a message describing what you need
4. AMNA will analyze and provide insights

#### Voice Messages
1. Click the microphone icon
2. Record your message
3. Review and send
4. AMNA will transcribe and respond

#### Message Threading
1. Hover over any message
2. Click "Reply" to start a thread
3. Continue focused discussions
4. Collapse/expand threads as needed

#### Using Templates
1. Click "Chat Templates" in the sidebar (or press ⌘T)
2. Browse available templates:
   - Technical questions
   - Process inquiries
   - Report generation
   - Data analysis
3. Select and customize as needed

### Personalization

#### Customizing Responses
1. Click Settings icon (or press ⌘,)
2. Adjust preferences:
   - **Response Length**: Concise, balanced, or detailed
   - **Tone**: Professional, friendly, or casual
   - **Technical Level**: Beginner, intermediate, or expert
   - **Language**: Multiple language support

#### Interface Customization
- **Theme**: Light, dark, or system preference
- **Font Size**: Adjust for readability
- **Message Density**: Compact or comfortable spacing
- **Quick Responses**: Enable/disable suggestions

### Productivity Tips

#### Keyboard Shortcuts
- **⌘K**: Search messages
- **⌘T**: Open templates
- **⌘,**: Open settings
- **⌘B**: Toggle sidebar
- **⌘Enter**: Send message
- **Shift+Enter**: New line in message

#### Quick Actions
- Start typing "/" for command suggestions
- Use "@" to mention specific topics
- Drag and drop files for instant upload
- Double-click messages to edit

## Common Use Cases

### For New Users
- **Platform Orientation**: "Give me a tour of the L&D platform"
- **Feature Discovery**: "What can I do in the vendor management system?"
- **Process Understanding**: "Explain the training approval process"
- **Quick Help**: "How do I submit my weekly wins?"

### For Daily Tasks
- **Information Lookup**: "What's the budget for Q3 training?"
- **Process Guidance**: "Walk me through creating a vendor proposal"
- **Data Analysis**: "Analyze this training completion report"
- **Document Creation**: "Help me write a training request justification"

### For Advanced Users
- **Integration Help**: "How do I integrate with the API?"
- **Custom Reports**: "Create a report of all Python courses with 4+ ratings"
- **Workflow Automation**: "Help me automate weekly report generation"
- **Technical Support**: "Debug this API integration issue"

### For Managers
- **Team Insights**: "Show me my team's training progress"
- **Budget Analysis**: "Analyze L&D spending by department"
- **Vendor Comparison**: "Compare top 3 e-learning vendors"
- **Report Generation**: "Create executive summary of Q2 achievements"

## Technical Setup

### Prerequisites
- Node.js 18+ installed
- pnpm package manager
- Access to the Luminar platform

### Installation

```bash
# Clone the repository
git clone [repository-url]

# Navigate to the project directory
cd AMNA

# Install dependencies
pnpm install

# Copy environment variables
cp .env.example .env

# Start the development server
pnpm dev
```

### Environment Configuration

Create a `.env` file with the following variables:

```env
VITE_API_URL=http://localhost:3001
VITE_AUTH_DOMAIN=your-auth-domain
VITE_PUBLIC_URL=http://localhost:3006
VITE_OPENAI_API_KEY=your-openai-key
VITE_ANTHROPIC_API_KEY=your-anthropic-key
```

### Build for Production

```bash
# Build the application
pnpm build

# Preview the production build
pnpm preview
```

## Troubleshooting

### Common Issues

**AMNA not responding**
- Check internet connection
- Verify API keys are configured
- Refresh the page
- Clear browser cache

**File upload failing**
- Check file size (max 10MB)
- Verify file type is supported
- Ensure proper permissions

**Search not working**
- Be more specific with search terms
- Try different keywords
- Check if filters are too restrictive

**Voice recording issues**
- Allow microphone permissions
- Check browser compatibility
- Ensure microphone is working

### Getting Help

- **In-App Help**: Type "@help" in chat
- **Documentation**: Refer to this guide
- **Support**: Contact <EMAIL>
- **Community**: Join the AMNA users group

## Best Practices

1. **Clear Communication**: Be specific in your requests
2. **Context Provision**: Give background for complex queries
3. **Iterative Refinement**: Use follow-up questions
4. **Template Usage**: Leverage templates for common tasks
5. **Feedback**: Rate responses to improve AMNA

## Privacy & Security

- **Data Protection**: All conversations are encrypted
- **Privacy Controls**: Delete messages and clear history
- **Access Control**: Role-based permissions apply
- **Compliance**: Meets data protection regulations

## Mobile Experience

- **Responsive Design**: Optimized for all screen sizes
- **Touch Gestures**: Swipe to navigate conversations
- **Voice Input**: Enhanced for mobile use
- **Offline Mode**: Basic features available offline

## Future Enhancements

- Multilingual support expansion
- Advanced AI models integration
- Proactive insights and recommendations
- Integration with more external tools
- Enhanced collaboration features
- Augmented reality support
- Predictive assistance

---

*AMNA is part of the Luminar L&D Platform Suite*
