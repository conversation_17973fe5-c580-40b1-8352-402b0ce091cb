# Multi-stage Dockerfile for AMNA Frontend
# Based on Vite + React build pattern

# Stage 1: Dependencies
FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml* ./

# Install dependencies (support both npm and pnpm)
RUN if [ -f pnpm-lock.yaml ]; then \
      corepack enable && \
      pnpm install --frozen-lockfile; \
    else \
      npm ci; \
    fi

# Stage 2: Builder
FROM node:20-alpine AS builder
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy dependencies from previous stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
ENV VITE_TELEMETRY_DISABLED=1
RUN if [ -f pnpm-lock.yaml ]; then \
      corepack enable && \
      pnpm build; \
    else \
      npm run build; \
    fi

# Stage 3: Runner (Production)
FROM nginx:alpine AS runner
RUN apk add --no-cache wget

# Copy built assets to nginx
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf 2>/dev/null || echo "
server {
    listen 3001;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;
    
    # Handle client-side routing
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 \"healthy\n\";
        add_header Content-Type text/plain;
    }
    
    # API proxy (if needed)
    location /api {
        proxy_pass http://command-center:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Static assets caching
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
        expires 1y;
        add_header Cache-Control \"public, immutable\";
    }
}
" > /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3001/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]