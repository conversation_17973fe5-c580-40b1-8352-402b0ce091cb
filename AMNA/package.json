{"name": "amna-chat-app", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui"}, "dependencies": {"@luminar/shared-ui": "file:../shared-ui", "@luminar/shared-auth": "file:../shared-auth", "@radix-ui/react-slot": "^1.2.0", "@tailwindcss/postcss": "^4.1.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "idb": "^8.0.3", "lucide-react": "^0.488.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.28.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vite": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@faker-js/faker": "^9.9.0", "@mswjs/data": "^0.16.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "happy-dom": "^15.11.6", "msw": "^2.10.3", "postcss": "^8.5.3", "prettier": "^3.3.3", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8"}, "msw": {"workerDirectory": ["public"]}}