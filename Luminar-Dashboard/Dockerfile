# Multi-stage Dockerfile for Luminar Dashboard
# Based on TanStack Start framework with shared dependencies

# Stage 1: Dependencies
FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Stage 2: Builder
FROM node:20-alpine AS builder
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy dependencies from previous stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Copy shared dependencies if they exist
COPY ../shared-ui ./shared-ui 2>/dev/null || true
COPY ../shared-auth ./shared-auth 2>/dev/null || true

# Build the application
ENV NODE_ENV=production
RUN npm run build

# Stage 3: Runner (Production)
FROM node:20-alpine AS runner
RUN apk add --no-cache libc6-compat wget

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tanstack -u 1001

WORKDIR /app

# Copy necessary files
COPY --from=builder --chown=tanstack:nodejs /app/.output ./
COPY --from=builder --chown=tanstack:nodejs /app/package*.json ./

# Install only production dependencies
RUN npm ci --only=production

# Switch to non-root user
USER tanstack

# Expose port
EXPOSE 3004

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3004/health || exit 1

# Set environment
ENV NODE_ENV=production
ENV PORT=3004

# Start the application
CMD ["npm", "start"]