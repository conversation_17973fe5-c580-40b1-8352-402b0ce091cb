{"name": "command-center-developer-dashboard", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "node .output/server/index.mjs", "lint": "eslint .", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:run && npm run test:e2e"}, "dependencies": {"@luminar/shared-auth": "file:../shared-auth", "@luminar/shared-ui": "file:../shared-ui", "@tanstack/react-query": "^5.17.9", "@tanstack/react-query-devtools": "^5.81.5", "@tanstack/react-router": "alpha", "@tanstack/react-router-devtools": "^1.127.3", "@tanstack/react-start": "alpha", "axios": "^1.6.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.2.0", "lucide-react": "^0.309.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "recharts": "^2.10.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tanstack/router-cli": "alpha", "@tanstack/router-vite-plugin": "alpha", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@types/node": "^20.10.6", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "^6.18.0", "@typescript-eslint/parser": "^6.18.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.1.3", "@vitest/ui": "^1.1.3", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "jsdom": "^23.2.0", "msw": "^2.0.11", "postcss": "^8.4.33", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "vite": "^6.0.0-alpha.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.1.3"}}