import { useState, useEffect } from 'react';
import { createFileRoute } from '@tanstack/react-router';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  CheckCircle2, 
  AlertCircle, 
  XCircle,
  RefreshCw,
  Zap,
  Settings,
  Activity,
  TrendingUp,
  Users,
  Mail,
  Search,
  BookOpen,
  Trophy
} from 'lucide-react';

interface IntegrationStatus {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
  lastSync: string;
  dataPoints: number;
  enabled: boolean;
}

export const Route = createFileRoute('/integration')({
  component: IntegrationPage,
});

function IntegrationPage() {
  const [integrations, setIntegrations] = useState<IntegrationStatus[]>([]);
  const [overallStats, setOverallStats] = useState({
    totalIntegrations: 0,
    activeIntegrations: 0,
    totalDataPoints: 0,
    syncRate: 0,
    lastGlobalSync: new Date().toISOString()
  });
  const [isSyncing, setIsSyncing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');

  useEffect(() => {
    const initializeIntegrationMonitoring = async () => {
      try {
        const { default: monitoringService } = await import('../services/monitoring.service');
        
        // Set up event callbacks for integration updates
        monitoringService.setCallbacks({
          onIntegrationsUpdate: (integrationsData) => {
            console.log('Received integrations update:', integrationsData);
            
            // Map backend integration data to frontend format
            const mappedIntegrations: IntegrationStatus[] = integrationsData.integrations.map(integration => ({
              id: integration.id,
              name: integration.name,
              description: integration.description,
              icon: getIntegrationIcon(integration.id),
              status: integration.status === 'healthy' ? 'connected' : 
                     integration.status === 'degraded' ? 'syncing' :
                     integration.status === 'unhealthy' ? 'error' : 'disconnected',
              lastSync: formatLastSync(integration.lastCheck),
              dataPoints: Math.floor(Math.random() * 1000) + 100, // Mock data points for now
              enabled: integration.status !== 'unknown',
            }));
            
            setIntegrations(mappedIntegrations);
            setOverallStats({
              totalIntegrations: integrationsData.summary.total,
              activeIntegrations: integrationsData.summary.healthy + integrationsData.summary.degraded,
              totalDataPoints: mappedIntegrations.reduce((sum, int) => sum + int.dataPoints, 0),
              syncRate: integrationsData.summary.averageUptime,
              lastGlobalSync: integrationsData.summary.lastUpdate.toISOString(),
            });
            setLastUpdate(new Date(integrationsData.summary.lastUpdate));
            setIsLoading(false);
          },
          onIntegrationUpdated: (integration) => {
            // Update specific integration
            setIntegrations(prev => prev.map(int => 
              int.id === integration.id 
                ? {
                    ...int,
                    status: integration.status === 'healthy' ? 'connected' : 
                           integration.status === 'degraded' ? 'syncing' :
                           integration.status === 'unhealthy' ? 'error' : 'disconnected',
                    lastSync: formatLastSync(integration.lastCheck),
                    enabled: integration.status !== 'unknown',
                  }
                : int
            ));
          },
          onError: (error) => {
            console.error('Integration monitoring error:', error);
            setConnectionStatus('disconnected');
          },
        });

        // Subscribe to real-time updates
        monitoringService.subscribe('integrations', true);
        setConnectionStatus('connected');

        // Initial data fetch
        const integrationsData = await monitoringService.getIntegrations();
        if (integrationsData) {
          const mappedIntegrations: IntegrationStatus[] = integrationsData.integrations.map(integration => ({
            id: integration.id,
            name: integration.name,
            description: integration.description,
            icon: getIntegrationIcon(integration.id),
            status: integration.status === 'healthy' ? 'connected' : 
                   integration.status === 'degraded' ? 'syncing' :
                   integration.status === 'unhealthy' ? 'error' : 'disconnected',
            lastSync: formatLastSync(integration.lastCheck),
            dataPoints: Math.floor(Math.random() * 1000) + 100,
            enabled: integration.status !== 'unknown',
          }));
          
          setIntegrations(mappedIntegrations);
          setOverallStats({
            totalIntegrations: integrationsData.summary.total,
            activeIntegrations: integrationsData.summary.healthy + integrationsData.summary.degraded,
            totalDataPoints: mappedIntegrations.reduce((sum, int) => sum + int.dataPoints, 0),
            syncRate: integrationsData.summary.averageUptime,
            lastGlobalSync: integrationsData.summary.lastUpdate.toISOString(),
          });
          setLastUpdate(new Date(integrationsData.summary.lastUpdate));
          setIsLoading(false);
        }

      } catch (error) {
        console.error('Failed to initialize integration monitoring:', error);
        setConnectionStatus('disconnected');
        setIsLoading(false);
        
        // Fallback to empty state
        setIntegrations([]);
        setOverallStats({
          totalIntegrations: 0,
          activeIntegrations: 0,
          totalDataPoints: 0,
          syncRate: 0,
          lastGlobalSync: new Date().toISOString()
        });
      }
    };

    initializeIntegrationMonitoring();
  }, []);

  // Helper functions
  const getIntegrationIcon = (id: string) => {
    switch (id) {
      case 'econnect': return Mail;
      case 'lighthouse': return Search;
      case 'training-needs': return BookOpen;
      case 'vendors': return BookOpen;
      case 'wins-of-week': return Trophy;
      case 'amna': return Search;
      case 'luminar-dashboard': return Activity;
      default: return Search;
    }
  };

  const formatLastSync = (lastCheck: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - new Date(lastCheck).getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins === 1) return '1 minute ago';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours === 1) return '1 hour ago';
    if (diffHours < 24) return `${diffHours} hours ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };

  const handleGlobalSync = async () => {
    setIsSyncing(true);
    try {
      const { default: monitoringService } = await import('../services/monitoring.service');
      const result = await monitoringService.refreshAllIntegrations();
      
      if (result) {
        // Update integrations with fresh data
        const mappedIntegrations: IntegrationStatus[] = result.map(integration => ({
          id: integration.id,
          name: integration.name,
          description: integration.description,
          icon: getIntegrationIcon(integration.id),
          status: integration.status === 'healthy' ? 'connected' : 
                 integration.status === 'degraded' ? 'syncing' :
                 integration.status === 'unhealthy' ? 'error' : 'disconnected',
          lastSync: formatLastSync(integration.lastCheck),
          dataPoints: Math.floor(Math.random() * 1000) + 100,
          enabled: integration.status !== 'unknown',
        }));
        
        setIntegrations(mappedIntegrations);
        setOverallStats(prev => ({
          ...prev,
          lastGlobalSync: new Date().toISOString()
        }));
      }
    } catch (error) {
      console.error('Failed to refresh integrations:', error);
    }
    
    setIsSyncing(false);
  };

  const handleRefreshIntegration = async (integrationId: string) => {
    try {
      const { default: monitoringService } = await import('../services/monitoring.service');
      await monitoringService.refreshIntegration(integrationId);
    } catch (error) {
      console.error(`Failed to refresh integration ${integrationId}:`, error);
    }
  };

  const getStatusIcon = (status: IntegrationStatus['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'syncing':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: IntegrationStatus['status']) => {
    const statusStyles = {
      connected: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      disconnected: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
      error: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      syncing: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading integration status...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-6 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Integrations</p>
                <p className="text-2xl font-bold">{overallStats.totalIntegrations}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Zap className="h-6 w-6 text-primary" />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active</p>
                <p className="text-2xl font-bold text-green-600">{overallStats.activeIntegrations}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                <Activity className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Data Points</p>
                <p className="text-2xl font-bold">{overallStats.totalDataPoints.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Sync Rate</p>
                <p className="text-2xl font-bold">{overallStats.syncRate}%</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-100 dark:bg-purple-900/20 flex items-center justify-center">
                <RefreshCw className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Integration Status</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleGlobalSync}
              disabled={isSyncing}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
              {isSyncing ? 'Syncing...' : 'Sync All'}
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-border text-sm font-medium rounded-md text-foreground bg-background hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
              <Settings className="h-4 w-4 mr-2" />
              Configure
            </button>
          </div>
        </div>

        {/* Integration Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {integrations.map((integration) => (
            <div
              key={integration.id}
              className={`bg-card rounded-lg border ${
                integration.enabled ? 'border-border' : 'border-border/50 opacity-60'
              } p-6 transition-all duration-200 hover:shadow-lg`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className={`h-12 w-12 rounded-lg ${
                    integration.enabled ? 'bg-primary/10' : 'bg-muted'
                  } flex items-center justify-center`}>
                    <integration.icon className={`h-6 w-6 ${
                      integration.enabled ? 'text-primary' : 'text-muted-foreground'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold">{integration.name}</h3>
                      {getStatusBadge(integration.status)}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {integration.description}
                    </p>
                    <div className="flex items-center space-x-4 mt-3 text-sm">
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(integration.status)}
                        <span className="text-muted-foreground">
                          Last sync: {integration.lastSync}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          {integration.dataPoints.toLocaleString()} data points
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <button className="text-muted-foreground hover:text-foreground">
                  <Settings className="h-5 w-5" />
                </button>
              </div>

              {/* Progress Bar */}
              {integration.status === 'syncing' && (
                <div className="mt-4">
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full animate-pulse" style={{ width: '60%' }} />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* AMNA Intelligence Overview */}
        <div className="bg-card rounded-lg border border-border p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold">AMNA Intelligence</h3>
              <p className="text-sm text-muted-foreground mt-1">
                AI-powered cross-application insights and automation
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Intelligence Level:</span>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary">
                Standard
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="p-4 rounded-lg bg-secondary/50">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Context Quality</span>
                <span className="text-2xl font-bold text-green-600">85%</span>
              </div>
              <div className="mt-2 w-full bg-secondary rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }} />
              </div>
            </div>

            <div className="p-4 rounded-lg bg-secondary/50">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Response Accuracy</span>
                <span className="text-2xl font-bold text-blue-600">92%</span>
              </div>
              <div className="mt-2 w-full bg-secondary rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '92%' }} />
              </div>
            </div>

            <div className="p-4 rounded-lg bg-secondary/50">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Cross-App Insights</span>
                <span className="text-2xl font-bold text-purple-600">147</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">Generated this week</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}