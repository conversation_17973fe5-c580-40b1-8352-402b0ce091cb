
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  Activity, 
  CheckCircle2, 
  AlertCircle, 
  XCircle,
  Server,
  Database, 
  HardDrive, 
  Cpu, 
  MemoryStick, 
  Network, 
  GitBranch, 
  Clock 
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { createFileRoute } from '@tanstack/react-router';

interface SystemStatus {
  services: {
    total: number;
    running: number;
    stopped: number;
    error: number;
  };
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  docker: {
    containers: number;
    images: number;
    volumes: number;
    networks: number;
  };
  database: {
    connections: number;
    queries: number;
    size: string;
    uptime: string;
  };
}

// @ts-ignore
export const Route = createFileRoute('/')({
  component: DashboardPage,
});

function DashboardPage() {
  const [status, setStatus] = useState<SystemStatus>({
    services: { total: 0, running: 0, stopped: 0, error: 0 },
    resources: { cpu: 0, memory: 0, disk: 0, network: 0 },
    docker: { containers: 0, images: 0, volumes: 0, networks: 0 },
    database: { connections: 0, queries: 0, size: '0 B', uptime: '0m' }
  });

  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');

  useEffect(() => {
    // Import the monitoring service dynamically
    const initializeMonitoring = async () => {
      try {
        const { default: monitoringService } = await import('../services/monitoring.service');
        
        // Set up event callbacks
        monitoringService.setCallbacks({
          onMetricsUpdate: (metrics) => {
            console.log('Received metrics update:', metrics);
            setStatus({
              services: {
                total: metrics.services.total,
                running: metrics.services.running,
                stopped: metrics.services.stopped,
                error: metrics.services.error,
              },
              resources: {
                cpu: Math.round(metrics.cpu.usage),
                memory: Math.round(metrics.memory.percentage),
                disk: Math.round(metrics.disk.percentage),
                network: Math.round((metrics.network.totalBytesIn + metrics.network.totalBytesOut) / (1024 * 1024 * 1024) * 100), // Simplified network usage
              },
              docker: {
                containers: metrics.docker.containers.total,
                images: metrics.docker.images,
                volumes: metrics.docker.volumes,
                networks: metrics.docker.networks,
              },
              database: {
                connections: metrics.database.connections.active,
                queries: metrics.database.queries.total,
                size: formatBytes(metrics.database.size.total),
                uptime: formatUptime(metrics.database.uptime),
              },
            });
            setLastUpdate(new Date(metrics.timestamp));
            setIsLoading(false);
          },
          onError: (error) => {
            console.error('Monitoring service error:', error);
            setConnectionStatus('disconnected');
          },
        });

        // Subscribe to real-time updates
        monitoringService.subscribe('metrics', true);
        setConnectionStatus('connected');

        // Initial data fetch
        const metrics = await monitoringService.getSystemMetrics();
        if (metrics) {
          // Trigger the onMetricsUpdate callback manually for initial data
          monitoringService.setCallbacks({
            onMetricsUpdate: (metrics) => {
              setStatus({
                services: {
                  total: metrics.services.total,
                  running: metrics.services.running,
                  stopped: metrics.services.stopped,
                  error: metrics.services.error,
                },
                resources: {
                  cpu: Math.round(metrics.cpu.usage),
                  memory: Math.round(metrics.memory.percentage),
                  disk: Math.round(metrics.disk.percentage),
                  network: Math.round((metrics.network.totalBytesIn + metrics.network.totalBytesOut) / (1024 * 1024 * 1024) * 100),
                },
                docker: {
                  containers: metrics.docker.containers.total,
                  images: metrics.docker.images,
                  volumes: metrics.docker.volumes,
                  networks: metrics.docker.networks,
                },
                database: {
                  connections: metrics.database.connections.active,
                  queries: metrics.database.queries.total,
                  size: formatBytes(metrics.database.size.total),
                  uptime: formatUptime(metrics.database.uptime),
                },
              });
              setLastUpdate(new Date(metrics.timestamp));
              setIsLoading(false);
            },
          });
        }

      } catch (error) {
        console.error('Failed to initialize monitoring service:', error);
        setConnectionStatus('disconnected');
        setIsLoading(false);
        
        // Fallback to mock data if real service fails
        setStatus({
          services: { total: 0, running: 0, stopped: 0, error: 0 },
          resources: { cpu: 0, memory: 0, disk: 0, network: 0 },
          docker: { containers: 0, images: 0, volumes: 0, networks: 0 },
          database: { connections: 0, queries: 0, size: '0 B', uptime: '0m' }
        });
      }
    };

    initializeMonitoring();

    // Cleanup function
    return () => {
      // Monitoring service cleanup will be handled by the service itself
    };
  }, []);

  // Helper functions
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const statusCards = [
    {
      title: 'Services',
      icon: Server,
      stats: [
        { label: 'Running', value: status.services.running, color: 'text-green-500' },
        { label: 'Stopped', value: status.services.stopped, color: 'text-yellow-500' },
        { label: 'Error', value: status.services.error, color: 'text-red-500' }
      ]
    },
    {
      title: 'Docker',
      icon: Server,
      stats: [
        { label: 'Containers', value: status.docker.containers, color: 'text-blue-500' },
        { label: 'Images', value: status.docker.images, color: 'text-purple-500' },
        { label: 'Volumes', value: status.docker.volumes, color: 'text-indigo-500' }
      ]
    },
    {
      title: 'Database',
      icon: Database,
      stats: [
        { label: 'Connections', value: status.database.connections, color: 'text-green-500' },
        { label: 'Size', value: status.database.size, color: 'text-blue-500' },
        { label: 'Uptime', value: status.database.uptime, color: 'text-purple-500' }
      ]
    }
  ];

  const resourceMetrics = [
    { name: 'CPU Usage', icon: Cpu, value: status.resources.cpu, color: 'bg-blue-500' },
    { name: 'Memory Usage', icon: MemoryStick, value: status.resources.memory, color: 'bg-green-500' },
    { name: 'Disk Usage', icon: HardDrive, value: status.resources.disk, color: 'bg-yellow-500' },
    { name: 'Network I/O', icon: Network, value: status.resources.network, color: 'bg-purple-500' }
  ];

  const recentEvents = [
    { id: 1, type: 'success', message: 'Real-time monitoring connected successfully', time: 'Just now', icon: CheckCircle2 },
    { id: 2, type: 'info', message: 'System metrics collection started', time: '1 min ago', icon: GitBranch },
    { id: 3, type: 'success', message: 'All integrations health checked', time: '2 min ago', icon: CheckCircle2 },
    { id: 4, type: 'info', message: 'WebSocket connection established', time: '3 min ago', icon: GitBranch },
    { id: 5, type: 'success', message: 'Monitoring dashboard initialized', time: '5 min ago', icon: CheckCircle2 }
  ];

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading system metrics...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-6 border border-primary/20">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">Welcome to Command Center</h2>
              <p className="text-muted-foreground">
                Real-time monitoring of your entire infrastructure and integrations.
              </p>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <div className={`w-2 h-2 rounded-full ${
                  connectionStatus === 'connected' ? 'bg-green-500' : 
                  connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span>{connectionStatus}</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Last update: {lastUpdate.toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {statusCards.map((card) => (
            <div key={card.title} className="bg-card rounded-lg p-6 border border-border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">{card.title}</h3>
                <card.icon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-3">
                {card.stats.map((stat) => (
                  <div key={stat.label} className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{stat.label}</span>
                    <span className={`text-lg font-semibold ${stat.color}`}>{stat.value}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Resource Metrics */}
        <div className="bg-card rounded-lg p-6 border border-border">
          <h3 className="text-lg font-semibold mb-4">System Resources</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {resourceMetrics.map((metric) => (
              <div key={metric.name}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <metric.icon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{metric.name}</span>
                  </div>
                  <span className="text-sm font-semibold">{metric.value}%</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div
                    className={`${metric.color} h-2 rounded-full transition-all duration-500`}
                    style={{ width: `${metric.value}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Events */}
        <div className="bg-card rounded-lg p-6 border border-border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Recent Events</h3>
            <Activity className="h-5 w-5 text-muted-foreground" />
          </div>
          <div className="space-y-3">
            {recentEvents.map((event) => (
              <div key={event.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-accent/50 transition-colors">
                <event.icon className={`h-5 w-5 mt-0.5 ${
                  event.type === 'success' ? 'text-green-500' :
                  event.type === 'warning' ? 'text-yellow-500' :
                  event.type === 'error' ? 'text-red-500' :
                  'text-blue-500'
                }`} />
                <div className="flex-1">
                  <p className="text-sm">{event.message}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Clock className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">{event.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left">
            <Server className="h-8 w-8 text-primary mb-2" />
            <h4 className="font-medium">Restart Services</h4>
            <p className="text-sm text-muted-foreground">Quick service restart</p>
          </button>
          <button className="p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left">
            <Database className="h-8 w-8 text-primary mb-2" />
            <h4 className="font-medium">Backup Database</h4>
            <p className="text-sm text-muted-foreground">Create backup now</p>
          </button>
          <button className="p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left">
            <GitBranch className="h-8 w-8 text-primary mb-2" />
            <h4 className="font-medium">Deploy Update</h4>
            <p className="text-sm text-muted-foreground">Deploy latest changes</p>
          </button>
          <button className="p-4 bg-card border border-border rounded-lg hover:bg-accent transition-colors text-left">
            <Activity className="h-8 w-8 text-primary mb-2" />
            <h4 className="font-medium">View Logs</h4>
            <p className="text-sm text-muted-foreground">Real-time log viewer</p>
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
}