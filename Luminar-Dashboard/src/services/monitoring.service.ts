import { io, Socket } from 'socket.io-client';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';
const WS_URL = `${API_BASE_URL}/monitoring`;

// Types from the backend
export interface SystemMetrics {
  timestamp: Date;
  cpu: CPUMetrics;
  memory: MemoryMetrics;
  disk: DiskMetrics;
  network: NetworkMetrics;
  services: ServiceMetrics;
  docker: DockerMetrics;
  database: DatabaseMetrics;
}

export interface CPUMetrics {
  usage: number;
  cores: number;
  load: number[];
  temperature?: number;
}

export interface MemoryMetrics {
  total: number;
  used: number;
  available: number;
  percentage: number;
  swap?: {
    total: number;
    used: number;
  };
}

export interface DiskMetrics {
  total: number;
  used: number;
  available: number;
  percentage: number;
  partitions: DiskPartition[];
}

export interface DiskPartition {
  device: string;
  mountpoint: string;
  total: number;
  used: number;
  available: number;
  percentage: number;
}

export interface NetworkMetrics {
  interfaces: NetworkInterface[];
  totalBytesIn: number;
  totalBytesOut: number;
  packetsIn: number;
  packetsOut: number;
}

export interface NetworkInterface {
  name: string;
  bytesIn: number;
  bytesOut: number;
  packetsIn: number;
  packetsOut: number;
  speed?: number;
  isUp: boolean;
}

export interface ServiceMetrics {
  total: number;
  running: number;
  stopped: number;
  error: number;
  services: ServiceStatus[];
}

export interface ServiceStatus {
  name: string;
  status: 'running' | 'stopped' | 'error' | 'starting' | 'stopping';
  pid?: number;
  uptime?: number;
  memory?: number;
  cpu?: number;
}

export interface DockerMetrics {
  containers: ContainerMetrics;
  images: number;
  volumes: number;
  networks: number;
  containerList: DockerContainer[];
}

export interface ContainerMetrics {
  total: number;
  running: number;
  stopped: number;
  paused: number;
}

export interface DockerContainer {
  id: string;
  name: string;
  image: string;
  status: string;
  state: 'running' | 'stopped' | 'paused' | 'restarting' | 'removing' | 'exited';
  created: Date;
  ports: string[];
  uptime?: string;
  health?: 'healthy' | 'unhealthy' | 'starting' | 'none';
  stats?: {
    cpu: number;
    memory: {
      usage: number;
      limit: number;
      percentage: number;
    };
    network: {
      rx: number;
      tx: number;
    };
  };
}

export interface DatabaseMetrics {
  connections: {
    active: number;
    idle: number;
    total: number;
    max: number;
  };
  queries: {
    total: number;
    slow: number;
    failed: number;
    averageTime: number;
  };
  size: {
    total: number;
    data: number;
    index: number;
  };
  uptime: number;
  performance: {
    cacheHitRatio: number;
    replicationLag?: number;
    locksWaiting: number;
  };
}

export interface IntegrationHealth {
  id: string;
  name: string;
  description: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  responseTime: number;
  uptime: number;
  endpoint: string;
  version?: string;
  dependencies?: IntegrationDependency[];
  healthChecks: HealthCheck[];
}

export interface IntegrationDependency {
  name: string;
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
}

export interface HealthCheck {
  name: string;
  status: 'pass' | 'fail' | 'warn';
  responseTime: number;
  message?: string;
  details?: any;
}

export interface SystemEvent {
  id: string;
  timestamp: Date;
  type: 'info' | 'warning' | 'error' | 'success';
  category: 'system' | 'docker' | 'database' | 'integration' | 'service';
  title: string;
  message: string;
  source: string;
  metadata?: any;
  acknowledged?: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

export interface IntegrationSummary {
  summary: {
    total: number;
    healthy: number;
    degraded: number;
    unhealthy: number;
    unknown: number;
    averageResponseTime: number;
    averageUptime: number;
    lastUpdate: Date;
  };
  integrations: IntegrationHealth[];
}

export interface DashboardSummary {
  system: {
    cpu: number;
    memory: number;
    disk: number;
    services: {
      total: number;
      running: number;
      issues: number;
    };
    docker: {
      total: number;
      running: number;
      issues: number;
    };
  };
  integrations: {
    total: number;
    healthy: number;
    issues: number;
    averageUptime: number;
  };
  events: {
    total: number;
    last24Hours: {
      total: number;
      error: number;
      warning: number;
      success: number;
      info: number;
    };
    lastHour: {
      total: number;
      error: number;
      warning: number;
      success: number;
      info: number;
    };
    unacknowledged: number;
  };
  timestamp: Date;
}

// API Response wrapper
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

// Event callbacks
export interface MonitoringEventCallbacks {
  onMetricsUpdate?: (metrics: SystemMetrics) => void;
  onIntegrationsUpdate?: (integrations: IntegrationSummary) => void;
  onNewEvent?: (event: SystemEvent) => void;
  onEventAcknowledged?: (data: { eventId: string }) => void;
  onIntegrationUpdated?: (integration: IntegrationHealth) => void;
  onDockerUpdate?: (docker: DockerMetrics) => void;
  onDatabaseUpdate?: (database: DatabaseMetrics) => void;
  onError?: (error: any) => void;
}

class MonitoringService {
  private socket: Socket | null = null;
  private callbacks: MonitoringEventCallbacks = {};
  private isConnected = false;

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    try {
      this.socket = io(WS_URL, {
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      this.socket.on('connect', () => {
        console.log('Connected to monitoring WebSocket');
        this.isConnected = true;
      });

      this.socket.on('disconnect', () => {
        console.log('Disconnected from monitoring WebSocket');
        this.isConnected = false;
      });

      this.socket.on('initialData', (data: {
        metrics: SystemMetrics;
        integrations: IntegrationSummary;
        events: SystemEvent[];
        timestamp: Date;
      }) => {
        console.log('Received initial data:', data);
        this.callbacks.onMetricsUpdate?.(data.metrics);
        this.callbacks.onIntegrationsUpdate?.(data.integrations);
      });

      this.socket.on('metricsUpdate', (metrics: SystemMetrics) => {
        this.callbacks.onMetricsUpdate?.(metrics);
      });

      this.socket.on('integrationsUpdate', (integrations: IntegrationSummary) => {
        this.callbacks.onIntegrationsUpdate?.(integrations);
      });

      this.socket.on('newEvent', (event: SystemEvent) => {
        this.callbacks.onNewEvent?.(event);
      });

      this.socket.on('eventAcknowledged', (data: { eventId: string }) => {
        this.callbacks.onEventAcknowledged?.(data);
      });

      this.socket.on('integrationUpdated', (integration: IntegrationHealth) => {
        this.callbacks.onIntegrationUpdated?.(integration);
      });

      this.socket.on('dockerUpdate', (docker: DockerMetrics) => {
        this.callbacks.onDockerUpdate?.(docker);
      });

      this.socket.on('databaseUpdate', (database: DatabaseMetrics) => {
        this.callbacks.onDatabaseUpdate?.(database);
      });

      this.socket.on('error', (error: any) => {
        console.error('WebSocket error:', error);
        this.callbacks.onError?.(error);
      });

    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
    }
  }

  // Subscribe to real-time updates
  subscribe(type: 'metrics' | 'events' | 'integrations' | 'docker' | 'database', enabled: boolean = true) {
    if (this.socket && this.isConnected) {
      this.socket.emit('subscribe', { type, enabled });
    }
  }

  // Set event callbacks
  setCallbacks(callbacks: MonitoringEventCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // API Methods
  async getSystemMetrics(useCache: boolean = true): Promise<SystemMetrics | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/metrics?useCache=${useCache}`);
      const result: ApiResponse<SystemMetrics> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      }
      
      console.error('Failed to get system metrics:', result.error);
      return null;
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      return null;
    }
  }

  async getDashboardSummary(): Promise<DashboardSummary | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/dashboard`);
      const result: ApiResponse<DashboardSummary> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      }
      
      console.error('Failed to get dashboard summary:', result.error);
      return null;
    } catch (error) {
      console.error('Error fetching dashboard summary:', error);
      return null;
    }
  }

  async getIntegrations(): Promise<IntegrationSummary | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/integrations`);
      const result: ApiResponse<IntegrationSummary> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      }
      
      console.error('Failed to get integrations:', result.error);
      return null;
    } catch (error) {
      console.error('Error fetching integrations:', error);
      return null;
    }
  }

  async getIntegrationHealth(id: string): Promise<IntegrationHealth | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/integrations/${id}`);
      const result: ApiResponse<IntegrationHealth> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      }
      
      console.error(`Failed to get integration health for ${id}:`, result.error);
      return null;
    } catch (error) {
      console.error(`Error fetching integration health for ${id}:`, error);
      return null;
    }
  }

  async refreshIntegration(id: string): Promise<IntegrationHealth | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/integrations/${id}/refresh`, {
        method: 'POST',
      });
      const result: ApiResponse<IntegrationHealth> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      }
      
      console.error(`Failed to refresh integration ${id}:`, result.error);
      return null;
    } catch (error) {
      console.error(`Error refreshing integration ${id}:`, error);
      return null;
    }
  }

  async refreshAllIntegrations(): Promise<IntegrationHealth[] | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/integrations/refresh`, {
        method: 'POST',
      });
      const result: ApiResponse<IntegrationHealth[]> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      }
      
      console.error('Failed to refresh all integrations:', result.error);
      return null;
    } catch (error) {
      console.error('Error refreshing all integrations:', error);
      return null;
    }
  }

  async getEvents(options: {
    limit?: number;
    type?: string;
    category?: string;
    since?: string;
    acknowledged?: boolean;
  } = {}): Promise<{ events: SystemEvent[]; summary: any } | null> {
    try {
      const params = new URLSearchParams();
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });

      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/events?${params.toString()}`);
      const result: ApiResponse<{ events: SystemEvent[]; summary: any }> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      }
      
      console.error('Failed to get events:', result.error);
      return null;
    } catch (error) {
      console.error('Error fetching events:', error);
      return null;
    }
  }

  async acknowledgeEvent(eventId: string, acknowledgedBy: string): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/events/${eventId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ acknowledgedBy }),
      });
      
      const result = await response.json();
      return result.success || false;
    } catch (error) {
      console.error('Error acknowledging event:', error);
      return false;
    }
  }

  async triggerTestEvent(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/test/event`, {
        method: 'POST',
      });
      
      const result = await response.json();
      return result.success || false;
    } catch (error) {
      console.error('Error triggering test event:', error);
      return false;
    }
  }

  async getHealthStatus(): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/system-monitoring/health`);
      return await response.json();
    } catch (error) {
      console.error('Error fetching health status:', error);
      return { status: 'unhealthy', error: error.message };
    }
  }

  // WebSocket methods
  requestMetrics() {
    if (this.socket && this.isConnected) {
      this.socket.emit('getMetrics');
    }
  }

  requestIntegrations() {
    if (this.socket && this.isConnected) {
      this.socket.emit('getIntegrations');
    }
  }

  requestEvents(options: any = {}) {
    if (this.socket && this.isConnected) {
      this.socket.emit('getEvents', options);
    }
  }

  refreshIntegrationViaWS(integrationId: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('refreshIntegration', { integrationId });
    }
  }

  acknowledgeEventViaWS(eventId: string, acknowledgedBy: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('acknowledgeEvent', { eventId, acknowledgedBy });
    }
  }

  // Utility methods
  isWebSocketConnected(): boolean {
    return this.isConnected;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }

  reconnect() {
    if (this.socket) {
      this.socket.connect();
    }
  }

  // Format helpers
  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  static getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'pass':
      case 'success':
        return 'text-green-500';
      case 'degraded':
      case 'warning':
      case 'warn':
        return 'text-yellow-500';
      case 'unhealthy':
      case 'error':
      case 'fail':
      case 'stopped':
        return 'text-red-500';
      case 'unknown':
      case 'paused':
        return 'text-gray-500';
      default:
        return 'text-blue-500';
    }
  }
}

// Export singleton instance
export const monitoringService = new MonitoringService();
export default monitoringService;