
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { ThemeProvider, IntegrationProvider, AMNAWidget } from '@luminar/shared-ui';

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
            refetchOnWindowFocus: false,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <IntegrationProvider>
          <AuthGuard>
            {children}
          </AuthGuard>
          
          {/* AMNAWidget for cross-app AI assistance */}
          <AMNAWidget
            position="bottom-left"
            context={{ 
              app: 'luminar-dashboard',
              page: typeof window !== 'undefined' ? window.location.pathname : '/',
              environment: 'production'
            }}
            showBadge={true}
          />
        </IntegrationProvider>
      </ThemeProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}